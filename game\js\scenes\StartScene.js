/**
 * 开始场景 - 负责资源加载
 */
class StartScene extends Phaser.Scene {
    constructor() {
        super({ key: 'StartScene' });
        this.assetsLoaded = false;
        this.uiManager = null;
    }

    preload() {
        // 预加载基础资源
        console.log('StartScene: 开始预加载基础资源');

        // 加载工具资源
        this.load.image('axe', 'assets/tools/axe.png');
    }

    create() {
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;

        // 创建UI管理器
        this.uiManager = new UIManager(this);

        // 添加纯色背景
        this.add.rectangle(
            width / 2,
            height / 2,
            width,
            height,
            0xFF8C42  // 使用温暖橙色背景
        );

        // 创建加载文本
        this.uiManager.createComponent('text', 'loadingText', {
            x: width / 2,
            y: height / 2 - 50,
            text: '正在加载游戏资源...',
            font: 'bold 24px Arial',
            color: '#ffffff',
            originX: 0.5,
            originY: 0.5
        });

        // 创建进度条
        this.uiManager.createComponent('progressbar', 'progressBar', {
            x: width / 2,
            y: height / 2,
            width: 400,
            height: 30,
            value: 0,
            maxValue: 100,
            backgroundColor: 0x333333,
            fillColor: 0x00ff00,
            showText: false
        });

        // 创建进度文本
        this.uiManager.createComponent('text', 'progressText', {
            x: width / 2,
            y: height / 2 + 50,
            text: '0%',
            font: '20px Arial',
            color: '#ffffff',
            originX: 0.5,
            originY: 0.5
        });

        // 创建开始游戏按钮（初始隐藏）
        this.uiManager.createComponent('button', 'startButton', {
            x: width / 2,
            y: height / 2,
            width: 200,
            height: 60,
            text: '开始游戏',
            visible: false,
            onClick: () => {
                this.scene.start('MainMenuScene');
            }
        });

        // 初始化游戏数据系统
        this.initializeGameSystems();

        // 开始加载资源
        this.loadAllAssets();
    }

    /**
     * 初始化游戏系统
     */
    initializeGameSystems() {
        try {
            // 初始化游戏数据系统
            if (window.initializeGameData) {
                const success = window.initializeGameData();
                if (!success) {
                    console.warn('游戏数据系统初始化失败，游戏将以基础模式运行');
                }
            } else {
                console.warn('游戏数据初始化函数不可用');
            }
        } catch (error) {
            console.error('初始化游戏系统时出错:', error);
        }
    }

    /**
     * 加载所有游戏资源
     */
    loadAllAssets() {
        // 加载基础图片资源
        this.load.image('background', 'assets/images/floor/floor_plain.png');
        this.load.image('home', 'assets/images/floor/home.png');
        this.load.image('player_boy_1', 'assets/images/player_boy/boy_01.png');
        this.load.image('player_boy_2', 'assets/images/player_boy/boy_02.png');
        this.load.image('player_boy_3', 'assets/images/player_boy/boy_03.png');
        this.load.image('player_boy_4', 'assets/images/player_boy/boy_04.png');

        // 加载UI资源
        this.load.image('card', 'assets/ui/choose/card.png');
        this.load.image('exp', 'assets/ui/exp.png');
        this.load.image('expin', 'assets/ui/expin.png');
        this.load.image('ui_heart_full', 'assets/other/ui_heart_full.png');

        // 加载武器图标
        this.load.image('a', 'assets/Icons/a.png');
        this.load.image('b', 'assets/Icons/b.png');
        this.load.image('c', 'assets/Icons/c.png');
        this.load.image('d', 'assets/Icons/d.png');

        // 加载属性图标
        this.load.image('spd', 'assets/Icons/spd.png');
        this.load.image('big', 'assets/Icons/big.png');
        this.load.image('atk', 'assets/Icons/atk.png');
        this.load.image('atkspd', 'assets/Icons/atkspd.png');
        this.load.image('aoe', 'assets/Icons/aoe.png');

        // 加载武器效果资源
        this.loadWeaponEffects();

        // 加载宠物资源
        this.loadPetAssets();

        // 加载敌人资源（用于蝙蝠群）
        this.loadEnemyAssets();

        // 添加资源加载错误处理
        this.load.on('loaderror', (fileObj) => {
            console.error(`资源加载失败: ${fileObj.key} (${fileObj.url})`);
        });

        // 监听加载进度
        this.load.on('progress', (value) => {
            // 更新进度条
            const progressBar = this.uiManager.getComponent('progressBar');
            if (progressBar) {
                progressBar.setValue(value * 100, false);
            }

            // 更新进度文本
            const progressText = this.uiManager.getComponent('progressText');
            if (progressText) {
                progressText.setText(`${Math.floor(value * 100)}%`);
            }
        });

        // 监听加载完成
        this.load.on('complete', () => {
            console.log('所有资源加载完成');

            // 检查关键武器资源是否成功加载
            const checkWeapons = ['a', 'b', 'c', 'd'];
            checkWeapons.forEach(weapon => {
                const firstFrameKey = `${weapon}_effect_${weapon === 'd' ? '01' : '001'}`;
                if (this.textures.exists(firstFrameKey)) {
                    console.log(`武器 ${weapon} 资源加载成功`);
                } else {
                    console.warn(`武器 ${weapon} 资源似乎未成功加载`);
                }
            });

            // 检查心形血量道具纹理是否加载成功
            if (this.textures.exists('ui_heart_full')) {
                console.log('心形血量道具纹理加载成功');
            } else {
                console.warn('心形血量道具纹理加载失败');
            }

            // 创建宠物动画
            this.createPetAnimations();

            // 创建敌人动画
            this.createEnemyAnimations();

            this.assetsLoaded = true;

            // 隐藏加载UI
            this.uiManager.getComponent('loadingText')?.setVisible(false);
            this.uiManager.getComponent('progressBar')?.setVisible(false);
            this.uiManager.getComponent('progressText')?.setVisible(false);

            // 显示游戏介绍弹窗
            this.showGameIntro();
        });

        // 开始加载
        this.load.start();
    }

    /**
     * 加载武器效果资源
     */
    loadWeaponEffects() {
        // 武器A
        console.log('开始加载武器A效果资源');
        try {
            for (let i = 1; i <= 5; i++) {
                const numStr = i.toString().padStart(3, '0');
                const key = `a_effect_${numStr}`;
                const path = `assets/fx/a/a_${numStr}.png`;
                this.load.image(key, path);
            }
        } catch (error) {
            console.error('武器A效果加载失败:', error);
        }

        // 武器B
        console.log('开始加载武器B效果资源');
        try {
            for (let i = 1; i <= 4; i++) {
                const numStr = i.toString().padStart(3, '0');
                const key = `b_effect_${numStr}`;
                const path = `assets/fx/b/${numStr}.png`;
                this.load.image(key, path);
            }
        } catch (error) {
            console.error('武器B效果加载失败:', error);
        }

        // 武器C
        console.log('开始加载武器C效果资源');
        try {
            for (let i = 1; i <= 7; i++) {
                const numStr = i.toString().padStart(3, '0');
                const key = `c_effect_${numStr}`;
                const path = `assets/fx/c/${numStr}.png`;
                this.load.image(key, path);
            }
        } catch (error) {
            console.error('武器C效果加载失败:', error);
        }

        // 武器D
        console.log('开始加载武器D效果资源');
        try {
            for (let i = 1; i <= 20; i++) {
                const numStr = i.toString().padStart(2, '0');
                const key = `d_effect_${numStr}`;
                const path = `assets/fx/d/Gravity-Sheet_${numStr}.png`;
                this.load.image(key, path);
            }
        } catch (error) {
            console.error('武器D效果加载失败:', error);
        }
    }

    /**
     * 加载宠物资源
     */
    loadPetAssets() {
        console.log('开始加载宠物资源');

        // 宠物001 - 猫咪宠物（精灵表）
        this.load.spritesheet('pet001_sprite', 'assets/pets/character01/JumpCattttt.png', {
            frameWidth: 32,
            frameHeight: 32
        });
        console.log('加载精灵表: pet001_sprite');

        // 宠物002 - 蘑菇宠物（多帧图片）
        for (let i = 1; i <= 8; i++) {
            const key = `pet_pet002_${i}`;
            const path = `assets/pets/character02/slm_0${i}.png`;
            this.load.image(key, path);
            console.log(`加载多帧图片: ${key}, 路径: ${path}`);
        }

        // 宠物003 - 哈比人宠物（多帧图片）
        for (let i = 1; i <= 10; i++) {
            const key = `pet_pet003_${i}`;
            const path = `assets/pets/character03/Hobbit - run${i}.png`;
            this.load.image(key, path);
            console.log(`加载多帧图片: ${key}, 路径: ${path}`);
        }

        // 宠物004 - Godot宠物（多帧图片）
        for (let i = 1; i <= 4; i++) {
            const key = `pet_pet004_${i}`;
            const path = `assets/pets/character04/big_f${i}.png`;
            this.load.image(key, path);
            console.log(`加载多帧图片: ${key}, 路径: ${path}`);
        }

        // 宠物005 - 蘑菇奔跑宠物（多帧图片）
        for (let i = 1; i <= 8; i++) {
            const key = `pet_pet005_${i}`;
            const path = `assets/pets/character05/D-_Users_tur_Desktop_Mushroom-Run_0${i}.png`;
            this.load.image(key, path);
            console.log(`加载多帧图片: ${key}, 路径: ${path}`);
        }
    }

    /**
     * 创建宠物动画
     */
    createPetAnimations() {
        // 宠物001 - 猫咪宠物（精灵表）
        if (!this.anims.exists('pet001_anim')) {
            this.anims.create({
                key: 'pet001_anim',
                frames: this.anims.generateFrameNumbers('pet001_sprite', {
                    start: 0,
                    end: 12 // 13帧动画
                }),
                frameRate: 10,
                repeat: -1
            });
            console.log('创建宠物动画: pet001_anim, 使用精灵表');
        }

        // 宠物002 - 蘑菇宠物（多帧图片）
        if (!this.anims.exists('pet002_anim')) {
            this.anims.create({
                key: 'pet002_anim',
                frames: Array.from({ length: 8 }, (_, i) => ({
                    key: `pet_pet002_${i + 1}`
                })),
                frameRate: 10,
                repeat: -1
            });
            console.log('创建宠物动画: pet002_anim, 帧数: 8');
        }

        // 宠物003 - 哈比人宠物（多帧图片）
        if (!this.anims.exists('pet_pet003_anim')) {
            this.anims.create({
                key: 'pet_pet003_anim',
                frames: Array.from({ length: 10 }, (_, i) => ({
                    key: `pet_pet003_${i + 1}`
                })),
                frameRate: 10,
                repeat: -1
            });
            console.log('创建宠物动画: pet_pet003_anim, 帧数: 10');
        }

        // 宠物004 - Godot宠物（多帧图片）
        if (!this.anims.exists('pet_pet004_anim')) {
            this.anims.create({
                key: 'pet_pet004_anim',
                frames: Array.from({ length: 4 }, (_, i) => ({
                    key: `pet_pet004_${i + 1}`
                })),
                frameRate: 10,
                repeat: -1
            });
            console.log('创建宠物动画: pet_pet004_anim, 帧数: 4');
        }

        // 宠物005 - 蘑菇奔跑宠物（多帧图片）
        if (!this.anims.exists('pet005_anim')) {
            this.anims.create({
                key: 'pet005_anim',
                frames: Array.from({ length: 8 }, (_, i) => ({
                    key: `pet_pet005_${i + 1}`
                })),
                frameRate: 10,
                repeat: -1
            });
            console.log('创建宠物动画: pet005_anim, 帧数: 8');
        }
    }

    /**
     * 加载敌人资源（用于蝙蝠群）
     */
    loadEnemyAssets() {
        console.log('开始加载敌人资源');

        // 加载E000a纹理（4帧）
        for (let i = 1; i <= 4; i++) {
            const key = `e000_${i}`;
            const path = `assets/images/000/0000_0${i}.png`;
            this.load.image(key, path);
            console.log(`加载E000a纹理: ${key}, 路径: ${path}`);
        }

        // 加载E000b纹理（4帧）
        for (let i = 1; i <= 4; i++) {
            const key = `e000b_${i}`;
            const path = `assets/images/000/000_0${i}.png`;
            this.load.image(key, path);
            console.log(`加载E000b纹理: ${key}, 路径: ${path}`);
        }
    }

    /**
     * 创建敌人动画（用于蝙蝠群）
     */
    createEnemyAnimations() {
        console.log('开始创建敌人动画');

        // 创建E000a动画
        if (!this.anims.exists('e000_anim')) {
            this.anims.create({
                key: 'e000_anim',
                frames: [
                    { key: 'e000_1' },
                    { key: 'e000_2' },
                    { key: 'e000_3' },
                    { key: 'e000_4' }
                ],
                frameRate: 10,
                repeat: -1
            });
            console.log('创建敌人动画: e000_anim');
        }

        // 创建E000b动画
        if (!this.anims.exists('e000b_anim')) {
            this.anims.create({
                key: 'e000b_anim',
                frames: [
                    { key: 'e000b_1' },
                    { key: 'e000b_2' },
                    { key: 'e000b_3' },
                    { key: 'e000b_4' }
                ],
                frameRate: 10,
                repeat: -1
            });
            console.log('创建敌人动画: e000b_anim');
        }
    }

    /**
     * 显示游戏介绍弹窗
     */
    showGameIntro() {
        // 游戏介绍文本
        const introText = `
#**以下内容经由AI涉密抽象过滤，具象内容请发起QA**

## 核心架构
基于[跨维度生成协同引擎](CDGE)构建神经网络工作流架构，实现任务节点的动态拓扑映射与自动化编排。工作流包含三大核心技术模块：

### 智能节点集群
- **语义指令编译系统**：实时编译自然语言需求为结构化指令流
- **多模态生成中枢**：通过LLM节点通过智能节点集群并行输出多维度开发要素
- **自动化优化引擎**：实现关键数字资产的智能生成与参数调优，减少人工干预环节80%
  -- 新增策划系统高维控制层：
  -- 数值平衡：通过动态均衡引擎实现参数泛化调节
  -- 玩法拓扑：采用决策树概率坍缩模型构建非线性内容架构

### 2review节点实现人工审核入口，可在AI输出的基础上对关键数据
### 3编程1/6（编程任务实现自动化，减少人工编程时间约80%）
### 4put中自动检测代码内外，减少调试时间约50%

### 5频率为临时占位符
### 6专利信息占位符

## 制作者声明
- 本内容为技术切片，美术/音频均为临时占位符
- 禁止任何商业化使用，核心专利覆盖CDGE架构及动态拓扑工作流算法
- 效能数据基于同等复杂度任务对比传统UEUN等工作流测得`;

        // 创建全屏模态弹窗
        this.uiManager.createComponent('modal', 'gameIntro', {
            title: 'AIgame制作流实验切片',
            content: introText,
            depth: 1000,
            onClose: () => {
                // 显示开始按钮
                this.uiManager.getComponent('startButton')?.setVisible(true);
            }
        });
    }

    /**
     * 场景销毁时清理UI
     */
    destroy() {
        if (this.uiManager) {
            this.uiManager.destroyAll();
            this.uiManager = null;
        }
        super.destroy();
    }
}