/**
 * UI进度条组件
 * 统一的进度条组件，支持主题和动画效果
 */
class UIProgressBar extends UIComponent {
    constructor(scene, config = {}) {
        super(scene, config);

        // 进度条特有属性
        this.value = config.value || 0;
        this.maxValue = config.maxValue || 100;
        this.minValue = config.minValue || 0;

        // 样式属性
        this.backgroundColor = config.backgroundColor || 0x666666;
        this.fillColor = config.fillColor || 0x00ff00;
        this.borderColor = config.borderColor || 0x333333;
        this.borderWidth = config.borderWidth || 1;

        // 显示选项
        this.showText = config.showText !== false;
        this.textFormat = config.textFormat || '{value}/{max}';
        this.textColor = config.textColor || '#ffffff';
        this.textFont = config.textFont || '14px Arial';

        // 动画选项
        this.animationDuration = config.animationDuration || 300;
        this.useAnimation = config.useAnimation !== false;

        // Phaser对象
        this.background = null;
        this.border = null;
        this.fillBar = null;
        this.textObject = null;

        // 当前显示的值（用于动画）
        this.displayValue = this.value;

        this.create();
    }

    /**
     * 创建进度条
     */
    create() {
        // 创建边框
        if (this.borderWidth > 0) {
            this.border = this.scene.add.rectangle(
                this.x,
                this.y,
                this.width + this.borderWidth * 2,
                this.height + this.borderWidth * 2,
                this.borderColor
            );
            this.addGameObject(this.border);
        }

        // 创建背景
        this.background = this.scene.add.rectangle(
            this.x,
            this.y,
            this.width,
            this.height,
            this.backgroundColor
        );
        this.addGameObject(this.background);

        // 创建填充条
        this.fillBar = this.scene.add.rectangle(
            this.x - this.width / 2,
            this.y,
            0,
            this.height,
            this.fillColor
        ).setOrigin(0, 0.5);
        this.addGameObject(this.fillBar);

        // 创建文本
        if (this.showText) {
            this.textObject = this.scene.add.text(
                this.x,
                this.y,
                this.getFormattedText(),
                {
                    font: this.textFont,
                    fill: this.textColor
                }
            ).setOrigin(0.5);
            this.addGameObject(this.textObject);
        }

        // 初始化显示
        this.updateDisplay();

        console.log(`进度条创建完成: ${this.value}/${this.maxValue}`);
    }

    /**
     * 设置进度值
     * @param {number} value - 新的进度值
     * @param {boolean} animate - 是否使用动画
     */
    setValue(value, animate = this.useAnimation) {
        // 限制值在有效范围内
        const newValue = Math.max(this.minValue, Math.min(this.maxValue, value));

        if (newValue === this.value) return this;

        const oldValue = this.value;
        this.value = newValue;

        if (animate && this.scene) {
            this.animateToValue(oldValue, newValue);
        } else {
            this.displayValue = newValue;
            this.updateDisplay();
        }

        // 触发值变化事件
        this.emit('progressbar:change', {
            oldValue,
            newValue,
            progress: this.getProgress()
        });

        return this;
    }

    /**
     * 获取当前进度值
     * @returns {number} 当前进度值
     */
    getValue() {
        return this.value;
    }

    /**
     * 获取进度百分比
     * @returns {number} 进度百分比 (0-1)
     */
    getProgress() {
        const range = this.maxValue - this.minValue;
        return range > 0 ? (this.value - this.minValue) / range : 0;
    }

    /**
     * 设置最大值
     * @param {number} maxValue - 新的最大值
     */
    setMaxValue(maxValue) {
        this.maxValue = maxValue;
        this.updateDisplay();
        return this;
    }

    /**
     * 设置最小值
     * @param {number} minValue - 新的最小值
     */
    setMinValue(minValue) {
        this.minValue = minValue;
        this.updateDisplay();
        return this;
    }

    /**
     * 增加进度值
     * @param {number} amount - 增加的数量
     * @param {boolean} animate - 是否使用动画
     */
    addValue(amount, animate = this.useAnimation) {
        return this.setValue(this.value + amount, animate);
    }

    /**
     * 减少进度值
     * @param {number} amount - 减少的数量
     * @param {boolean} animate - 是否使用动画
     */
    subtractValue(amount, animate = this.useAnimation) {
        return this.setValue(this.value - amount, animate);
    }

    /**
     * 动画到指定值
     * @param {number} fromValue - 起始值
     * @param {number} toValue - 目标值
     */
    animateToValue(fromValue, toValue) {
        if (!this.scene) return;

        // 设置起始值
        this.displayValue = fromValue;

        this.scene.tweens.add({
            targets: this,
            displayValue: toValue,
            duration: this.animationDuration,
            ease: 'Power2',
            onUpdate: () => {
                this.updateDisplay();
            },
            onComplete: () => {
                this.emit('progressbar:animationComplete', {
                    value: toValue,
                    progress: this.getProgress()
                });
            }
        });
    }

    /**
     * 更新显示
     */
    updateDisplay() {
        if (!this.fillBar) return;

        // 计算填充宽度
        const progress = this.getDisplayProgress();
        const fillWidth = this.width * progress;

        // 更新填充条
        this.fillBar.displayWidth = fillWidth;

        // 更新文本
        if (this.textObject) {
            this.textObject.setText(this.getFormattedText());
        }
    }

    /**
     * 获取显示进度百分比
     * @returns {number} 显示进度百分比 (0-1)
     */
    getDisplayProgress() {
        const range = this.maxValue - this.minValue;
        return range > 0 ? (this.displayValue - this.minValue) / range : 0;
    }

    /**
     * 获取格式化的文本
     * @returns {string} 格式化后的文本
     */
    getFormattedText() {
        return this.textFormat
            .replace('{value}', Math.round(this.displayValue))
            .replace('{max}', this.maxValue)
            .replace('{min}', this.minValue)
            .replace('{progress}', Math.round(this.getDisplayProgress() * 100) + '%');
    }

    /**
     * 应用样式
     * @param {Object} style - 样式配置
     */
    applyStyle(style) {
        if (style.backgroundColor !== undefined) {
            this.backgroundColor = style.backgroundColor;
            if (this.background) {
                this.background.fillColor = this.backgroundColor;
            }
        }

        if (style.fillColor !== undefined) {
            this.fillColor = style.fillColor;
            if (this.fillBar) {
                this.fillBar.fillColor = this.fillColor;
            }
        }

        if (style.borderColor !== undefined) {
            this.borderColor = style.borderColor;
            if (this.border) {
                this.border.fillColor = this.borderColor;
            }
        }

        if (style.borderWidth !== undefined) {
            this.borderWidth = style.borderWidth;
            // 边框宽度变化需要重新创建
        }
    }

    /**
     * 设置文本格式
     * @param {string} format - 文本格式
     */
    setTextFormat(format) {
        this.textFormat = format;
        this.updateDisplay();
        return this;
    }

    /**
     * 设置是否显示文本
     * @param {boolean} show - 是否显示
     */
    setShowText(show) {
        this.showText = show;
        if (this.textObject) {
            this.textObject.setVisible(show);
        }
        return this;
    }
}
