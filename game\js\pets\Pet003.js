/**
 * 宠物003
 */
class Pet003 extends Pet {
    /** 缩放比例 */
    static SCALE = 3.8;

    /** 宠物间距 */
    static SPACING = 34;

    /**
     * 创建人
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {Player|Pet} target - 跟随目标（玩家或其他宠物）
     * @param {Object} config - 配置参数
     */
    constructor(scene, target, config = {}) {
        // 调用父类构造函数，传入基本配置
        super(scene, target, {
            ...config,
            id: 'pet003',
            name: '人',
            scale: Pet003.SCALE
            // 不设置速度，使用玩家速度
        });
    }

    /**
     * 加载宠物资源和创建精灵
     * @param {number} x - 初始X坐标
     * @param {number} y - 初始Y坐标
     */
    loadAssets(x, y) {
        // 检查第一帧纹理是否已加载
        if (!this.scene.textures.exists('pet_pet003_1')) {
            // 如果纹理未加载，尝试加载所有帧
            console.log('人纹理未加载，尝试加载');

            // 加载10帧图片
            for (let i = 1; i <= 10; i++) {
                const key = `pet_pet003_${i}`;
                const path = `assets/pets/character03/Hobbit - run${i}.png`;
                this.scene.load.image(key, path);
            }

            // 加载完成后创建动画和精灵
            this.scene.load.once('complete', () => {
                // 创建动画
                if (!this.scene.anims.exists('pet_pet003_anim')) {
                    this.scene.anims.create({
                        key: 'pet_pet003_anim',
                        frames: Array.from({ length: 10 }, (_, i) => ({
                            key: `pet_pet003_${i + 1}`
                        })),
                        frameRate: 10,
                        repeat: -1
                    });
                }

                this.createPetSprite(x, y);
            });

            // 开始加载
            this.scene.load.start();
        } else {
            // 纹理已加载，直接创建精灵
            this.createPetSprite(x, y);
        }
    }

    /**
     * 创建宠物精灵
     * @param {number} x - 初始X坐标
     * @param {number} y - 初始Y坐标
     */
    createPetSprite(x, y) {
        // 创建精灵，使用第一帧（不使用物理碰撞）
        this.sprite = this.scene.add.sprite(x, y, 'pet_pet003_1');
        this.sprite.setOrigin(0.5, 0.52); // 纹理位置数值

        // 检查动画是否已创建
        if (this.scene.anims.exists('pet_pet003_anim')) {
            // 播放动画
            this.sprite.play('pet_pet003_anim');
            console.log('创建人并播放动画');
        } else {
            console.warn('人动画未创建');
        }

        // 设置深度和缩放
        if (this.sprite) {
            this.sprite.customType = 'Pet';

            if (typeof DepthManager !== 'undefined') {
                DepthManager.updateDepth(this.sprite, 'Pet', true);
            }

            this.sprite.setScale(this.scale);
        }

        // 初始化宠物武器
        this.weapon = new A1(this.scene, this);
    }

}
