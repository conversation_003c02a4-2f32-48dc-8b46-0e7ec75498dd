/**
 * 宠物基类
 */
class Pet {
    /**
     * 创建一个宠物
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {Object} config - 配置参数
     */
    constructor(scene, target, config = {}) {
        this.scene = scene;
        this.target = target;

        // 宠物基本信息
        this.id = config.id || 'default_pet';
        this.name = config.name || '默认宠物';
        this.scale = config.scale || 1.0;      // 缩放比例

        // 跟随相关参数
        this.followDelay = config.followDelay || 80; // 跟随延迟步数
        this.lerpFactor = 0.5; // 插值因子，控制跟随平滑度

        // 战斗属性
        this.attackPower = 0; // 攻击力，初始为0


        // 创建精灵
        this.createSprite();
    }

    /**
     * 创建宠物精灵 - 由子类实现具体逻辑
     */
    createSprite() {
        // 获取目标精灵
        const targetSprite = this.target.sprite;

        // 初始位置就是目标位置
        const initialX = targetSprite.x;
        const initialY = targetSprite.y;

        // 加载宠物资源和创建精灵 - 子类必须实现此方法
        this.loadAssets(initialX, initialY);
    }

    /**
     * 加载宠物资源和创建精灵 - 子类必须实现此方法
     * @param {number} x - 初始X坐标
     * @param {number} y - 初始Y坐标
     */
    loadAssets(x, y) {
        // 子类必须重写此方法以实现特定的资源加载逻辑
        console.log(`宠物 ${this.id} 需要实现loadAssets方法，初始位置: (${x}, ${y})`);
    }



    /**
     * 更新宠物
     */
    update() {
        if (!this.sprite || !this.target) return;

        // 获取玩家实例
        const player = this.getPlayerInstance();
        if (!player) return;

        // 获取历史位置
        const historyPos = player.getHistoryPosition(this.followDelay);
        if (!historyPos) return;

        // 使用插值实现平滑跟随
        this.followHistoryPosition(historyPos);

        // 更新朝向
        this.updateDirection(historyPos.flipX);

        // 更新宠物武器
        if (this.weapon) {
            this.weapon.update(this.scene.time.now);
        }
    }

    /**
     * 获取玩家实例
     * @returns {Player} 玩家实例
     */
    getPlayerInstance() {
        // 如果目标是玩家，直接返回
        if (this.target instanceof Player) {
            return this.target;
        }

        // 如果目标是宠物，递归查找玩家
        if (this.target instanceof Pet && this.target.target) {
            return this.target.getPlayerInstance();
        }

        return null;
    }

    /**
     * 跟随历史位置
     * @param {Object} position - 历史位置对象
     */
    followHistoryPosition(position) {
        if (!this.sprite || !position) return;

        // 使用插值计算新位置，实现平滑移动
        this.sprite.x = Phaser.Math.Linear(this.sprite.x, position.x, this.lerpFactor);
        this.sprite.y = Phaser.Math.Linear(this.sprite.y, position.y, this.lerpFactor);
    }

    /**
     * 更新宠物朝向
     * @param {boolean} flipX - 是否水平翻转
     */
    updateDirection(flipX) {
        if (!this.sprite) return;

        // 设置宠物朝向与历史位置相同
        this.sprite.setFlipX(flipX);
    }

    /**
     * 销毁宠物
     */
    destroy() {
        if (this.sprite) {
            this.sprite.destroy();
            this.sprite = null;
        }
    }
}