
高级游戏系统设计师

【和平精英 核心策划】【真格AI Game Hackathon获奖制作人】【红杉终轮CPO】【主流AI IDE工作流建设】【高效团队治理】【C#/python等】【产品矩阵】

经验
======
AI游戏制作人
【游云创玩】
09/2022 - now
AI游戏UGC游玩平台（荣誉：AI⿊客松3等奖；红杉YUE终轮）
游刃 · YoRunner
 - 【AI游戏系统设计与团队赋能】构建自有phaser框架，编写生产工具，创建AI驱动开发流程，设计AI嵌入的代码生成架构，系统效率提升60%+
 - 【系统设计参数化】设计动态系统实现参数化调节，采用决策树概率模型，建立数值与系统智能化管理框架，实现游戏内容的自适应调节机制
 - 【AI游戏平台原型设计】预判 调研 验证市场趋势、提炼用户画像、思考获客与增长、成本及商业化等，构架用户模型与交易模型，输出产品原型
 - 【AI游戏设计】基于UGC功能与AI特性，设计⾼度适配UGC的AI游戏玩法，构架接⼊AIGC⽣成内容的gameplay、嵌⼊LLM推动发展的AI剧情bot，关注AI前沿技术论⽂，激励团队攻克了LLM驱动⾓⾊⾏为、prompt携带long term memory等技术难点，并统筹有限资源，驱动⼩组⽣产，输出分⼯TO DO list，unity内C#脚本提出算法与构架优化意⻅，部分游戏在gamejam中获奖
 - 【主导设计的二次元AI产品上线2个月突破50w用户】无投流的情况下 策划产品小红书话题热度7000w，日活最高10w用户

攻坚负责人（项目系统优化）
【我们的星球】
10/2023 - 10/2024
腾讯系帕鲁like社交沙盒手游
英雄 · HERO GAME
 - 【攻坚团队组建、统筹管理、士气提升】通过关键成员1v1深度面谈摸底项目能力与产能，快速筛选20人跨职能攻坚小组，激活影响力调动团队士气，建立高效协作机制，团队凝聚力显著提升，多名核心成员表达长期合作意愿
 - 【系统问题诊断与设计重构】独立诊断项目问题，复杂系统解耦重构，统筹系统、数值、战斗、UX各组资源，通过号召力和专业权威打通部门壁垒，显著提升跨部门执行效率，获得各组长一致认可和主动配合，负责模块0bug报错，机制在其他模块成功复用，最终通过腾讯审核成功上线

核心版本系统策划
【和平精英】
12/2017 - 08/2022
头部射击手游 （荣誉：金摇杆、IEG年度进步奖等）
腾讯 · Tencent
 - 【版本系统问题诊断与优化】诊断版本主题系统用户负反馈根源，设计并主导实施全新的版本主题系统架构，重新构建用户感知模型和交互逻辑，撰写版本⽩⽪书并跨组对外宣讲拉⻬认知，统筹协调策划、美术、程序、运营、市场等部门30+人员，通过个人专业权威和沟通打通部门壁垒，协同策划 美术 程序落地，并执⾏相应的审核⼯作，最终实现MUR好评率从30%提升至70%
 - 【系统设计方法论体系化与人才培养】将系统优化经验抽象为可复用的设计方法论，开设部门内训课程，培养系统设计人才，逻辑被后续版本沿⽤，所在部门专业口碑和影响力大幅提升，建立的设计模式成为后续版本标准
 - 【跨部门影响力建设与协作优化】通过个人沟通与专业化彰显打通所在部门原本闭塞的合作渠道，提升部门影响⼒，获得各部门负责人一致认可
 - 【其他系统模块设计】赛季典藏室系统、UGC模块绿洲启元内嵌玩法、用户回流系统等多个核心系统的玩法架构设计与跟进

执行副主策
【黎明行动】
建造合作⽣存竞技（荣誉：TimiStar榜样员工奖）
 - 【从零构建核心玩法系统】从零开始深度参与全⾃研流程，设计确定游戏核⼼玩法与细节机制，组织⽇常体验并提出调优意⻅；构架世界观背景事件，并设计局内碎⽚化叙事逻辑，初版demo为团队争取到⽴项资格，先游测试后，该玩法合⼊和平精英版内
 - 
【⽇常版本管理】对⽇常构建版本负责，收集版本问题，多组协调输出优化⽅案并推动落地，版本获总办肯定，助团队赢得公司级赛道资格
【UE\Unity关卡组长】设计核⼼开放关卡 定调各区战⽃体验&艺术氛围 搭建⽩盒提取美术需求 协调美术程序组推动落地并验收 提出迭代⽅案
 - 【生产流程优化与跨组协调】重建策划美术程序分⼯，优化UE编辑器内⽣产流程与规则，使⽤极少⼈⼒短期内⼤幅提升版本品质，得到高层认可
 - 【PVE丧尸系统与关卡设计】夜晚毒雾事件玩家改建安全屋抵御⼫潮的PVE体验: 设计关卡主题与核⼼玩法逻辑、安全屋内外功能与结构，建造等多途径抵御策略，搭建⽩盒关卡并验收；设计怪物攻击节奏、特性、技能；赋予关卡文学性，提取美术资源与蓝图需求，推动落地与测试迭代
 - 【彩6新手系统关卡设计】拆解彩6核⼼逻辑，设计易上⼿关卡，UE搭建关卡⽩盒，结合玩法设计⽂学主题，主导测试与调优，CE测试获高分
 - 【美术技术标准化与团队协作】与主美合作建立美术技术规范，输出优化建议，迭代资源⽣产⽅法，提升美术资源输出品质
 - 【绝地求⽣⼿游局内建造关卡研发】参与J3天美吃鸡⼿游后期新增的局内建造关卡合作研发，取得阶段性成果，并在合作版本内应⽤

IP策划总监
【魁拔】【狠西游】等
12/2013 - 01/2016
国内头部动漫IP产品（荣誉：华表奖等）
⻘⻘树 · Vasoon
 - 【组建团队，多IP开发】魁拔、神猎等头部IP规划，影游联动，后为⼦公司组建初始团队，同时推进多个原创项⽬，控品，保障⼦公司顺利成⽴


教育
======
哈尔滨理⼯⼤学HRBUST · 远东文学院FES
学⼠学位 · Bachelor

DeepLearning.AI · 人工智能深度学习
职业进修 · Cedu OL


亮点
======
【高效解决项目顽疾】
1周解决了困扰团队1年之久的载具3C负体验问题，定位问题 调研3C逻辑、载具力学，调动合作组积极性，快速应⽤优化⽅案，助⼒团队在公司级赛道竞争中胜出

【扭转项目数据】
主导构建和优化版本⼤模型、沉淀⽅法论，多部门协调，解决了和平精英版本负体验问题，MUR好评率从30%提⾄70%，⼤幅提升所在部门的综合影响⼒

【技术前沿探索】
构建自有phaser框架下的全AI生产流程，主导AI游戏UGC+云玩+直播平台的研发创新策略，在创投评估中获得多⽅资⾦⾸肯，协助公司进⼊红杉资本创投最终轮


案例简述（需要ai帮我整理）
======
腾讯：从零开始全面跟进了一款全自研的融入丧尸pve元素的合作生存竞技手游，深度参与了玩法构建，系统设计，多个类型的关卡设计与落地等相关的工作。这个版本从最初的表现。经过我整个重构了我们的一个工作流程执行标准的构建。嗯，后续是达到了一个竞争公司赛道级别的品质。后来我们这个没有当成一个新游上线，但是这个玩法，嗯是基本上被和平精英原封不动的拿去做了一个娱乐玩法。

后面就去了和平精英。和平精英那边主要负责的是一个嗯，协调版本内的程序美术策划和项目组外的这些支援部门来去协调。嗯构建一个嗯大版本。嗯，这个也是解决了一个问题，就是版本的感知度很低嗯什么时候开始什么时候结束什么时候应该关注什么嗯资源的投放玩家是比较一个模糊的状态。嗯，就是所以我们投的就是投放的一些活动，玩家的跟进度不高。同时mu r的一个评价好评度也只有30%。就是当时的一个版本的mur用户反馈的好评率只有30%嗯后面经过我的一个嗯方法的重构是好评达到了60~70%。那这个后面我可以展开讲一下。

嗯，从和平精英这边离开以后，嗯，我是做了一段时间的AI的拓扑。就围绕着AI嗯，一些技术的一些研究和一些好伙伴去做一些AI产品的研发嗯也参加了一些AI的海克省AI gem嗯包括一些嗯基金的AI相关的创投比如说我们后面也进了红山月的最终轮啊！
自有的facer的框架。百分之60.嗯
。
英雄游戏这边的工作。嗯，英雄游戏这边的工作主要是一个项目治理。
无法通过腾讯那边的一个嗯上限标准。目标： 能够接收数据不好，但必须要按期通过腾讯的审核上线。

嗯，一个是公司的的奖惩机制形同虚设。是他的考核机制基本上是无效的。
反复修改，积极度涣散。

设立新奖励，季度明星 发点小奖金。荣誉。
建立锦衣卫，攻坚组。
机制：owner的这个岗位就是它有更大的话语权。哦，有一定的人力调配的权力。我这边的任务都是优先级排名的，靠前的嘛，那么他就有一定的嗯，这个协调的一个筹码。
让跟着我做攻坚的 受尊重，有地位。
。
离开了还是有很多小朋友私聊说想跟着我。即便是被我这个鞭策过的就是有一些刺儿头打完了他还是给了他甜枣的。就相对他之前什么都不做。他在和我的合作中还是受益了的。嗯，有点斯特格尔摩也有说想要跟我一起的。


突出贡献：


。和平精英mur正向转化：的一个版本内投放内容混乱。玩家跟随活动意愿低。好评30%。
核心问题是难协调各组。目标是让各组都要尽量配合版本主题去做投放。各组争抢资源这次是你争到了下次是他争到。玩家形成不了习惯认知。

策划希望  ：版本主题最重要。投放值渠道分为三个等级。给信息分类。按照合版本的关联度。
我首先我来梳理一个版本的逻辑白皮书。按照规矩。先把正常的一个优先级和大家同步了一次。嗯，这个是公开场合的。
私底下我又和各组的组长李德去沟通。去了解他们的意愿。然后一边扮演为男一边答应他们，愿意去和策划这边的副总监去争取给你们组尽量在第一梯队的渠道里面一个长期固定的投放接口。但是这个部分必须要配合版本主题。

使各组他去配合版本主。同时他其他部分的内容配合版本主题的意愿也高了。
双赢。



。载具负体验的解决：不跟手，颠簸强烈，撞挺。大概持续了一年多呃，程序呢也非常的消极，或者说态度比较恶劣啊，所以策划被动 被怼。原理其实程序和策划都是未知。除了本身技术数值的修正 。，这个手动挡看。音效。镜头。摇晃。新人程序的果实分享。策划组地位抬升。



碎片化叙事的方法论沉淀：线性顺序 沉浸 ，触发概率 ，高频。  基础背景 ＞ 细节丰富 ＞反转



llm无法携带long term memory。：没有多伦输入记忆。微积分  x +关键时间1。


观察是一个容器。就是他的体验包含了所有游戏里面的内容，比如说嗯，3c嗯，比如说怪物战斗嗯比如说这个UI比如说美术品质。你很难单一把关卡拿出来单拿出来来说观察本身的体验。所以他的体验是随机且观察策划很大可能不可控
r6也有设计失败的观察，比如说贫民窟。
。如果强行要解说一下到底观察的一个核心设计的技巧是什么，那就是要去平衡，既给玩家掌控感，又给玩家无力感。嗯，就是跟这个我前面说的育人是一样的，你既要给他巴掌，还要给他甜枣。

是管理团队的话就是要把你的成员当人。现在我觉得已经不需要说怎么pua怎么布吧人当人在中国在各个公司里这种规矩这种管理方法已经有很多了。我在职场混过的就都知道这些也不用我讲了，所以在这种大环境下的一个最好的管理方式就是少用这些负面的技巧。去共赢。让他们觉得跟着你混舒适。就这么简单。全靠同行衬托。
