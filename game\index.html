<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>mini</title>
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            overflow: hidden;
        }
        #game-container {
            position: relative;
            width: 100%;
            height: 100%;
        }
        canvas {
            display: block;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div id="game-container"></div>


    <!-- 引入Phaser框架（本地文件v3.90.0） -->
    <script src="lib/phaser.min.js"></script>

    <!-- 配置文件 -->
    <script src="js/config/WorkstationConfig.js"></script>

    <!-- 工具类 -->
    <script src="js/managers/DepthManager.js"></script>
    <script src="js/managers/SaveManager.js"></script>
    <script src="js/managers/GameData.js"></script>
    <script src="js/managers/BatSwarmManager.js"></script>
    <script src="js/utils/InputManager.js"></script>
    <script src="js/utils/BuffUtils.js"></script>
    <script src="js/utils/WeaponUpgradeUtils.js"></script>
    <script src="js/utils/UpgradeOptionGenerator.js"></script>

    <!-- 武器系统 -->
    <script src="js/weapons/Weapon.js"></script>
    <script src="js/weapons/A.js"></script>
    <script src="js/weapons/A1.js"></script>
    <script src="js/weapons/B.js"></script>
    <script src="js/weapons/B1.js"></script>
    <script src="js/weapons/C.js"></script>
    <script src="js/weapons/C1.js"></script>
    <script src="js/weapons/D.js"></script>
    <script src="js/weapons/D1.js"></script>
    <script src="js/weapons/WeaponManager.js"></script>

    <!-- 宠物系统 -->
    <script src="js/pets/Pet.js"></script>
    <script src="js/pets/Pet001.js"></script>
    <script src="js/pets/Pet002.js"></script>
    <script src="js/pets/Pet003.js"></script>
    <script src="js/pets/Pet004.js"></script>
    <script src="js/pets/PetManager.js"></script>

    <!-- 实体类 -->
    <script src="js/entities/Player.js"></script>
    <script src="js/entities/Enemy.js"></script>
    <script src="js/entities/BatSwarm.js"></script>

    <!-- 怪物类型 -->
    <script src="js/entities/000a.js"></script>
    <script src="js/entities/000b.js"></script>
    <script src="js/entities/001.js"></script>
    <script src="js/entities/002.js"></script>
    <script src="js/entities/003.js"></script>
    <script src="js/entities/004.js"></script>
    <script src="js/entities/006.js"></script>
    <script src="js/entities/007.js"></script>
    <script src="js/entities/008.js"></script>
    <script src="js/entities/009.js"></script>
    <script src="js/entities/011.js"></script>
    <script src="js/entities/012.js"></script>
    <script src="js/entities/013.js"></script>
    <script src="js/entities/015.js"></script>

    <!-- 物品类 -->
    <script src="js/objects/StaticObject.js"></script>
    <script src="js/objects/tools/Axe.js"></script>
    <script src="js/objects/BaseWorkstation.js"></script>
    <script src="js/objects/HealthWorkstation.js"></script>
    <script src="js/objects/SpeedWorkstation.js"></script>
    <script src="js/objects/AttackWorkstation.js"></script>
    <script src="js/objects/RangeWorkstation.js"></script>
    <script src="js/items/Coin.js"></script>
    <script src="js/items/ExperienceGem.js"></script>
    <script src="js/items/Diamond.js"></script>
    <script src="js/items/HealthPotion.js"></script>
    <script src="js/items/InGameItems.js"></script>
    <script src="js/items/GoldCoin.js"></script>

    <!-- UI系统 -->
    <script src="js/ui/UIEventManager.js"></script>
    <script src="js/ui/UITheme.js"></script>
    <script src="js/ui/UIComponent.js"></script>
    <script src="js/ui/components/UIButton.js"></script>
    <script src="js/ui/components/UIText.js"></script>
    <script src="js/ui/components/UIProgressBar.js"></script>
    <script src="js/ui/components/UIPanel.js"></script>
    <script src="js/ui/UIModal.js"></script>
    <script src="js/ui/UIManager.js"></script>
    <script src="js/ui/UpgradeModal.js"></script>

    <!-- 游戏场景 -->
    <script src="js/scenes/StartScene.js"></script>
    <script src="js/scenes/LoadingScene.js"></script>
    <script src="js/scenes/MainMenuScene.js"></script>
    <script src="js/scenes/HomeScene.js"></script>
    <script src="js/scenes/PetShopScene.js"></script>
    <script src="js/scenes/GameScene.js"></script>

    <!-- 游戏主入口 -->
    <script src="js/main.js"></script>
</body>
</html>
