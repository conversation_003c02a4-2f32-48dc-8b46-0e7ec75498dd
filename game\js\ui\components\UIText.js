/**
 * UI文本组件
 * 统一的文本组件，支持主题和样式管理
 */
class UIText extends UIComponent {
    constructor(scene, config = {}) {
        super(scene, config);
        
        // 文本特有属性
        this.text = config.text || '';
        this.font = config.font || '18px Arial';
        this.color = config.color || '#ffffff';
        this.strokeColor = config.strokeColor || '#000000';
        this.strokeThickness = config.strokeThickness || 3;
        this.align = config.align || 'left';
        this.originX = config.originX !== undefined ? config.originX : 0;
        this.originY = config.originY !== undefined ? config.originY : 0;
        
        // Phaser文本对象
        this.textObject = null;
        
        this.create();
    }

    /**
     * 创建文本
     */
    create() {
        // 创建文本样式
        const style = {
            font: this.font,
            fill: this.color,
            align: this.align
        };
        
        // 添加描边
        if (this.strokeThickness > 0) {
            style.stroke = this.strokeColor;
            style.strokeThickness = this.strokeThickness;
        }
        
        // 创建文本对象
        this.textObject = this.scene.add.text(
            this.x,
            this.y,
            this.text,
            style
        ).setOrigin(this.originX, this.originY);
        
        // 添加到游戏对象列表
        this.addGameObject(this.textObject);
        
        console.log(`文本创建完成: ${this.text.substring(0, 20)}...`);
    }

    /**
     * 设置文本内容
     * @param {string} text - 新文本
     */
    setText(text) {
        this.text = text;
        if (this.textObject) {
            this.textObject.setText(text);
        }
        return this;
    }

    /**
     * 获取文本内容
     * @returns {string} 当前文本
     */
    getText() {
        return this.text;
    }

    /**
     * 设置文本颜色
     * @param {string} color - 颜色值
     */
    setColor(color) {
        this.color = color;
        if (this.textObject) {
            this.textObject.setFill(color);
        }
        return this;
    }

    /**
     * 设置字体
     * @param {string} font - 字体样式
     */
    setFont(font) {
        this.font = font;
        if (this.textObject) {
            this.textObject.setFont(font);
        }
        return this;
    }

    /**
     * 设置描边
     * @param {string} color - 描边颜色
     * @param {number} thickness - 描边厚度
     */
    setStroke(color, thickness) {
        this.strokeColor = color;
        this.strokeThickness = thickness;
        if (this.textObject) {
            this.textObject.setStroke(color, thickness);
        }
        return this;
    }

    /**
     * 设置对齐方式
     * @param {string} align - 对齐方式 ('left', 'center', 'right')
     */
    setAlign(align) {
        this.align = align;
        if (this.textObject) {
            this.textObject.setAlign(align);
        }
        return this;
    }

    /**
     * 设置原点
     * @param {number} x - X原点 (0-1)
     * @param {number} y - Y原点 (0-1)
     */
    setOrigin(x, y) {
        this.originX = x;
        this.originY = y;
        if (this.textObject) {
            this.textObject.setOrigin(x, y);
        }
        return this;
    }

    /**
     * 设置文本样式
     * @param {Object} style - 样式对象
     */
    setStyle(style) {
        if (this.textObject) {
            this.textObject.setStyle(style);
        }
        
        // 更新内部属性
        if (style.font) this.font = style.font;
        if (style.fill) this.color = style.fill;
        if (style.stroke) this.strokeColor = style.stroke;
        if (style.strokeThickness !== undefined) this.strokeThickness = style.strokeThickness;
        if (style.align) this.align = style.align;
        
        return this;
    }

    /**
     * 应用主题样式
     * @param {Object} style - 样式配置
     */
    applyStyle(style) {
        const newStyle = {};
        
        if (style.color !== undefined) {
            this.color = style.color;
            newStyle.fill = style.color;
        }
        
        if (style.font !== undefined) {
            this.font = style.font;
            newStyle.font = style.font;
        }
        
        if (style.strokeColor !== undefined) {
            this.strokeColor = style.strokeColor;
            newStyle.stroke = style.strokeColor;
        }
        
        if (style.strokeThickness !== undefined) {
            this.strokeThickness = style.strokeThickness;
            newStyle.strokeThickness = style.strokeThickness;
        }
        
        if (style.align !== undefined) {
            this.align = style.align;
            newStyle.align = style.align;
        }
        
        this.setStyle(newStyle);
    }

    /**
     * 获取文本宽度
     * @returns {number} 文本宽度
     */
    getTextWidth() {
        return this.textObject ? this.textObject.width : 0;
    }

    /**
     * 获取文本高度
     * @returns {number} 文本高度
     */
    getTextHeight() {
        return this.textObject ? this.textObject.height : 0;
    }

    /**
     * 设置文本透明度
     * @param {number} alpha - 透明度 (0-1)
     */
    setAlpha(alpha) {
        if (this.textObject) {
            this.textObject.setAlpha(alpha);
        }
        return this;
    }

    /**
     * 设置文本旋转
     * @param {number} rotation - 旋转角度（弧度）
     */
    setRotation(rotation) {
        if (this.textObject) {
            this.textObject.setRotation(rotation);
        }
        return this;
    }

    /**
     * 设置文本缩放
     * @param {number} scaleX - X轴缩放
     * @param {number} scaleY - Y轴缩放
     */
    setScale(scaleX, scaleY = scaleX) {
        if (this.textObject) {
            this.textObject.setScale(scaleX, scaleY);
        }
        return this;
    }
}
