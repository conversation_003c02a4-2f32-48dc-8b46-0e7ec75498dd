/**
 * 升级选项模态框类
 * 统一管理升级选项UI的显示、交互和销毁
 */
class UpgradeModal {
    constructor(scene) {
        this.scene = scene;
        this.isVisible = false;
        this.uiElements = [];
        this.eventListeners = [];
        this._wasAlreadyPaused = false;
    }

    /**
     * 显示升级选项
     */
    show() {
        // 防止重复显示
        if (this.isVisible) {
            console.warn('升级选项已经显示，忽略重复调用');
            return;
        }

        this.isVisible = true;

        // 记录原始暂停状态并暂停游戏
        this._wasAlreadyPaused = this.scene.isPaused;
        this.pauseGame();

        // 生成升级选项
        const options = window.upgradeOptionGenerator.generateUpgradeOptions(this.scene.player);

        // 创建UI
        this.createUI(options);
    }

    /**
     * 暂停游戏
     */
    pauseGame() {
        this.scene.isPaused = true;
        this.scene.physics.pause();
        this.scene.anims.pauseAll();

        if (this.scene.player && this.scene.player.sprite) {
            this.scene.player.sprite.setVelocity(0, 0);
        }
    }

    /**
     * 恢复游戏
     */
    resumeGame() {
        if (!this._wasAlreadyPaused) {
            this.scene.isPaused = false;
            this.scene.physics.resume();
            this.scene.anims.resumeAll();
            this.scene.startTime = this.scene.time.now - this.scene.gameTime;
        }
    }

    /**
     * 创建UI元素
     */
    createUI(options) {
        const centerX = this.scene.cameras.main.width / 2;
        const centerY = this.scene.cameras.main.height / 2;

        // 创建遮罩层
        const overlay = this.scene.add.rectangle(
            centerX, centerY,
            this.scene.cameras.main.width,
            this.scene.cameras.main.height,
            0x000000, 0.7
        ).setScrollFactor(0).setDepth(1000);

        // 创建标题
        const title = this.scene.add.text(
            centerX, centerY - 200,
            '选择升级',
            {
                font: 'bold 28px Arial',
                fill: '#ffffff',
                stroke: '#000000',
                strokeThickness: 4
            }
        ).setOrigin(0.5).setScrollFactor(0).setDepth(1001);

        // 创建卡片背景
        const card = this.scene.add.image(
            centerX, centerY, 'card'
        ).setScrollFactor(0).setDepth(1002).setInteractive().setScale(2);

        // 创建选项文本
        const leftOption = this.scene.add.text(
            centerX - 120, centerY + 50,
            options[0].name,
            {
                font: 'bold 16px Arial',
                fill: '#ffffff',
                align: 'center',
                wordWrap: { width: 100 }
            }
        ).setOrigin(0.5).setScrollFactor(0).setDepth(1003);

        const rightOption = this.scene.add.text(
            centerX + 120, centerY + 50,
            options[1].name,
            {
                font: 'bold 16px Arial',
                fill: '#ffffff',
                align: 'center',
                wordWrap: { width: 100 }
            }
        ).setOrigin(0.5).setScrollFactor(0).setDepth(1003);

        // 创建图标
        const iconY = centerY - 100;
        const leftIcon = this.createIcon(options[0], centerX - 120, iconY);
        const rightIcon = this.createIcon(options[1], centerX + 120, iconY);

        // 存储UI元素（按固定顺序）
        this.uiElements = [overlay, title, card, leftOption, rightOption, leftIcon, rightIcon];

        // 设置交互
        this.setupInteraction(card, options, centerX, centerY);
    }

    /**
     * 创建图标
     */
    createIcon(option, x, y) {
        const iconKey = this.getUpgradeIcon(option);
        if (iconKey && this.scene.textures.exists(iconKey)) {
            const icon = this.scene.add.image(x, y, iconKey)
                .setScrollFactor(0).setDepth(1003).setScale(3);

            // 添加呼吸效果
            this.scene.tweens.add({
                targets: icon,
                scaleX: 3.3,
                scaleY: 3.3,
                duration: 1000,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });

            return icon;
        }
        return null;
    }

    /**
     * 获取升级选项图标
     */
    getUpgradeIcon(option) {
        if (option.weaponType) {
            const weaponIconMap = { 'A': 'a', 'B': 'b', 'C': 'c', 'D': 'd' };
            return weaponIconMap[option.weaponType] || 'a';
        }
        if (option.icon) {
            return option.icon;
        }
        return 'a';
    }

    /**
     * 设置交互逻辑
     */
    setupInteraction(card, options, centerX, centerY) {
        let isDragging = false;
        let startX = 0;

        // 获取需要移动的UI元素
        const leftOption = this.uiElements[3]; // leftOption
        const rightOption = this.uiElements[4]; // rightOption
        const leftIcon = this.uiElements[5]; // leftIcon (可能为null)
        const rightIcon = this.uiElements[6]; // rightIcon (可能为null)

        // 鼠标按下事件
        const onPointerDown = (pointer) => {
            isDragging = true;
            startX = pointer.x;
        };

        // 鼠标移动事件
        const onPointerMove = (pointer) => {
            if (!isDragging) return;

            const deltaX = pointer.x - startX;

            // 卡片跟随移动
            card.x = centerX + deltaX;
            leftOption.x = centerX - 120 + deltaX;
            rightOption.x = centerX + 120 + deltaX;

            // 图标跟随移动
            if (leftIcon) leftIcon.x = centerX - 120 + deltaX;
            if (rightIcon) rightIcon.x = centerX + 120 + deltaX;

            // 旋转效果
            const rotation = deltaX * 0.001;
            card.rotation = rotation;
            leftOption.rotation = rotation;
            rightOption.rotation = rotation;
            if (leftIcon) leftIcon.rotation = rotation;
            if (rightIcon) rightIcon.rotation = rotation;

            // 实时检测：距离超过阈值立即触发选择
            const distance = Math.abs(deltaX);
            const threshold = this.scene.cameras.main.width * 0.45;
            if (distance > threshold) {
                isDragging = false;
                const selectedOption = deltaX < 0 ? options[0] : options[1];
                this.selectOption(selectedOption);
            }
        };

        // 鼠标释放事件
        const onPointerUp = () => {
            if (!isDragging) return;
            isDragging = false;

            const distance = card.x - centerX;
            const threshold = this.scene.cameras.main.width * 0.25;

            if (Math.abs(distance) >= threshold) {
                const selectedOption = distance < 0 ? options[0] : options[1];
                this.selectOption(selectedOption);
            } else {
                // 回到原位
                card.x = centerX;
                leftOption.x = centerX - 120;
                rightOption.x = centerX + 120;
                card.rotation = 0;
                leftOption.rotation = 0;
                rightOption.rotation = 0;

                // 图标回到原位
                if (leftIcon) {
                    leftIcon.x = centerX - 120;
                    leftIcon.rotation = 0;
                }
                if (rightIcon) {
                    rightIcon.x = centerX + 120;
                    rightIcon.rotation = 0;
                }
            }
        };

        // 绑定事件
        card.on('pointerdown', onPointerDown);
        this.scene.input.on('pointermove', onPointerMove);
        this.scene.input.on('pointerup', onPointerUp);

        // 存储事件监听器以便清理
        this.eventListeners = [
            { target: card, event: 'pointerdown', handler: onPointerDown },
            { target: this.scene.input, event: 'pointermove', handler: onPointerMove },
            { target: this.scene.input, event: 'pointerup', handler: onPointerUp }
        ];
    }

    /**
     * 选择升级选项
     */
    selectOption(selectedOption) {
        // 记录选择
        if (window.upgradeOptionGenerator) {
            window.upgradeOptionGenerator.recordSelection(selectedOption);
        }

        // 应用升级
        selectedOption.apply(this.scene.player);

        // 关闭模态框
        this.hide();
    }

    /**
     * 隐藏升级选项
     */
    hide() {
        if (!this.isVisible) return;

        // 清理事件监听器
        this.eventListeners.forEach(({ target, event, handler }) => {
            target.off(event, handler);
        });
        this.eventListeners = [];

        // 销毁UI元素
        this.uiElements.forEach(element => {
            if (element && element.destroy) {
                element.destroy();
            }
        });
        this.uiElements = [];

        // 恢复游戏
        this.resumeGame();

        // 通知GameScene解除升级锁定
        if (this.scene && typeof this.scene.onUpgradeModalClosed === 'function') {
            this.scene.onUpgradeModalClosed();
        }

        // 重置状态
        this.isVisible = false;
    }

    /**
     * 销毁模态框
     */
    destroy() {
        this.hide();
    }
}
