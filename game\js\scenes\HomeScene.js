/**
 * 家园场景
 */
class HomeScene extends Phaser.Scene {
    constructor() {
        super({ key: 'HomeScene' });
        this.uiManager = null;
    }

    create() {
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;

        // 创建UI管理器
        this.uiManager = new UIManager(this);

        // 初始化深度管理器
        DepthManager.initScene(this);

        // 地图缩放系数
        const mapScale = 0.8;

        // 获取家园背景图片纹理并获取其尺寸
        const homeTexture = this.textures.get('home');
        const homeWidth = homeTexture.getSourceImage().width;
        const homeHeight = homeTexture.getSourceImage().height;

        // 添加家园背景（使用image而非tileSprite，避免平铺）
        this.background = this.add.image(
            homeWidth * mapScale / 2,
            homeHeight * mapScale / 2,
            'home'
        ).setScale(mapScale);

        // 设置世界边界，限制为图片大小
        this.physics.world.setBounds(0, 0, homeWidth * mapScale, homeHeight * mapScale);

        // 添加玩家，位置在地图中央
        this.player = new Player(this, 800, homeHeight * mapScale / 2);

        // 启用玩家与世界边界的碰撞
        if (this.player) {
            this.player.setWorldCollision(true);
        }

        // 创建斧头静止物体（在玩家位置上方200像素）
        this.axe = new Axe(
            this,
            1190,  // x坐标与玩家相同
            450,  // y坐标在玩家上方200像素
            3  // 设置缩放为5
        );

        // 添加玩家与斧头的碰撞
        this.physics.add.collider(this.player.sprite, this.axe.sprite);

        // 创建标题文本
        this.uiManager.createComponent('text', 'title', {
            x: width / 2,
            y: 60,
            text: '家园',
            font: 'bold 32px Arial',
            color: '#ffffff',
            strokeColor: '#000000',
            strokeThickness: 6,
            originX: 0.5,
            originY: 0.5,
            scrollFactor: 0,
            depth: DepthManager.LAYERS.UI
        });

        // 创建返回按钮
        this.uiManager.createComponent('button', 'returnButton', {
            x: 100,
            y: 50,
            width: 160,
            height: 50,
            text: '返回主菜单',
            font: 'bold 16px Arial',
            backgroundColor: 0x3498db,
            hoverColor: 0x2980b9,
            scrollFactor: 0,
            depth: DepthManager.LAYERS.UI,
            onClick: () => {
                this.scene.start('MainMenuScene');
            }
        });

        // 创建钱币显示
        this.createCoinDisplay();

        // 设置相机跟随玩家
        this.cameras.main.startFollow(this.player.sprite);

        // 设置相机边界，不超出地图范围
        this.cameras.main.setBounds(0, 0, homeWidth * mapScale, homeHeight * mapScale);

        // 创建输入管理器
        this.inputManager = new InputManager(this, this.player);

        // 创建虚拟摇杆
        this.createVirtualJoystick();

        // 创建唯一多边形碰撞区域
        this.createHomeBoundary();

        // 创建工作台组（提前创建避免时序问题）
        this.workstationGroup = this.physics.add.staticGroup();

        // 创建工作台
        this.createWorkstation();

        // 设置工作台碰撞
        this.setupWorkstationCollisions();

        console.log('家园场景UI重构完成');
    }

    /**
     * 创建钱币显示
     */
    createCoinDisplay() {
        const width = this.cameras.main.width;

        // 获取累计钱币数量
        let totalCoins = 0;
        if (window.gameData) {
            try {
                totalCoins = window.gameData.getTotalCoins();
            } catch (error) {
                console.error('获取累计钱币失败:', error);
            }
        }

        // 创建钱币背景面板
        this.uiManager.createComponent('panel', 'coinPanel', {
            x: width - 120,
            y: 50,
            width: 200,
            height: 60,
            backgroundColor: 0x2c3e50,
            borderColor: 0xf39c12,
            borderWidth: 2,
            scrollFactor: 0,
            depth: DepthManager.LAYERS.UI
        });

        // 创建钱币图标文本（使用文字代替图标）
        this.uiManager.createComponent('text', 'coinIcon', {
            x: width - 180,
            y: 50,
            text: '💰',
            font: '24px Arial',
            color: '#f39c12',
            originX: 0.5,
            originY: 0.5,
            scrollFactor: 0,
            depth: DepthManager.LAYERS.UI + 1
        });

        // 创建钱币数量文本
        this.uiManager.createComponent('text', 'coinText', {
            x: width - 120,
            y: 50,
            text: totalCoins.toString(),
            font: 'bold 20px Arial',
            color: '#ffffff',
            strokeColor: '#000000',
            strokeThickness: 2,
            originX: 0.5,
            originY: 0.5,
            scrollFactor: 0,
            depth: DepthManager.LAYERS.UI + 1
        });

        console.log(`家园显示累计钱币: ${totalCoins}`);
    }

    /**
     * 更新钱币显示
     */
    updateCoinDisplay() {
        let totalCoins = 0;
        if (window.gameData) {
            try {
                totalCoins = window.gameData.getTotalCoins();
            } catch (error) {
                console.error('更新钱币显示失败:', error);
            }
        }

        const coinText = this.uiManager.getComponent('coinText');
        if (coinText) {
            coinText.setText(totalCoins.toString());
        }
    }

    /**
     * 创建唯一多边形碰撞区域（用小圆形拼接近似多边形边界，并用Graphics可视化）
     */
    createHomeBoundary() {
        // 边界坐标点
        const boundaryPoints = [
            { "x": 1277, "y": 401 }, { "x": 1124, "y": 398 },
            { "x": 1124, "y": 400 }, { "x": 1070, "y": 400 },
            { "x": 1070, "y": 359 }, { "x": 920, "y": 359 },
            { "x": 920, "y": 400 }, { "x": 517, "y": 400 },
            { "x": 515, "y": 460 }, { "x": 465, "y": 460 },
            { "x": 463, "y": 500 }, { "x": 343, "y": 500 },
            { "x": 336, "y": 533 }, { "x": 288, "y": 535 },

            { "x": 282, "y": 676 }, { "x": 345, "y": 683 },
            { "x": 340, "y": 727 }, { "x": 391, "y": 737 },
            { "x": 407, "y": 734 }, { "x": 417, "y": 677 },
            { "x": 487, "y": 667 }, { "x": 504, "y": 689 },
            { "x": 508, "y": 736 }, { "x": 515, "y": 770 },
            { "x": 511, "y": 799 }, { "x": 508, "y": 815 },
            { "x": 528, "y": 821 }, { "x": 563, "y": 827 },
            { "x": 564, "y": 856 }, { "x": 561, "y": 883 },
            { "x": 570, "y": 914 }, { "x": 588, "y": 921 },
            { "x": 618, "y": 936 }, { "x": 623, "y": 967 },
            { "x": 638, "y": 984 }, { "x": 666, "y": 1010 },
            { "x": 709, "y": 1043 }, { "x": 739, "y": 1063 },
            { "x": 794, "y": 1050 }, { "x": 809, "y": 1060 },
            { "x": 805, "y": 1110 }, { "x": 853, "y": 1091 },
            { "x": 894, "y": 1084 }, { "x": 925, "y": 1101 },
            { "x": 923, "y": 1128 }, { "x": 923, "y": 1147 }, { "x": 924, "y": 1156 }, { "x": 1164, "y": 1156 }, { "x": 1188, "y": 1130 }, { "x": 1215, "y": 1123 }, { "x": 1246, "y": 1142 }, { "x": 1241, "y": 1156 }, { "x": 1279, "y": 1137 }
        ];
        // 只保留碰撞体，不画红线
        this.boundaryGroup = this.physics.add.staticGroup();
        const radius = 3; // 每个小圆的半径
        for (let i = 0; i < boundaryPoints.length; i++) {
            const p1 = boundaryPoints[i];
            const p2 = boundaryPoints[(i + 1) % boundaryPoints.length];
            const dist = Phaser.Math.Distance.Between(p1.x, p1.y, p2.x, p2.y);
            const steps = Math.max(2, Math.ceil(dist / (radius * 1.5)));
            for (let s = 0; s < steps; s++) {
                const t = s / (steps - 1);
                const x = Phaser.Math.Interpolation.Linear([p1.x, p2.x], t);
                const y = Phaser.Math.Interpolation.Linear([p1.y, p2.y], t);
                const circle = this.add.circle(x, y, radius, 0x00ff00, 0.2);
                circle.setVisible(false);
                this.boundaryGroup.add(circle);
                // 让circle有物理体
                this.physics.add.existing(circle, true);
                circle.body.setCircle(radius);
            }
        }
        // 玩家与唯一边界组发生碰撞
        if (this.player && this.player.sprite) {
            this.physics.add.collider(this.player.sprite, this.boundaryGroup);
        }
    }

    update(time) {
        // 更新玩家
        if (this.player) {
            this.player.update(time);
        }

        // 更新输入管理器
        if (this.inputManager) {
            this.inputManager.update();
        }

        // 更新虚拟摇杆
        this.updateVirtualJoystick();

        // 定期更新钱币显示（每秒检查一次）
        if (!this.lastCoinUpdateTime || time - this.lastCoinUpdateTime > 1000) {
            this.updateCoinDisplay();
            this.lastCoinUpdateTime = time;
        }
    }

    /**
     * 创建工作台
     */
    createWorkstation() {
        // 创建4个工作台
        this.healthWorkstation = new HealthWorkstation(this, 600, 780);
        this.speedWorkstation = new SpeedWorkstation(this, 1080, 980);
        this.attackWorkstation = new AttackWorkstation(this, 710, 430);
        this.rangeWorkstation = new RangeWorkstation(this, 1160, 660);
        console.log('所有工作台已创建');
    }

    /**
     * 设置工作台碰撞
     */
    setupWorkstationCollisions() {
        // 添加所有工作台到组（跳过multiple类型的主精灵）
        const workstations = [this.healthWorkstation, this.speedWorkstation, this.attackWorkstation, this.rangeWorkstation];
        workstations.forEach(workstation => {
            if (workstation.getCollisionConfig().type !== 'multiple') {
                this.workstationGroup.add(workstation.sprite);
            }
        });

        // 设置玩家与工作台组的碰撞
        this.physics.add.collider(this.player.sprite, this.workstationGroup);

        console.log('工作台碰撞设置完成');
    }

    /**
     * 创建工作台购买按钮
     */
    createWorkstationBuyButton(config) {
        this.uiManager.createComponent('button', 'buyWorkstationButton', {
            x: 600,
            y: 350,
            width: 200,
            height: 60,
            text: `购买${config.name}\n${config.cost}💰`,
            font: 'bold 16px Arial',
            backgroundColor: 0x27ae60,
            hoverColor: 0x2ecc71,
            scrollFactor: 1, // 跟随相机
            depth: 100,
            onClick: () => {
                this.buyWorkstation(config);
            }
        });
    }

    /**
     * 购买工作台
     */
    buyWorkstation(config) {
        const success = window.gameData.buyWorkstation(config.id, config.cost);
        if (success) {
            // 购买成功，移除按钮并显示工作台
            this.uiManager.destroyComponent('buyWorkstationButton');
            this.healthWorkstation = new HealthWorkstation(this, 600, 350);
            this.updateCoinDisplay();
            console.log(`${config.name}购买成功`);
        } else {
            console.log('购买失败：金币不足');
        }
    }

    /**
     * 场景销毁时清理UI
     */
    destroy() {
        // 清理虚拟摇杆
        this.destroyVirtualJoystick();
        // 清理所有工作台
        if (this.healthWorkstation) {
            this.healthWorkstation.destroy();
            this.healthWorkstation = null;
        }
        if (this.speedWorkstation) {
            this.speedWorkstation.destroy();
            this.speedWorkstation = null;
        }
        if (this.attackWorkstation) {
            this.attackWorkstation.destroy();
            this.attackWorkstation = null;
        }
        if (this.rangeWorkstation) {
            this.rangeWorkstation.destroy();
            this.rangeWorkstation = null;
        }

        if (this.uiManager) {
            this.uiManager.destroyAll();
            this.uiManager = null;
        }
        super.destroy();
    }

    /**
     * 创建虚拟摇杆
     */
    createVirtualJoystick() {
        // 摇杆参数
        this.joystick = {
            isActive: false,
            baseRadius: 60,
            knobRadius: 25,
            maxDistance: 50,
            baseX: 0,
            baseY: 0,
            knobX: 0,
            knobY: 0,
            moveX: 0,
            moveY: 0
        };

        // 创建摇杆底盘（初始隐藏）
        this.joystickBase = this.add.circle(0, 0, this.joystick.baseRadius, 0x333333, 0.3)
            .setDepth(DepthManager.LAYERS.UI + 5)
            .setScrollFactor(0)
            .setVisible(false);

        // 创建摇杆头（初始隐藏）
        this.joystickKnob = this.add.circle(0, 0, this.joystick.knobRadius, 0x666666, 0.8)
            .setDepth(DepthManager.LAYERS.UI + 6)
            .setScrollFactor(0)
            .setVisible(false);

        // 绑定触摸事件到场景输入
        this.input.on('pointerdown', this.onJoystickPointerDown, this);
        this.input.on('pointermove', this.onJoystickPointerMove, this);
        this.input.on('pointerup', this.onJoystickPointerUp, this);

        console.log('虚拟摇杆已创建');
    }

    /**
     * 触摸开始事件
     */
    onJoystickPointerDown(pointer) {
        // 激活摇杆
        this.joystick.isActive = true;

        // 设置摇杆底盘位置为触摸位置
        this.joystick.baseX = pointer.x;
        this.joystick.baseY = pointer.y;
        this.joystick.knobX = pointer.x;
        this.joystick.knobY = pointer.y;

        // 显示摇杆
        this.joystickBase.setPosition(this.joystick.baseX, this.joystick.baseY).setVisible(true);
        this.joystickKnob.setPosition(this.joystick.knobX, this.joystick.knobY).setVisible(true);
    }

    /**
     * 触摸移动事件
     */
    onJoystickPointerMove(pointer) {
        if (!this.joystick.isActive) return;

        // 计算从底盘中心到触摸点的距离和角度
        const deltaX = pointer.x - this.joystick.baseX;
        const deltaY = pointer.y - this.joystick.baseY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        if (distance <= this.joystick.maxDistance) {
            // 在范围内，摇杆头跟随触摸点
            this.joystick.knobX = pointer.x;
            this.joystick.knobY = pointer.y;
        } else {
            // 超出范围，限制在最大距离内
            const angle = Math.atan2(deltaY, deltaX);
            this.joystick.knobX = this.joystick.baseX + Math.cos(angle) * this.joystick.maxDistance;
            this.joystick.knobY = this.joystick.baseY + Math.sin(angle) * this.joystick.maxDistance;
        }

        // 更新摇杆头位置
        this.joystickKnob.setPosition(this.joystick.knobX, this.joystick.knobY);

        // 计算移动向量（标准化到-1到1之间）
        const finalDeltaX = this.joystick.knobX - this.joystick.baseX;
        const finalDeltaY = this.joystick.knobY - this.joystick.baseY;

        this.joystick.moveX = finalDeltaX / this.joystick.maxDistance;
        this.joystick.moveY = finalDeltaY / this.joystick.maxDistance;
    }

    /**
     * 触摸结束事件
     */
    onJoystickPointerUp() {
        // 停用摇杆
        this.joystick.isActive = false;

        // 隐藏摇杆
        this.joystickBase.setVisible(false);
        this.joystickKnob.setVisible(false);

        // 重置移动向量
        this.joystick.moveX = 0;
        this.joystick.moveY = 0;
    }

    /**
     * 更新虚拟摇杆
     */
    updateVirtualJoystick() {
        if (!this.joystick || !this.player) return;

        // 如果摇杆激活，使用摇杆输入控制玩家移动
        if (this.joystick.isActive) {
            const moveInput = {
                x: this.joystick.moveX,
                y: this.joystick.moveY
            };
            this.player.move(moveInput);
        }
    }

    /**
     * 销毁虚拟摇杆
     */
    destroyVirtualJoystick() {
        // 移除事件监听器
        if (this.input) {
            this.input.off('pointerdown', this.onJoystickPointerDown, this);
            this.input.off('pointermove', this.onJoystickPointerMove, this);
            this.input.off('pointerup', this.onJoystickPointerUp, this);
        }

        // 销毁摇杆UI元素
        if (this.joystickBase) {
            this.joystickBase.destroy();
            this.joystickBase = null;
        }
        if (this.joystickKnob) {
            this.joystickKnob.destroy();
            this.joystickKnob = null;
        }

        // 清理摇杆数据
        this.joystick = null;

        console.log('虚拟摇杆已销毁');
    }
}