/**
 * 武器C
 * 产生破坏性区域
 *
 * 行为特点：
 * 1. 区域按顺时针方向依次生成，类似吸血鬼幸存者中的圣水武器
 * 2. 区域会缓慢向玩家移动，并在移动过程中逐渐变大
 * 3. 多个区域时，会均匀分布在玩家周围，整体按顺时针旋转
 */
class C extends Weapon {
    /**
     * 升级路径定义
     * 每个等级对应的升级效果和描述
     */
    static UPGRADE_PATH = [
        { level: 1, description: "基础C" },
        { level: 2, description: "射弹+1、范围+20%", effect: "增加1个区域和范围" },
        { level: 3, description: "伤害+10、持续时间+0.5s", effect: "增加伤害和持续时间" },
        { level: 4, description: "射弹+1、范围+20%", effect: "增加1个区域和范围" },
        { level: 5, description: "伤害+10、持续时间+0.3s", effect: "增加伤害和持续时间" },
        { level: 6, description: "射弹+1、范围+20%", effect: "增加1个区域和范围" },
        { level: 7, description: "伤害+5、持续时间+0.3s", effect: "增加伤害和持续时间" },
        { level: 8, description: "伤害+5、持续时间+0.3s", effect: "增加伤害和持续时间" }
    ];

    /**
     * 创建武器C
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {Player} player - 玩家对象
     * @param {boolean} isAcquired - 是否已获得武器，默认为false
     */
    constructor(scene, player, isAcquired = false) {
        super(scene, player, isAcquired);

        // 武器属性
        this.name = "C";
        this.damageMin = 10;        // 最小伤害值
        this.damageMax = 15;        // 最大伤害值
        this.cooldownTime = 4500;   // 冷却时间(毫秒)
        this.range = 30;            // 攻击范围(像素)，区域半径
        this.projectileCount = 1;   // 区域数量
        this.level = 1;             // 武器等级
        this.duration = 3000;       // 区域持续时间(毫秒)
        this.damageInterval = 500;  // 伤害间隔(毫秒)

        // 配置参数
        this.edgeDistance = 100;     // 固定值：圆边缘到玩家的距离
        this.growSpeed = 0.0005;    // 区域增长速度
        this.overlapRatio = 0.15;   // 重叠比例(15%)
        this.rotationSpeed = Math.PI / 6; // 整体旋转速度；值越大，旋转越快：如Math.PI / 6（30度/次） 值越小，旋转越慢：如Math.PI / 24（7.5度/次）

        // 创建区域组
        this.areaGroup = this.scene.physics.add.group();

        // 系统状态
        this.effectsLoaded = false; // 特效资源是否已加载完成
        this.debug = true;          // 调试标志
        this.lastStartAngle = 0;    // 上一次攻击的起始角度

        // 预加载武器图像
        this.preloadAssets();
    }

    /**
     * 预加载资源
     */
    preloadAssets() {
        // 加载特效图片
        for (let i = 1; i <= 7; i++) {
            const frameNumber = i.toString().padStart(3, '0');
            const key = `c_effect_${frameNumber}`;
            const path = `assets/fx/c/${frameNumber}.png`;

            // 如果纹理不存在，加载它
            if (!this.scene.textures.exists(key)) {
                this.scene.load.image(key, path);
            }
        }

        // 确保图片已加载
        this.scene.load.once('complete', () => {
            // 创建动画
            if (!this.scene.anims.exists('c_effect_anim')) {
                this.scene.anims.create({
                    key: 'c_effect_anim',
                    frames: Array.from({ length: 7 }, (_, i) => {
                        const frameNumber = (i + 1).toString().padStart(3, '0');
                        return { key: `c_effect_${frameNumber}` };
                    }),
                    frameRate: 12,
                    repeat: -1 // 循环播放
                });
            }

            // 标记特效资源已加载完成
            this.effectsLoaded = true;
            if (this.debug) console.log('武器C特效资源加载完成');
        });

        // 开始加载
        this.scene.load.start();
    }

    /**
     * 创建暗色渐变纹理
     * @param {string} key - 纹理键名
     * @param {number} size - 纹理大小
     * @returns {Phaser.Textures.Texture} 创建的纹理
     */
    createDarkGradientTexture(key, size) {
        // 如果纹理已存在，直接返回
        if (this.scene.textures.exists(key)) {
            return this.scene.textures.get(key);
        }

        // 创建图形对象
        const graphics = this.scene.add.graphics();
        const radius = size / 2;

        // 绘制从中心向外渐变的圆形
        for (let i = 0; i < radius; i++) {
            const alpha = 0.7 * (1 - (i / radius));
            graphics.fillStyle(0x000000, alpha);
            graphics.fillCircle(radius, radius, radius - i);
        }

        // 生成纹理并清理
        const texture = graphics.generateTexture(key, size, size);
        graphics.destroy();

        return texture;
    }

    /**
     * 攻击方法
     */
    attack() {
        if (!this.scene.enemiesGroup) return;

        // 计算基础伤害
        const damage = Phaser.Math.Between(this.damageMin, this.damageMax);

        // 根据区域数量创建多个区域，添加延迟使其按顺序出现
        const delayBetweenAreas = 100;

        if (this.debug) console.log(`武器C攻击，projectileCount = ${this.projectileCount}`);

        for (let i = 0; i < this.projectileCount; i++) {
            this.scene.time.delayedCall(i * delayBetweenAreas, () => {
                this.createDamageArea(damage, i);
            });
        }
    }

    /**
     * 创建伤害区域
     * @param {number} damage - 伤害值
     * @param {number} index - 区域索引，用于计算位置
     */
    createDamageArea(damage, index) {
        // 确定区域位置
        const position = this.calculateAreaPosition(index);

        // 创建区域精灵
        const area = this.areaGroup.create(
            position.x,
            position.y,
            this.effectsLoaded ? 'c_effect_001' : null
        );

        // 设置原点为中心点
        area.setOrigin(0.5, 0.5);

        // 设置区域属性
        area.damage = damage;
        area.damageInterval = this.damageInterval;
        area.affectedEnemies = new Set();
        area.moveSpeed = 5;
        area.growSpeed = this.growSpeed;

        // 碰撞设置
        const collisionRadius = 30;
        area.body.setCircle(collisionRadius);

        // 创建背景效果
        const darkTextureSize = collisionRadius * 2;
        const darkTexture = this.createDarkGradientTexture(`dark_area_${darkTextureSize}`, darkTextureSize);
        const darkBackground = this.scene.add.image(area.x, area.y, darkTexture.key);
        darkBackground.setOrigin(0.5, 0.5);

        // 计算并应用初始缩放
        const initialBackgroundScale = this.range / collisionRadius;
        darkBackground.setScale(initialBackgroundScale);
        darkBackground.initialScale = initialBackgroundScale;
        darkBackground.setAlpha(0.6);
        darkBackground.setDepth(DepthManager.LAYERS.EFFECTS);

        // 设置视觉效果
        if (this.effectsLoaded) {
            // 播放动画
            area.play('c_effect_anim');

            // 获取并计算特效缩放
            const effectFrame = this.scene.textures.getFrame('c_effect_001');
            const effectScaleX = (this.range * 2) / effectFrame.width;
            const effectScaleY = (this.range * 2) / effectFrame.height;

            // 应用初始和目标缩放
            area.setScale(effectScaleX, effectScaleY);
            area.initialScaleX = effectScaleX;
            area.initialScaleY = effectScaleY;
            area.targetScaleX = effectScaleX * 1.5;
            area.targetScaleY = effectScaleY * 1.5;
        } else {
            // 特效未加载时不显示临时图形
            area.setVisible(false);
        }

        // 设置深度
        area.setDepth(DepthManager.LAYERS.EFFECTS + 1);

        // 添加区域碰撞检测
        const collider = this.scene.physics.add.overlap(
            area,
            this.scene.enemiesGroup,
            this.onAreaDamageEnemy,
            null,
            this
        );

        // 设置伤害间隔定时器
        const damageTimer = this.scene.time.addEvent({
            delay: this.damageInterval,
            callback: () => {
                area.affectedEnemies.clear();
            },
            loop: true
        });

        // 添加更新函数，使区域向玩家移动并逐渐变大
        area.update = (_, delta) => {
            if (!area?.active || !this.player?.sprite) return;

            // 计算区域到玩家的向量
            const dx = this.player.sprite.x - area.x;
            const dy = this.player.sprite.y - area.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance > 10) {
                // 计算移动方向（从玩家向外移动）
                const dirX = -dx / distance;
                const dirY = -dy / distance;

                // 移动区域
                area.x += dirX * area.moveSpeed * (delta / 1000);
                area.y += dirY * area.moveSpeed * (delta / 1000);
                darkBackground.x = area.x;
                darkBackground.y = area.y;

                // 更新物理体位置
                if (area.body?.reset) {
                    area.body.reset(area.x, area.y);
                }

                // 随时间增大区域
                if (area.initialScaleX && area.initialScaleY) {
                    // 特效动画缩放
                    const growFactor = area.growSpeed * delta;
                    const newScaleX = Math.min(area.targetScaleX, area.scaleX + growFactor);
                    const newScaleY = Math.min(area.targetScaleY, area.scaleY + growFactor);
                    area.setScale(newScaleX, newScaleY);

                    // 背景缩放同步
                    const scaleRatio = newScaleX / area.initialScaleX;
                    darkBackground.setScale(darkBackground.initialScale * scaleRatio);
                } else {
                    // 简单纹理缩放
                    const newScale = Math.min(area.targetScale, area.scaleX + area.growSpeed * delta);
                    area.setScale(newScale);
                    darkBackground.setScale(darkBackground.initialScale * (newScale / area.initialScale));
                }
            }
        };

        // 将区域添加到场景的更新列表中
        this.scene.events.on('update', area.update);

        // 设置区域生命周期和淡出效果
        const fadeOutDuration = 300;

        // 背景渐变效果
        this.scene.tweens.add({
            targets: darkBackground,
            alpha: 0.1,
            duration: this.duration,
            ease: 'Linear'
        });

        // 设置区域消失
        this.scene.time.delayedCall(this.duration - fadeOutDuration, () => {
            this.scene.tweens.add({
                targets: [area, darkBackground],
                alpha: 0,
                duration: fadeOutDuration,
                onComplete: () => {
                    // 清理资源
                    damageTimer?.destroy();
                    collider?.destroy();

                    // 移除更新事件
                    if (area?.update) {
                        this.scene.events.off('update', area.update);
                    }

                    // 销毁对象
                    area?.active && area.destroy();
                    darkBackground?.active && darkBackground.destroy();
                }
            });
        });
    }

    /**
     * 计算区域位置
     * @param {number} index - 区域索引
     * @returns {Object} 包含x和y坐标的对象
     */
    calculateAreaPosition(index) {
        // 获取圆圈数量和半径
        const n = this.projectileCount;
        const r = this.range;

        // 计算圆环基础半径
        const ringRadius = this.edgeDistance + r;

        // 计算两个相切圆圈之间的角度
        const angleBetweenCircles = 2 * Math.asin(r / ringRadius);

        // 计算当前圆圈的角度位置 - 相邻排列而非均匀分布
        const angle = this.lastStartAngle + (index * angleBetweenCircles);

        // 计算坐标
        const x = this.player.sprite.x + Math.cos(angle) * ringRadius;
        const y = this.player.sprite.y + Math.sin(angle) * ringRadius;

        // 更新整体旋转角度 - 只在处理完所有圆圈后更新
        if (index === n - 1) {
            this.lastStartAngle = (this.lastStartAngle + this.rotationSpeed) % (Math.PI * 2);
        }

        if (this.debug && index === 0) {
            console.log(`相邻圆圈角度: ${(angleBetweenCircles * 180 / Math.PI).toFixed(1)}°, 圆环半径: ${ringRadius.toFixed(0)}, 圆圈数: ${n}`);
        }

        return { x, y };
    }

    /**
     * 区域对敌人造成伤害的回调
     * @param {Phaser.GameObjects.Sprite} area - 区域精灵
     * @param {Phaser.GameObjects.Sprite} enemySprite - 敌人精灵
     */
    onAreaDamageEnemy(area, enemySprite) {
        // 获取敌人对象
        const enemy = enemySprite.enemyInstance;
        if (!enemy) return;

        // 检查是否已经在当前间隔内受到过伤害
        if (area.affectedEnemies.has(enemy.id)) return;

        // 标记该敌人已受到伤害并造成伤害
        area.affectedEnemies.add(enemy.id);
        this.damageEnemy(enemy, area.damage);
    }

    /**
     * 升级武器
     * @returns {boolean} 是否成功升级
     */
    upgrade() {
        // 获取当前级别的升级设置
        const currentLevel = this.level;
        const nextLevel = currentLevel + 1;

        // 检查是否达到最大级别
        if (nextLevel > C.UPGRADE_PATH.length) {
            console.warn(`武器C已达到最高级别: ${currentLevel}`);
            return false;
        }

        // 获取升级效果
        const upgrade = C.UPGRADE_PATH[nextLevel - 1];

        // 应用升级效果
        if (upgrade.effect.includes("增加1个区域")) {
            this.projectileCount += 1;
        }

        if (upgrade.effect.includes("范围+20%") || upgrade.effect.includes("增加1个区域和范围")) {
            this.range *= 1.2;
        }

        if (upgrade.effect.includes("伤害+")) {
            const damageIncrease = parseInt(upgrade.effect.match(/伤害\+(\d+)/)[1]);
            this.damageMin += damageIncrease;
            this.damageMax += damageIncrease;
        }

        if (upgrade.effect.includes("持续时间+")) {
            const durationMatch = upgrade.effect.match(/持续时间\+(\d+\.\d+)s/);
            if (durationMatch) {
                const durationIncrease = parseFloat(durationMatch[1]) * 1000;
                this.duration += durationIncrease;
            }
        }

        // 升级成功，更新等级
        this.level = nextLevel;

        if (this.debug) {
            console.log(`武器C升级到${nextLevel}级`);
            console.log(`- 伤害: ${this.damageMin}-${this.damageMax}`);
            console.log(`- 区域数量: ${this.projectileCount}`);
            console.log(`- 范围: ${this.range}`);
            console.log(`- 持续时间: ${this.duration}ms`);
        }

        return true;
    }

    /**
     * 销毁武器
     */
    destroy() {
        // 清理区域组
        if (this.areaGroup) {
            // 移除每个区域的更新事件监听
            this.areaGroup.getChildren().forEach(area => {
                if (area.update) {
                    this.scene.events.off('update', area.update);
                }
            });

            // 清理区域组
            this.areaGroup.clear(true, true);
        }
    }
}