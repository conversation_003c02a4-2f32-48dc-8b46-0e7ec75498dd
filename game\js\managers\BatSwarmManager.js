/**
 * 蝙蝠群管理器
 * 管理所有蝙蝠群实例的生成、更新和销毁
 */
class BatSwarmManager {
    /**
     * 创建蝙蝠群管理器
     * @param {Phaser.Scene} scene - 游戏场景
     */
    constructor(scene) {
        this.scene = scene;
        this.batSwarms = []; // 存储所有活跃的蝙蝠群实例

        console.log('蝙蝠群管理器已初始化');
    }

    /**
     * 生成蝙蝠群突袭
     * @param {number} groupCount - 蝙蝠群组数
     */
    spawnBatSwarm(groupCount) {
        console.log(`开始生成蝙蝠群突袭: ${groupCount}组`);

        if (!this.scene.player || !this.scene.player.sprite) {
            console.warn('玩家不存在，无法生成蝙蝠群');
            return;
        }

        const camera = this.scene.cameras.main;
        const playerX = this.scene.player.sprite.x;
        const playerY = this.scene.player.sprite.y;

        // 4个方向的边缘生成配置
        const directions = [
            { // 左到右
                startX: playerX - camera.width,
                startY: playerY + (Math.random() - 0.5) * camera.height,
                endX: playerX + camera.width,
                endY: playerY + (Math.random() - 0.5) * camera.height
            },
            { // 上到下
                startX: playerX + (Math.random() - 0.5) * camera.width,
                startY: playerY - camera.height,
                endX: playerX + (Math.random() - 0.5) * camera.width,
                endY: playerY + camera.height
            },
            { // 右到左
                startX: playerX + camera.width,
                startY: playerY + (Math.random() - 0.5) * camera.height,
                endX: playerX - camera.width,
                endY: playerY + (Math.random() - 0.5) * camera.height
            },
            { // 下到上
                startX: playerX + (Math.random() - 0.5) * camera.width,
                startY: playerY + camera.height,
                endX: playerX + (Math.random() - 0.5) * camera.width,
                endY: playerY - camera.height
            }
        ];

        // 生成每组蝙蝠群
        for (let i = 0; i < groupCount; i++) {
            const direction = directions[i % 4];
            const batsPerGroup = 15 + Math.floor(Math.random() * 6); // 15-20个蝙蝠

            console.log(`生成第${i + 1}组蝙蝠群: ${batsPerGroup}个蝙蝠`);

            // 在该方向生成一组蝙蝠
            for (let j = 0; j < batsPerGroup; j++) {
                // 在起始线上随机分布
                const startX = direction.startX + (Math.random() - 0.5) * 200;
                const startY = direction.startY + (Math.random() - 0.5) * 200;

                // 目标位置也有一些随机偏移
                const endX = direction.endX + (Math.random() - 0.5) * 200;
                const endY = direction.endY + (Math.random() - 0.5) * 200;

                // 随机选择蝙蝠类型
                const batType = Math.random() < 0.5 ? 'E000a' : 'E000b';

                // 创建蝙蝠实例
                const bat = new BatSwarm(this.scene, startX, startY, endX, endY, batType);
                this.batSwarms.push(bat);
            }
        }

        console.log(`蝙蝠群突袭生成完成: 总共${this.batSwarms.length}个蝙蝠`);
    }

    /**
     * 更新所有蝙蝠群
     * @param {number} deltaTime - 时间增量(秒)
     */
    update(deltaTime) {
        // 获取玩家位置
        const playerPosition = this.scene.player && this.scene.player.sprite ?
            { x: this.scene.player.sprite.x, y: this.scene.player.sprite.y } :
            { x: 0, y: 0 };

        // 更新所有蝙蝠，移除已销毁的
        for (let i = this.batSwarms.length - 1; i >= 0; i--) {
            const bat = this.batSwarms[i];

            // 检查蝙蝠是否还存活
            if (!bat.isAlive || !bat.sprite || !bat.sprite.active) {
                this.batSwarms.splice(i, 1);
                continue;
            }

            // 更新蝙蝠
            bat.update(playerPosition);
        }
    }

    /**
     * 清除所有蝙蝠群
     */
    clearAll() {
        console.log(`清除所有蝙蝠群: ${this.batSwarms.length}个`);

        for (const bat of this.batSwarms) {
            bat.destroy();
        }

        this.batSwarms = [];
    }

    /**
     * 获取当前蝙蝠群数量
     */
    getBatCount() {
        return this.batSwarms.length;
    }
}
