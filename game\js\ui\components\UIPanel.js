/**
 * UI面板组件
 * 统一的面板容器组件，支持背景、边框和子组件管理
 */
class UIPanel extends UIComponent {
    constructor(scene, config = {}) {
        super(scene, config);
        
        // 面板特有属性
        this.backgroundColor = config.backgroundColor || 0x000000;
        this.alpha = config.alpha !== undefined ? config.alpha : 0.7;
        this.borderColor = config.borderColor || 0x666666;
        this.borderWidth = config.borderWidth || 2;
        this.cornerRadius = config.cornerRadius || 0;
        
        // 子组件管理
        this.children = [];
        this.childrenById = new Map();
        
        // 布局选项
        this.padding = config.padding || { top: 10, right: 10, bottom: 10, left: 10 };
        this.layout = config.layout || 'none'; // 'none', 'vertical', 'horizontal', 'grid'
        this.spacing = config.spacing || 10;
        
        // Phaser对象
        this.background = null;
        this.border = null;
        this.mask = null;
        
        // 是否可拖拽
        this.draggable = config.draggable || false;
        this.isDragging = false;
        this.dragStartX = 0;
        this.dragStartY = 0;
        
        this.create();
    }

    /**
     * 创建面板
     */
    create() {
        // 创建背景
        if (this.cornerRadius > 0) {
            // 使用图形对象创建圆角背景
            this.background = this.scene.add.graphics();
            this.background.fillStyle(this.backgroundColor, this.alpha);
            this.background.fillRoundedRect(
                this.x - this.width / 2,
                this.y - this.height / 2,
                this.width,
                this.height,
                this.cornerRadius
            );
        } else {
            // 使用矩形创建背景
            this.background = this.scene.add.rectangle(
                this.x,
                this.y,
                this.width,
                this.height,
                this.backgroundColor,
                this.alpha
            );
        }
        
        this.addGameObject(this.background);
        
        // 创建边框
        if (this.borderWidth > 0) {
            if (this.cornerRadius > 0) {
                this.border = this.scene.add.graphics();
                this.border.lineStyle(this.borderWidth, this.borderColor);
                this.border.strokeRoundedRect(
                    this.x - this.width / 2,
                    this.y - this.height / 2,
                    this.width,
                    this.height,
                    this.cornerRadius
                );
            } else {
                this.border = this.scene.add.rectangle(
                    this.x,
                    this.y,
                    this.width + this.borderWidth,
                    this.height + this.borderWidth,
                    this.borderColor,
                    0
                ).setStrokeStyle(this.borderWidth, this.borderColor);
            }
            
            this.addGameObject(this.border);
        }
        
        // 设置拖拽
        if (this.draggable) {
            this.setupDragging();
        }
        
        console.log(`面板创建完成: ${this.width}x${this.height}`);
    }

    /**
     * 设置拖拽功能
     */
    setupDragging() {
        if (!this.background) return;
        
        this.background.setInteractive();
        
        this.background.on('pointerdown', (pointer) => {
            this.isDragging = true;
            this.dragStartX = pointer.x - this.x;
            this.dragStartY = pointer.y - this.y;
            this.emit('panel:dragStart', this);
        });
        
        this.scene.input.on('pointermove', (pointer) => {
            if (this.isDragging) {
                const newX = pointer.x - this.dragStartX;
                const newY = pointer.y - this.dragStartY;
                this.setPosition(newX, newY);
                this.emit('panel:drag', this);
            }
        });
        
        this.scene.input.on('pointerup', () => {
            if (this.isDragging) {
                this.isDragging = false;
                this.emit('panel:dragEnd', this);
            }
        });
    }

    /**
     * 添加子组件
     * @param {UIComponent} child - 子组件
     * @param {string} id - 子组件ID（可选）
     */
    addChild(child, id = null) {
        if (!child) return;
        
        this.children.push(child);
        
        if (id) {
            this.childrenById.set(id, child);
        }
        
        // 设置子组件的父面板
        child.parent = this;
        
        // 应用布局
        this.applyLayout();
        
        console.log(`子组件已添加到面板: ${id || 'unnamed'}`);
    }

    /**
     * 移除子组件
     * @param {UIComponent|string} childOrId - 子组件或ID
     */
    removeChild(childOrId) {
        let child;
        
        if (typeof childOrId === 'string') {
            child = this.childrenById.get(childOrId);
            this.childrenById.delete(childOrId);
        } else {
            child = childOrId;
        }
        
        if (child) {
            const index = this.children.indexOf(child);
            if (index !== -1) {
                this.children.splice(index, 1);
                child.parent = null;
                this.applyLayout();
                console.log(`子组件已从面板移除`);
            }
        }
    }

    /**
     * 获取子组件
     * @param {string} id - 子组件ID
     * @returns {UIComponent|null} 子组件
     */
    getChild(id) {
        return this.childrenById.get(id) || null;
    }

    /**
     * 获取所有子组件
     * @returns {Array} 子组件数组
     */
    getAllChildren() {
        return [...this.children];
    }

    /**
     * 应用布局
     */
    applyLayout() {
        if (this.layout === 'none' || this.children.length === 0) return;
        
        const contentX = this.x - this.width / 2 + this.padding.left;
        const contentY = this.y - this.height / 2 + this.padding.top;
        const contentWidth = this.width - this.padding.left - this.padding.right;
        const contentHeight = this.height - this.padding.top - this.padding.bottom;
        
        switch (this.layout) {
            case 'vertical':
                this.applyVerticalLayout(contentX, contentY, contentWidth, contentHeight);
                break;
            case 'horizontal':
                this.applyHorizontalLayout(contentX, contentY, contentWidth, contentHeight);
                break;
            case 'grid':
                this.applyGridLayout(contentX, contentY, contentWidth, contentHeight);
                break;
        }
    }

    /**
     * 应用垂直布局
     */
    applyVerticalLayout(startX, startY, width, height) {
        const totalSpacing = (this.children.length - 1) * this.spacing;
        const availableHeight = height - totalSpacing;
        const itemHeight = availableHeight / this.children.length;
        
        this.children.forEach((child, index) => {
            const y = startY + index * (itemHeight + this.spacing) + itemHeight / 2;
            child.setPosition(startX + width / 2, y);
        });
    }

    /**
     * 应用水平布局
     */
    applyHorizontalLayout(startX, startY, width, height) {
        const totalSpacing = (this.children.length - 1) * this.spacing;
        const availableWidth = width - totalSpacing;
        const itemWidth = availableWidth / this.children.length;
        
        this.children.forEach((child, index) => {
            const x = startX + index * (itemWidth + this.spacing) + itemWidth / 2;
            child.setPosition(x, startY + height / 2);
        });
    }

    /**
     * 应用网格布局
     */
    applyGridLayout(startX, startY, width, height) {
        // 简单的网格布局实现
        const cols = Math.ceil(Math.sqrt(this.children.length));
        const rows = Math.ceil(this.children.length / cols);
        
        const cellWidth = width / cols;
        const cellHeight = height / rows;
        
        this.children.forEach((child, index) => {
            const col = index % cols;
            const row = Math.floor(index / cols);
            
            const x = startX + col * cellWidth + cellWidth / 2;
            const y = startY + row * cellHeight + cellHeight / 2;
            
            child.setPosition(x, y);
        });
    }

    /**
     * 设置位置（重写父类方法）
     */
    setPosition(x, y) {
        const deltaX = x - this.x;
        const deltaY = y - this.y;
        
        super.setPosition(x, y);
        
        // 更新背景和边框位置
        if (this.background) {
            if (this.cornerRadius > 0) {
                this.background.clear();
                this.background.fillStyle(this.backgroundColor, this.alpha);
                this.background.fillRoundedRect(
                    x - this.width / 2,
                    y - this.height / 2,
                    this.width,
                    this.height,
                    this.cornerRadius
                );
            } else {
                this.background.setPosition(x, y);
            }
        }
        
        if (this.border) {
            if (this.cornerRadius > 0) {
                this.border.clear();
                this.border.lineStyle(this.borderWidth, this.borderColor);
                this.border.strokeRoundedRect(
                    x - this.width / 2,
                    y - this.height / 2,
                    this.width,
                    this.height,
                    this.cornerRadius
                );
            } else {
                this.border.setPosition(x, y);
            }
        }
        
        // 移动所有子组件
        this.children.forEach(child => {
            child.setPosition(child.x + deltaX, child.y + deltaY);
        });
        
        return this;
    }

    /**
     * 应用样式
     * @param {Object} style - 样式配置
     */
    applyStyle(style) {
        if (style.backgroundColor !== undefined) {
            this.backgroundColor = style.backgroundColor;
        }
        
        if (style.alpha !== undefined) {
            this.alpha = style.alpha;
        }
        
        if (style.borderColor !== undefined) {
            this.borderColor = style.borderColor;
        }
        
        if (style.borderWidth !== undefined) {
            this.borderWidth = style.borderWidth;
        }
        
        // 重新创建背景和边框
        if (this.background) {
            this.background.destroy();
            this.background = null;
        }
        if (this.border) {
            this.border.destroy();
            this.border = null;
        }
        
        this.create();
    }

    /**
     * 销毁面板
     */
    destroy() {
        // 销毁所有子组件
        this.children.forEach(child => {
            if (child && child.destroy) {
                child.destroy();
            }
        });
        
        this.children = [];
        this.childrenById.clear();
        
        super.destroy();
    }
}
