class LoadingScene extends Phaser.Scene {
    constructor() {
        super({ key: 'LoadingScene' });
        this.assetsLoaded = false;
    }

    preload() {
        // 预加载基础资源
        console.log('LoadingScene: 开始预加载基础资源');
    }

    create() {
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;

        // 添加纯色背景
        this.add.rectangle(
            width / 2,
            height / 2,
            width,
            height,
            0x2f4f2f  // 使用深绿色背景
        );

        // 创建加载文本
        this.loadingText = this.add.text(
            width / 2,
            height / 2 - 50,
            '正在加载游戏资源...',
            {
                font: 'bold 24px Arial',
                fill: '#ffffff'
            }
        ).setOrigin(0.5);

        // 创建进度条背景
        this.progressBarBg = this.add.rectangle(
            width / 2,
            height / 2,
            400,
            30,
            0x333333
        );

        // 创建进度条填充
        this.progressBar = this.add.rectangle(
            width / 2 - 195,
            height / 2,
            10,
            20,
            0x00ff00
        ).setOrigin(0, 0.5);

        // 创建进度文本
        this.progressText = this.add.text(
            width / 2,
            height / 2 + 50,
            '0%',
            {
                font: '20px Arial',
                fill: '#ffffff'
            }
        ).setOrigin(0.5);

        // 创建开始战斗按钮（初始隐藏）
        const buttonWidth = 200;
        const buttonHeight = 60;

        // 按钮背景
        this.startButton = this.add.rectangle(
            width / 2,
            height / 2,
            buttonWidth,
            buttonHeight,
            0x3498db  // 使用蓝色
        ).setInteractive().setVisible(false);

        // 按钮文字
        this.buttonText = this.add.text(
            width / 2,
            height / 2,
            '开始战斗',
            {
                font: 'bold 24px Arial',
                fill: '#ffffff'
            }
        ).setOrigin(0.5).setVisible(false);

        // 添加按钮交互效果
        this.startButton.on('pointerover', () => {
            this.startButton.fillColor = 0x2980b9; // 悬停时颜色变深
            this.game.canvas.style.cursor = 'pointer';
        });

        this.startButton.on('pointerout', () => {
            this.startButton.fillColor = 0x3498db; // 恢复原色
            this.game.canvas.style.cursor = 'default';
        });

        // 点击按钮时切换到游戏场景
        this.startButton.on('pointerdown', () => {
            this.scene.start('GameScene');
        });

        // 开始加载资源
        this.loadAllAssets();
    }

    /**
     * 加载所有游戏资源
     */
    loadAllAssets() {
        // 加载基础图片资源
        this.load.image('background', 'assets/images/floor/floor_plain.png');
        this.load.image('player_boy_1', 'assets/images/player_boy/boy_01.png');
        this.load.image('player_boy_2', 'assets/images/player_boy/boy_02.png');
        this.load.image('player_boy_3', 'assets/images/player_boy/boy_03.png');
        this.load.image('player_boy_4', 'assets/images/player_boy/boy_04.png');

        // 加载UI资源
        this.load.image('card', 'assets/ui/choose/card.png');
        this.load.image('exp', 'assets/ui/exp.png');
        this.load.image('expin', 'assets/ui/expin.png');
        this.load.image('ui_heart_full', 'assets/other/ui_heart_full.png');

        // 加载武器图标
        this.load.image('a', 'assets/Icons/a.png');
        this.load.image('b', 'assets/Icons/b.png');
        this.load.image('c', 'assets/Icons/c.png');
        this.load.image('d', 'assets/Icons/d.png');

        // 加载属性图标
        this.load.image('spd', 'assets/Icons/spd.png');
        this.load.image('big', 'assets/Icons/big.png');
        this.load.image('atk', 'assets/Icons/atk.png');
        this.load.image('atkspd', 'assets/Icons/atkspd.png');
        this.load.image('aoe', 'assets/Icons/aoe.png');

        // 加载武器效果资源
        this.loadWeaponEffects();

        // 加载宠物资源
        this.loadPetAssets();

        // 添加资源加载错误处理
        this.load.on('loaderror', (fileObj) => {
            console.error(`资源加载失败: ${fileObj.key} (${fileObj.url})`);
        });

        // 监听加载进度
        this.load.on('progress', (value) => {
            // 更新进度条宽度
            this.progressBar.width = 390 * value;

            // 更新进度文本
            this.progressText.setText(`${Math.floor(value * 100)}%`);
        });

        // 监听加载完成
        this.load.on('complete', () => {
            console.log('所有资源加载完成');

            // 检查关键武器资源是否成功加载
            const checkWeapons = ['a', 'b', 'c', 'd'];
            checkWeapons.forEach(weapon => {
                const firstFrameKey = `${weapon}_effect_${weapon === 'd' ? '01' : '001'}`;
                if (this.textures.exists(firstFrameKey)) {
                    console.log(`武器 ${weapon} 资源加载成功`);
                } else {
                    console.warn(`武器 ${weapon} 资源似乎未成功加载`);
                }
            });

            // 创建宠物动画
            this.createPetAnimations();

            this.assetsLoaded = true;

            // 隐藏加载UI
            this.loadingText.setVisible(false);
            this.progressBarBg.setVisible(false);
            this.progressBar.setVisible(false);
            this.progressText.setVisible(false);

            // 显示开始按钮
            this.startButton.setVisible(true);
            this.buttonText.setVisible(true);
        });

        // 开始加载
        this.load.start();
    }

    /**
     * 加载武器效果资源
     */
    loadWeaponEffects() {
        // 武器A
        console.log('开始加载武器A效果资源');
        try {
            for (let i = 1; i <= 5; i++) {
                const numStr = i.toString().padStart(3, '0');
                const key = `a_effect_${numStr}`;
                const path = `assets/fx/a/a_${numStr}.png`;
                this.load.image(key, path);
            }
        } catch (error) {
            console.error('武器A效果加载失败:', error);
        }

        // 武器B
        console.log('开始加载武器B效果资源');
        try {
            for (let i = 1; i <= 4; i++) {
                const numStr = i.toString().padStart(3, '0');
                const key = `b_effect_${numStr}`;
                const path = `assets/fx/b/${numStr}.png`;
                this.load.image(key, path);
            }
        } catch (error) {
            console.error('武器B效果加载失败:', error);
        }

        // 武器C
        console.log('开始加载武器C效果资源');
        try {
            for (let i = 1; i <= 7; i++) {
                const numStr = i.toString().padStart(3, '0');
                const key = `c_effect_${numStr}`;
                const path = `assets/fx/c/${numStr}.png`;
                this.load.image(key, path);
            }
        } catch (error) {
            console.error('武器C效果加载失败:', error);
        }

        // 武器D
        console.log('开始加载武器D效果资源');
        try {
            for (let i = 1; i <= 20; i++) {
                const numStr = i.toString().padStart(2, '0');
                const key = `d_effect_${numStr}`;
                const path = `assets/fx/d/Gravity-Sheet_${numStr}.png`;
                this.load.image(key, path);
            }
        } catch (error) {
            console.error('武器D效果加载失败:', error);
        }
    }

    /**
     * 加载宠物资源
     */
    loadPetAssets() {
        console.log('开始加载宠物资源');

        // 宠物001 - 猫咪宠物（精灵表）
        this.load.spritesheet('pet001_sprite', 'assets/pets/character01/JumpCattttt.png', {
            frameWidth: 32,
            frameHeight: 32
        });
        console.log('加载精灵表: pet001_sprite');

        // 宠物002 - 蘑菇宠物（多帧图片）
        for (let i = 1; i <= 8; i++) {
            const key = `pet_pet002_${i}`;
            const path = `assets/pets/character02/slm_0${i}.png`;
            this.load.image(key, path);
            console.log(`加载多帧图片: ${key}, 路径: ${path}`);
        }

        // 宠物003 - 哈比人宠物（多帧图片）
        for (let i = 1; i <= 10; i++) {
            const key = `pet_pet003_${i}`;
            const path = `assets/pets/character03/Hobbit - run${i}.png`;
            this.load.image(key, path);
            console.log(`加载多帧图片: ${key}, 路径: ${path}`);
        }

        // 宠物004 - Godot宠物（多帧图片）
        for (let i = 1; i <= 4; i++) {
            const key = `pet_pet004_${i}`;
            const path = `assets/pets/character04/big_f${i}.png`;
            this.load.image(key, path);
            console.log(`加载多帧图片: ${key}, 路径: ${path}`);
        }

        // 宠物005 - 蘑菇奔跑宠物（多帧图片）
        for (let i = 1; i <= 8; i++) {
            const key = `pet_pet005_${i}`;
            const path = `assets/pets/character05/D-_Users_tur_Desktop_Mushroom-Run_0${i}.png`;
            this.load.image(key, path);
            console.log(`加载多帧图片: ${key}, 路径: ${path}`);
        }
    }

    /**
     * 创建宠物动画
     */
    createPetAnimations() {
        // 宠物001 - 猫咪宠物（精灵表）
        if (!this.anims.exists('pet001_anim')) {
            this.anims.create({
                key: 'pet001_anim',
                frames: this.anims.generateFrameNumbers('pet001_sprite', {
                    start: 0,
                    end: 12 // 13帧动画
                }),
                frameRate: 10,
                repeat: -1
            });
            console.log('创建宠物动画: pet001_anim, 使用精灵表');
        }

        // 宠物002 - 蘑菇宠物（多帧图片）
        if (!this.anims.exists('pet002_anim')) {
            this.anims.create({
                key: 'pet002_anim',
                frames: Array.from({ length: 8 }, (_, i) => ({
                    key: `pet_pet002_${i + 1}`
                })),
                frameRate: 10,
                repeat: -1
            });
            console.log('创建宠物动画: pet002_anim, 帧数: 8');
        }

        // 宠物003 - 哈比人宠物（多帧图片）
        if (!this.anims.exists('pet_pet003_anim')) {
            this.anims.create({
                key: 'pet_pet003_anim',
                frames: Array.from({ length: 10 }, (_, i) => ({
                    key: `pet_pet003_${i + 1}`
                })),
                frameRate: 10,
                repeat: -1
            });
            console.log('创建宠物动画: pet_pet003_anim, 帧数: 10');
        }

        // 宠物004 - Godot宠物（多帧图片）
        if (!this.anims.exists('pet_pet004_anim')) {
            this.anims.create({
                key: 'pet_pet004_anim',
                frames: Array.from({ length: 4 }, (_, i) => ({
                    key: `pet_pet004_${i + 1}`
                })),
                frameRate: 10,
                repeat: -1
            });
            console.log('创建宠物动画: pet_pet004_anim, 帧数: 4');
        }

        // 宠物005 - 蘑菇奔跑宠物（多帧图片）
        if (!this.anims.exists('pet005_anim')) {
            this.anims.create({
                key: 'pet005_anim',
                frames: Array.from({ length: 8 }, (_, i) => ({
                    key: `pet_pet005_${i + 1}`
                })),
                frameRate: 10,
                repeat: -1
            });
            console.log('创建宠物动画: pet005_anim, 帧数: 8');
        }
    }
}
