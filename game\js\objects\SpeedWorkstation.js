/**
 * 速度工作台类
 * 继承通用工作台基类
 */
class SpeedWorkstation extends BaseWorkstation {
    constructor(scene, x, y) {
        super(scene, x, y);
    }

    /**
     * 获取工作台静态配置
     * @returns {Object} 工作台配置信息
     */
    static getConfig() {
        return {
            id: 'speed_workstation',
            name: '速度工作台',
            cost: 2,
            buffValue: 0.2, // +20%移动速度
            buffType: 'moveSpeed',
            description: '提升移动速度 +20%',
            spriteKey: 'speed_workstation',
            spritePath: 'assets/tools/Alchemy_Table_03-Sheet.png',
            petId: 'pet001',
            frameConfig: {
                frameWidth: 48,
                frameHeight: 64
            },
            scale: 3
        };
    }

    /**
     * 获取宠物显示配置
     */
    getPetDisplayConfig() {
        return {
            offsetX: 20,
            offsetY: 100,
            scale: 2.3,
            flipX: true, // 添加翻转配置
            originX: 0.5,
            originY: 0.8,
            resourceType: 'spritesheet',
            textureKey: 'pet001_sprite',
            path: 'assets/pets/character01/JumpCattttt.png',
            frameConfig: { frameWidth: 32, frameHeight: 32 },
            frames: 13,
            animKey: 'pet001_speed_anim',
            frameRate: 8
        };
    }

    /**
     * 获取碰撞配置
     */
    getCollisionConfig() {
        return {
            type: 'rectangle',
            width: 140,
            height: 90,
            offsetX: 5,
            offsetY: 50
        };
    }
}
