/**
 * 血量道具类
 * 玩家拾取后恢复血量
 */
class HealthPotion {
    /** 缩放比例 */
    static SCALE = 2.0;

    /** 碰撞箱设置 */
    static COLLISION_BOX = {
        radius: 15,
        offsetX: 0,
        offsetY: 0
    };

    /**
     * 创建血量道具
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} healAmount - 恢复血量
     */
    constructor(scene, x, y, healAmount = 25) {
        this.scene = scene;
        this.healAmount = healAmount;
        this.type = 'healthPotion';

        // 创建精灵，使用心形图标
        this.sprite = scene.physics.add.sprite(x, y, 'ui_heart_full');
        this.sprite.setScale(HealthPotion.SCALE);
        this.sprite.setScrollFactor(1);

        // 设置物理属性
        this.sprite.body.setCircle(
            HealthPotion.COLLISION_BOX.radius,
            HealthPotion.COLLISION_BOX.offsetX,
            HealthPotion.COLLISION_BOX.offsetY
        );

        // 添加引用
        this.sprite.healthPotionInstance = this;
        this.sprite.customType = 'HealthPotion';

        // 使用DepthManager设置深度
        if (typeof DepthManager !== 'undefined') {
            DepthManager.updateDepth(this.sprite, 'Item', true);
        }

        console.log(`血量道具创建在(${x}, ${y})，恢复量：${healAmount}`);
    }

    /**
     * 被玩家收集
     */
    collect() {
        if (!this.sprite || !this.sprite.active) return;

        // 恢复玩家血量
        if (this.scene.player && this.scene.player.healHealth) {
            this.scene.player.healHealth(this.healAmount);
        }

        console.log(`血量道具被收集，恢复${this.healAmount}点血量`);

        // 销毁道具
        this.destroy();
    }

    /**
     * 销毁道具
     */
    destroy() {
        if (this.sprite && this.sprite.active) {
            this.sprite.destroy();
        }
    }
}
