/**
 * 模块加载器 - 统一管理所有JS文件的加载
 * 彻底解决"XXX is not defined"错误
 */
class ModuleLoader {
    constructor() {
        this.loadedModules = new Set();
        this.loadingPromises = new Map();
        this.dependencyGraph = new Map();
        this.loadOrder = [];
        this.errorHandlers = [];

        // 定义模块加载顺序配置
        this.moduleConfig = {
            // 第一层：基础工具类（无依赖）
            core: [
                'js/managers/DepthManager.js',
                'js/managers/SaveManager.js',
                'js/managers/GameData.js',
                'js/utils/InputManager.js',
                'js/utils/BuffUtils.js'
            ],

            // 第二层：管理器类
            managers: [
                'js/managers/BatSwarmManager.js',
                'js/utils/WeaponUpgradeUtils.js',
                'js/utils/UpgradeOptionGenerator.js'
            ],

            // 第三层：基础实体类
            entities: [
                'js/entities/Player.js',
                'js/entities/Enemy.js',
                'js/entities/BatSwarm.js'
            ],

            // 第四层：武器系统
            weapons: [
                'js/weapons/Weapon.js',
                'js/weapons/A.js',
                'js/weapons/A1.js',
                'js/weapons/B.js',
                'js/weapons/B1.js',
                'js/weapons/C.js',
                'js/weapons/C1.js',
                'js/weapons/D.js',
                'js/weapons/D1.js',
                'js/weapons/WeaponManager.js'
            ],

            // 第五层：宠物系统
            pets: [
                'js/pets/Pet.js',
                'js/pets/Pet001.js',
                'js/pets/Pet002.js',
                'js/pets/Pet003.js',
                'js/pets/Pet004.js',
                'js/pets/PetManager.js'
            ],

            // 第六层：具体敌人类型
            enemyTypes: [
                'js/entities/000a.js',
                'js/entities/000b.js',
                'js/entities/001.js',
                'js/entities/002.js',
                'js/entities/003.js',
                'js/entities/004.js',
                'js/entities/006.js',
                'js/entities/007.js',
                'js/entities/008.js',
                'js/entities/009.js',
                'js/entities/011.js',
                'js/entities/012.js',
                'js/entities/013.js',
                'js/entities/015.js'
            ],

            // 第七层：物品和工作台
            objects: [
                'js/objects/StaticObject.js',
                'js/objects/tools/Axe.js',
                'js/objects/BaseWorkstation.js',
                'js/objects/HealthWorkstation.js',
                'js/objects/SpeedWorkstation.js',
                'js/objects/AttackWorkstation.js',
                'js/objects/RangeWorkstation.js',
                'js/items/Coin.js',
                'js/items/ExperienceGem.js',
                'js/items/Diamond.js',
                'js/items/InGameItems.js',
                'js/items/GoldCoin.js',
                'js/items/HealthPotion.js'
            ],

            // 第八层：UI系统
            ui: [
                'js/ui/UIEventManager.js',
                'js/ui/UITheme.js',
                'js/ui/UIComponent.js',
                'js/ui/components/UIButton.js',
                'js/ui/components/UIText.js',
                'js/ui/components/UIProgressBar.js',
                'js/ui/components/UIPanel.js',
                'js/ui/UIManager.js',
                'js/ui/UpgradeModal.js'
            ],

            // 第九层：游戏场景
            scenes: [
                'js/scenes/StartScene.js',
                'js/scenes/LoadingScene.js',
                'js/scenes/MainMenuScene.js',
                'js/scenes/HomeScene.js',
                'js/scenes/PetShopScene.js',
                'js/scenes/GameScene.js'
            ]
        };

        console.log('ModuleLoader初始化完成');
    }

    /**
     * 加载单个模块
     * @param {string} modulePath - 模块路径
     * @returns {Promise} 加载Promise
     */
    loadModule(modulePath) {
        // 如果已经加载过，直接返回
        if (this.loadedModules.has(modulePath)) {
            return Promise.resolve();
        }

        // 如果正在加载，返回现有的Promise
        if (this.loadingPromises.has(modulePath)) {
            return this.loadingPromises.get(modulePath);
        }

        // 创建新的加载Promise
        const loadPromise = new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = modulePath;
            script.async = false; // 保证按顺序执行

            script.onload = () => {
                this.loadedModules.add(modulePath);
                this.loadingPromises.delete(modulePath);
                console.log(`模块加载成功: ${modulePath}`);
                resolve();
            };

            script.onerror = (error) => {
                this.loadingPromises.delete(modulePath);
                const errorMsg = `模块加载失败: ${modulePath}`;
                console.error(errorMsg, error);
                this.handleLoadError(modulePath, error);
                reject(new Error(errorMsg));
            };

            document.head.appendChild(script);
        });

        this.loadingPromises.set(modulePath, loadPromise);
        return loadPromise;
    }

    /**
     * 按层级顺序加载所有模块
     * @returns {Promise} 加载完成Promise
     */
    async loadAllModules() {
        console.log('开始按顺序加载所有模块...');

        try {
            // 按配置的顺序逐层加载
            for (const [layerName, modules] of Object.entries(this.moduleConfig)) {
                console.log(`正在加载 ${layerName} 层模块...`);

                // 并行加载同一层的所有模块
                const layerPromises = modules.map(modulePath => this.loadModule(modulePath));
                await Promise.all(layerPromises);

                console.log(`${layerName} 层模块加载完成`);
            }

            console.log('所有模块加载完成！');
            this.validateLoadedModules();

        } catch (error) {
            console.error('模块加载过程中出现错误:', error);
            throw error;
        }
    }

    /**
     * 验证关键模块是否正确加载
     */
    validateLoadedModules() {
        const criticalClasses = [
            'DepthManager', 'SaveManager', 'GameData', 'Player', 'Enemy',
            'UIManager', 'StartScene', 'GameScene', 'MainMenuScene'
        ];

        const missingClasses = [];

        criticalClasses.forEach(className => {
            if (typeof window[className] === 'undefined') {
                missingClasses.push(className);
            }
        });

        if (missingClasses.length > 0) {
            const errorMsg = `关键类未定义: ${missingClasses.join(', ')}`;
            console.error(errorMsg);
            this.handleValidationError(missingClasses);
        } else {
            console.log('所有关键模块验证通过');
        }
    }

    /**
     * 处理加载错误
     * @param {string} modulePath - 失败的模块路径
     * @param {Error} error - 错误对象
     */
    handleLoadError(modulePath, error) {
        // 尝试从备用路径加载
        console.log(`尝试从备用路径加载: ${modulePath}`);

        // 通知错误处理器
        this.errorHandlers.forEach(handler => {
            try {
                handler(modulePath, error);
            } catch (e) {
                console.error('错误处理器执行失败:', e);
            }
        });
    }

    /**
     * 处理验证错误
     * @param {Array} missingClasses - 缺失的类列表
     */
    handleValidationError(missingClasses) {
        // 显示用户友好的错误信息
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #ff4444;
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 10000;
            font-family: Arial, sans-serif;
            text-align: center;
        `;
        errorDiv.innerHTML = `
            <h3>游戏加载错误</h3>
            <p>以下模块加载失败:</p>
            <p><strong>${missingClasses.join(', ')}</strong></p>
            <p>请刷新页面重试</p>
            <button onclick="location.reload()" style="
                background: white;
                color: #ff4444;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                margin-top: 10px;
            ">刷新页面</button>
        `;
        document.body.appendChild(errorDiv);
    }

    /**
     * 添加错误处理器
     * @param {Function} handler - 错误处理函数
     */
    addErrorHandler(handler) {
        this.errorHandlers.push(handler);
    }

    /**
     * 检查模块是否已加载
     * @param {string} modulePath - 模块路径
     * @returns {boolean} 是否已加载
     */
    isModuleLoaded(modulePath) {
        return this.loadedModules.has(modulePath);
    }

    /**
     * 获取加载状态
     * @returns {Object} 加载状态信息
     */
    getLoadStatus() {
        const totalModules = Object.values(this.moduleConfig).flat().length;
        const loadedCount = this.loadedModules.size;

        return {
            total: totalModules,
            loaded: loadedCount,
            progress: loadedCount / totalModules,
            isComplete: loadedCount === totalModules
        };
    }
}

// 创建全局模块加载器实例
window.moduleLoader = new ModuleLoader();
