/**
 * 宠物001
 */
class Pet001 extends Pet {
    /** 缩放比例 */
    static SCALE = 2.3;

    /** 宠物间距 */
    static SPACING = 35;

    /**
     * 创建喵
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {Player|Pet} target - 跟随目标（玩家或其他宠物）
     * @param {Object} config - 配置参数
     */
    constructor(scene, target, config = {}) {
        // 调用父类构造函数，传入基本配置
        super(scene, target, {
            ...config,
            id: 'pet001',
            name: '喵',
            scale: Pet001.SCALE
            // 不设置速度，使用玩家速度
        });
    }

    /**
     * 加载宠物资源和创建精灵
     * @param {number} x - 初始X坐标
     * @param {number} y - 初始Y坐标
     */
    loadAssets(x, y) {
        // 检查是否已加载宠物精灵表
        if (!this.scene.textures.exists('pet001_sprite')) {
            // 加载宠物精灵表
            this.scene.load.spritesheet('pet001_sprite', 'assets/pets/character01/JumpCattttt.png', {
                frameWidth: 32,  // 每帧宽度
                frameHeight: 32  // 每帧高度
            });

            // 加载完成后创建动画
            this.scene.load.once('complete', () => {
                // 创建宠物动画
                if (!this.scene.anims.exists('pet001_anim')) {
                    this.scene.anims.create({
                        key: 'pet001_anim',
                        frames: this.scene.anims.generateFrameNumbers('pet001_sprite', { start: 0, end: 12 }), // 13帧动画
                        frameRate: 10,
                        repeat: -1
                    });
                }

                // 创建精灵并播放动画
                this.createPetSprite(x, y);
            });

            // 开始加载
            this.scene.load.start();
        } else {
            // 精灵表已加载，直接创建精灵
            this.createPetSprite(x, y);
        }
    }

    /**
     * 创建宠物精灵
     * @param {number} x - 初始X坐标
     * @param {number} y - 初始Y坐标
     */
    createPetSprite(x, y) {
        // 创建精灵（不使用物理碰撞）
        this.sprite = this.scene.add.sprite(x, y, 'pet001_sprite');
        this.sprite.setOrigin(0.5, 0.72); // 纹理位置数值
        console.log('创建喵');

        // 播放动画
        if (this.scene.anims.exists('pet001_anim')) {
            this.sprite.play('pet001_anim');
            console.log('播放喵动画');
        }

        // 设置深度和缩放
        if (this.sprite) {
            this.sprite.customType = 'Pet';

            if (typeof DepthManager !== 'undefined') {
                DepthManager.updateDepth(this.sprite, 'Pet', true);
            }

            this.sprite.setScale(this.scale);
        }

        // 初始化宠物武器
        this.weapon = new D1(this.scene, this);
    }

}
