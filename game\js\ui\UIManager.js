/**
 * UI管理器类
 * 统一管理游戏中所有UI组件的创建、更新和销毁
 */
class UIManager {
    constructor(scene) {
        this.scene = scene;
        this.components = new Map(); // 存储所有UI组件
        this.eventManager = new UIEventManager();
        this.theme = new UITheme();

        console.log('UIManager初始化完成');
    }

    /**
     * 创建UI组件
     * @param {string} type - 组件类型
     * @param {string} id - 组件唯一标识
     * @param {Object} config - 组件配置
     * @returns {UIComponent} 创建的组件实例
     */
    createComponent(type, id, config = {}) {
        // 如果组件已存在，先销毁
        if (this.components.has(id)) {
            this.destroyComponent(id);
        }

        let component;

        switch (type) {
            case 'button':
                component = new UIButton(this.scene, config);
                break;
            case 'text':
                component = new UIText(this.scene, config);
                break;
            case 'progressbar':
                component = new UIProgressBar(this.scene, config);
                break;
            case 'panel':
                component = new UIPanel(this.scene, config);
                break;
            case 'modal':
                component = new UIModal(this.scene, config);
                break;
            default:
                console.warn(`未知的UI组件类型: ${type}`);
                return null;
        }

        if (component) {
            component.id = id;
            component.uiManager = this;
            this.components.set(id, component);

            // 应用主题样式
            this.theme.applyTheme(component, config.theme);

            console.log(`UI组件创建成功: ${type}(${id})`);
        }

        return component;
    }

    /**
     * 获取UI组件
     * @param {string} id - 组件ID
     * @returns {UIComponent|null} 组件实例
     */
    getComponent(id) {
        return this.components.get(id) || null;
    }

    /**
     * 销毁UI组件
     * @param {string} id - 组件ID
     */
    destroyComponent(id) {
        const component = this.components.get(id);
        if (component) {
            component.destroy();
            this.components.delete(id);
            console.log(`UI组件已销毁: ${id}`);
        }
    }

    /**
     * 销毁所有UI组件
     */
    destroyAll() {
        for (const component of this.components.values()) {
            component.destroy();
        }
        this.components.clear();
        this.eventManager.removeAllListeners();
        console.log('所有UI组件已销毁');
    }

    /**
     * 更新所有UI组件
     */
    update() {
        for (const component of this.components.values()) {
            if (component.update) {
                component.update();
            }
        }
    }

    /**
     * 获取事件管理器
     */
    getEventManager() {
        return this.eventManager;
    }

    /**
     * 获取主题管理器
     */
    getTheme() {
        return this.theme;
    }
}
