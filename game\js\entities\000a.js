/**
 * 敌人：第0分钟生成
 */

class E000a extends Enemy {
    /** 缩放比例 */
    static SCALE = 0.5;

    /** 碰撞箱设置 */
    static COLLISION_BOX = {
        type: 'circle',  // 圆形碰撞箱
        radius: 40,      // 半径
        offsetX: 40,      // X偏移
        offsetY: 0       // Y偏移
    };

    /**
     * 创建敌人
     */
    constructor(scene, x, y) {
        // 属性: 生命值(随机), 伤害值, 移动速度(随机)
        const randomHealth = Phaser.Math.Between(6, 12);
        const baseSpeed = 27;
        const randomSpeed = Phaser.Math.Between(baseSpeed - 5, baseSpeed + 5);
        super(scene, x, y, 'e000', randomHealth, 5, randomSpeed);

        // 加载图片和创建动画
        this.loadAssets();
    }

    /**
     * 加载图片和创建动画
     */
    loadAssets() {
        // 动画帧图片
        const frames = [
            { key: 'e000_1', path: 'assets/images/000/0000_01.png' },
            { key: 'e000_2', path: 'assets/images/000/0000_02.png' },
            { key: 'e000_3', path: 'assets/images/000/0000_03.png' },
            { key: 'e000_4', path: 'assets/images/000/0000_04.png' }
        ];

        // 加载图片
        frames.forEach(frame => {
            this.scene.load.image(frame.key, frame.path);
        });

        // 加载完成后创建动画并播放
        this.scene.load.once('complete', () => {
            // 创建动画
            if (!this.scene.anims.exists('e000_anim')) {
                this.scene.anims.create({
                    key: 'e000_anim',
                    frames: frames.map(frame => ({ key: frame.key })),
                    frameRate: 10,
                    repeat: -1
                });
            }

            // 设置精灵并播放动画
            if (this.sprite) {
                this.sprite.setTexture('e000_1');
                this.sprite.setScale(E000a.SCALE);
                this.sprite.play('e000_anim');
            }
        });

        // 开始加载
        this.scene.load.start();
    }

    /**
     * 更新行为
     */
    update(playerPosition) {
        super.update(playerPosition);
    }
}