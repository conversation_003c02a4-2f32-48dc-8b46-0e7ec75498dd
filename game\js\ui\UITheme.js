/**
 * UI主题管理器
 * 统一管理UI样式和主题
 */
class UITheme {
    constructor() {
        // 默认主题配置
        this.themes = {
            default: {
                button: {
                    backgroundColor: 0x3498db,
                    hoverColor: 0x2980b9,
                    textColor: '#ffffff',
                    font: 'bold 24px Arial',
                    strokeColor: '#000000',
                    strokeThickness: 0
                },
                text: {
                    color: '#ffffff',
                    font: '18px Arial',
                    strokeColor: '#000000',
                    strokeThickness: 3
                },
                panel: {
                    backgroundColor: 0x000000,
                    alpha: 0.7,
                    borderColor: 0x666666,
                    borderWidth: 2
                },
                progressBar: {
                    backgroundColor: 0x666666,
                    fillColor: 0x00ff00,
                    borderColor: 0x333333,
                    borderWidth: 1
                }
            },
            menu: {
                button: {
                    backgroundColor: 0x27ae60,
                    hoverColor: 0x219653,
                    textColor: '#ffffff',
                    font: 'bold 24px Arial'
                }
            },
            game: {
                button: {
                    backgroundColor: 0xff5500,
                    hoverColor: 0xcc4400,
                    textColor: '#ffffff',
                    font: 'bold 16px Arial'
                },
                text: {
                    color: '#ffffff',
                    font: '16px Arial',
                    strokeColor: '#000000',
                    strokeThickness: 2
                }
            }
        };
        
        this.currentTheme = 'default';
        console.log('UITheme初始化完成');
    }

    /**
     * 设置当前主题
     * @param {string} themeName - 主题名称
     */
    setTheme(themeName) {
        if (this.themes[themeName]) {
            this.currentTheme = themeName;
            console.log(`主题已切换到: ${themeName}`);
        } else {
            console.warn(`主题不存在: ${themeName}`);
        }
    }

    /**
     * 获取组件样式
     * @param {string} componentType - 组件类型
     * @param {string} themeName - 主题名称（可选）
     * @returns {Object} 样式配置
     */
    getStyle(componentType, themeName = null) {
        const theme = themeName || this.currentTheme;
        const themeConfig = this.themes[theme];
        
        if (!themeConfig) {
            console.warn(`主题不存在: ${theme}`);
            return this.themes.default[componentType] || {};
        }
        
        // 合并默认样式和当前主题样式
        const defaultStyle = this.themes.default[componentType] || {};
        const themeStyle = themeConfig[componentType] || {};
        
        return { ...defaultStyle, ...themeStyle };
    }

    /**
     * 应用主题到组件
     * @param {UIComponent} component - UI组件
     * @param {string} themeName - 主题名称（可选）
     */
    applyTheme(component, themeName = null) {
        if (!component) return;
        
        const componentType = this.getComponentType(component);
        const style = this.getStyle(componentType, themeName);
        
        // 应用样式到组件
        if (component.applyStyle) {
            component.applyStyle(style);
        }
    }

    /**
     * 获取组件类型
     * @param {UIComponent} component - UI组件
     * @returns {string} 组件类型
     */
    getComponentType(component) {
        if (component instanceof UIButton) return 'button';
        if (component instanceof UIText) return 'text';
        if (component instanceof UIPanel) return 'panel';
        if (component instanceof UIProgressBar) return 'progressBar';
        return 'default';
    }

    /**
     * 添加自定义主题
     * @param {string} themeName - 主题名称
     * @param {Object} themeConfig - 主题配置
     */
    addTheme(themeName, themeConfig) {
        this.themes[themeName] = themeConfig;
        console.log(`自定义主题已添加: ${themeName}`);
    }

    /**
     * 获取当前主题名称
     * @returns {string} 当前主题名称
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * 获取所有可用主题
     * @returns {Array} 主题名称数组
     */
    getAvailableThemes() {
        return Object.keys(this.themes);
    }
}
