/**
 * 宠物商店场景
 */
class PetShopScene extends Phaser.Scene {
    constructor() {
        super({ key: 'PetShopScene' });
        this.uiManager = null;
        this.petCards = []; // 存储宠物卡片
    }

    create() {
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;

        // 创建UI管理器
        this.uiManager = new UIManager(this);

        // 添加背景
        this.add.rectangle(
            width / 2,
            height / 2,
            width,
            height,
            0x2c3e50  // 深蓝灰色背景
        );

        // 创建标题文本
        this.uiManager.createComponent('text', 'title', {
            x: width / 2,
            y: 60,
            text: '精灵商店',
            font: 'bold 32px Arial',
            color: '#ffffff',
            strokeColor: '#000000',
            strokeThickness: 6,
            originX: 0.5,
            originY: 0.5
        });

        // 创建返回按钮
        this.uiManager.createComponent('button', 'returnButton', {
            x: 100,
            y: 50,
            width: 160,
            height: 50,
            text: '返回主菜单',
            font: 'bold 16px Arial',
            backgroundColor: 0x3498db,
            hoverColor: 0x2980b9,
            onClick: () => {
                this.scene.start('MainMenuScene');
            }
        });

        // 创建钱币显示
        this.createCoinDisplay();

        // 创建宠物卡片
        this.createPetCards();

        console.log('精灵商店场景创建完成');
    }

    /**
     * 创建钱币显示
     */
    createCoinDisplay() {
        const width = this.cameras.main.width;

        // 获取累计钱币数量
        let totalCoins = 0;
        if (window.gameData) {
            try {
                totalCoins = window.gameData.getTotalCoins();
            } catch (error) {
                console.error('获取累计钱币失败:', error);
            }
        }

        // 创建钱币背景面板
        this.uiManager.createComponent('panel', 'coinPanel', {
            x: width - 120,
            y: 50,
            width: 200,
            height: 60,
            backgroundColor: 0x2c3e50,
            borderColor: 0xf39c12,
            borderWidth: 2
        });

        // 创建钱币图标文本
        this.uiManager.createComponent('text', 'coinIcon', {
            x: width - 180,
            y: 50,
            text: '💰',
            font: '24px Arial',
            color: '#f39c12',
            originX: 0.5,
            originY: 0.5
        });

        // 创建钱币数量文本
        this.uiManager.createComponent('text', 'coinText', {
            x: width - 120,
            y: 50,
            text: totalCoins.toString(),
            font: 'bold 20px Arial',
            color: '#ffffff',
            strokeColor: '#000000',
            strokeThickness: 2,
            originX: 0.5,
            originY: 0.5
        });

        console.log(`精灵商店显示累计钱币: ${totalCoins}`);
    }

    /**
     * 更新钱币显示
     */
    updateCoinDisplay() {
        let totalCoins = 0;
        if (window.gameData) {
            try {
                totalCoins = window.gameData.getTotalCoins();
            } catch (error) {
                console.error('更新钱币显示失败:', error);
            }
        }

        const coinText = this.uiManager.getComponent('coinText');
        if (coinText) {
            coinText.setText(totalCoins.toString());
        }
    }

    /**
     * 创建宠物卡片
     */
    createPetCards() {
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;

        // 品质等级配置
        const rarityConfig = {
            common: { name: '普通', color: 0x95a5a6, glowColor: 0xbdc3c7, price: 100 },
            uncommon: { name: '优秀', color: 0x2ecc71, glowColor: 0x58d68d, price: 200 },
            rare: { name: '稀有', color: 0x3498db, glowColor: 0x5dade2, price: 400 },
            epic: { name: '史诗', color: 0x9b59b6, glowColor: 0xbb8fce, price: 800 },
            legendary: { name: '传说', color: 0xe67e22, glowColor: 0xf39c12, price: 1600 }
        };

        // 宠物数据配置（包含已解锁和未解锁）
        const petData = [
            // 宠物列表
            {
                id: 'pet001',
                name: '喵',
                description: '是的，这是一只猫，很普通的那种。',
                weapon: '武器D1',
                rarity: 'common',
                icon: '🐱',
                purchasable: true
            },
            {
                id: 'pet002',
                name: '草',
                description: '额...是的',
                weapon: '武器C1',
                rarity: 'uncommon',
                icon: '🌿',
                purchasable: true
            },
            {
                id: 'pet003',
                name: '人',
                description: '人类的优点有很多',
                weapon: '武器A1',
                rarity: 'rare',
                icon: '👨‍🦰',
                purchasable: true
            },
            {
                id: 'pet004',
                name: '呲毛',
                description: '一团乱糟糟的东西，有很多传闻...',
                weapon: '武器B1',
                rarity: 'epic',
                icon: '👹',
                purchasable: true
            },
            {
                id: 'pet006',
                name: '龙族守护',
                description: '强大的龙族战士',
                weapon: '龙息术',
                rarity: 'legendary',
                icon: '🐉',
                purchasable: false
            },
            {
                id: 'pet007',
                name: '凤凰之翼',
                description: '重生的火焰精灵',
                weapon: '烈焰风暴',
                rarity: 'epic',
                unlocked: false,
                icon: '🔥',
                purchasable: false
            },
            {
                id: 'pet008',
                name: '雷电狼王',
                description: '掌控雷电的狼族',
                weapon: '雷霆一击',
                rarity: 'epic',
                unlocked: false,
                icon: '⚡',
                purchasable: false
            },
            {
                id: 'pet009',
                name: '冰霜巨熊',
                description: '寒冰之力的守护者',
                weapon: '冰封术',
                rarity: 'rare',
                unlocked: false,
                icon: '🐻‍❄️',
                purchasable: false
            },
            {
                id: 'pet010',
                name: '暗影刺客',
                description: '隐匿于黑暗的杀手',
                weapon: '暗影突袭',
                rarity: 'rare',
                unlocked: false,
                icon: '🥷',
                purchasable: false
            },
            {
                id: 'pet011',
                name: '森林精灵',
                description: '自然之力的使者',
                weapon: '藤蔓缠绕',
                rarity: 'uncommon',
                unlocked: false,
                icon: '🧚‍♀️',
                purchasable: false
            },
            {
                id: 'pet012',
                name: '机械战犬',
                description: '科技与力量的结合',
                weapon: '激光炮',
                rarity: 'uncommon',
                unlocked: false,
                icon: '🤖',
                purchasable: false
            }
        ];

        // 计算网格布局（改为2列布局，卡片更大）
        const cardWidth = 320;
        const cardHeight = 240;
        const cols = 2;
        const colSpacing = 40;
        const rowSpacing = 30;

        // 计算起始位置
        const totalGridWidth = cols * cardWidth + (cols - 1) * colSpacing;
        const startX = (width - totalGridWidth) / 2 + cardWidth / 2;
        const startY = height * 0.25; // 从屏幕25%位置开始

        // 创建每个宠物卡片
        petData.forEach((pet, index) => {
            const col = index % cols;
            const row = Math.floor(index / cols);

            const cardX = startX + col * (cardWidth + colSpacing);
            const cardY = startY + row * (cardHeight + rowSpacing);

            const rarity = rarityConfig[pet.rarity];
            this.createPetCard(pet, rarity, cardX, cardY, cardWidth, cardHeight);
        });
    }

    /**
     * 创建单个宠物卡片
     */
    createPetCard(petData, rarity, x, y, width, height) {
        // 检查精灵状态：已拥有、可购买、锁定
        const isOwned = window.gameData ? window.gameData.isOwnedPet(petData.id) : false;
        const isPurchasable = petData.purchasable !== false; // 默认可购买，除非明确设置为false
        const price = rarity.price;

        // 创建卡片容器组
        const cardGroup = this.physics.add.group();

        // 1. 创建外发光效果（多层）
        const glowLayers = [
            { size: 12, alpha: 0.3, color: rarity.glowColor },
            { size: 8, alpha: 0.5, color: rarity.glowColor },
            { size: 4, alpha: 0.7, color: rarity.glowColor }
        ];

        glowLayers.forEach(layer => {
            const glow = this.add.rectangle(x, y, width + layer.size, height + layer.size, layer.color);
            glow.setAlpha(layer.alpha);
            cardGroup.add(glow);
        });

        // 2. 创建卡片主背景（渐变效果）
        const cardBg = this.add.rectangle(x, y, width, height, 0x2c3e50);
        cardBg.setStrokeStyle(3, rarity.color);
        cardGroup.add(cardBg);

        // 3. 创建内部渐变背景
        const innerBg = this.add.rectangle(x, y, width - 6, height - 6, 0x34495e);
        innerBg.setAlpha(0.8);
        cardGroup.add(innerBg);

        // 4. 创建品质标识条
        const rarityBar = this.add.rectangle(x, y - height / 2 + 18, width - 10, 26, rarity.color);
        rarityBar.setAlpha(0.9);
        cardGroup.add(rarityBar);

        // 5. 创建品质文字
        const rarityText = this.add.text(x, y - height / 2 + 18, rarity.name, {
            fontSize: '18px',
            fontStyle: 'bold',
            fill: '#ffffff',
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);
        cardGroup.add(rarityText);

        // 6. 创建宠物图标 - 所有宠物都显示真实图标
        const petIcon = this.add.text(x, y - 40, petData.icon, {
            fontSize: '48px'
        }).setOrigin(0.5);
        cardGroup.add(petIcon);

        // 7. 创建宠物名称 - 所有宠物都显示真实名称
        const petName = this.add.text(x, y + 15, petData.name, {
            fontSize: '20px',
            fontStyle: 'bold',
            fill: isOwned ? '#ffffff' : '#cccccc',
            stroke: '#000000',
            strokeThickness: 1
        }).setOrigin(0.5);
        cardGroup.add(petName);

        // 8. 创建宠物描述 - 所有宠物都显示真实描述
        const petDesc = this.add.text(x, y + 40, petData.description, {
            fontSize: '16px',
            fill: isOwned ? '#bdc3c7' : '#999999'
        }).setOrigin(0.5);
        cardGroup.add(petDesc);

        // 9. 创建武器信息 - 所有宠物都显示真实武器
        const weaponInfo = this.add.text(x, y + 65, `装备: ${petData.weapon}`, {
            fontSize: '14px',
            fill: isOwned ? '#95a5a6' : '#777777'
        }).setOrigin(0.5);
        cardGroup.add(weaponInfo);

        // 10. 创建状态按钮
        let statusButton, statusLabel;
        if (isOwned) {
            statusButton = this.add.rectangle(x, y + height / 2 - 25, width - 30, 35, 0x27ae60);
            statusButton.setStrokeStyle(3, 0x2ecc71);
            statusLabel = this.add.text(x, y + height / 2 - 25, '已解锁', {
                fontSize: '18px',
                fontStyle: 'bold',
                fill: '#ffffff'
            }).setOrigin(0.5);
        } else if (isPurchasable) {
            statusButton = this.add.rectangle(x, y + height / 2 - 25, width - 30, 35, 0xe74c3c);
            statusButton.setStrokeStyle(3, 0xc0392b);
            statusLabel = this.add.text(x, y + height / 2 - 25, `${price} 钱币`, {
                fontSize: '18px',
                fontStyle: 'bold',
                fill: '#ffffff'
            }).setOrigin(0.5);

        } else {
            // 锁定状态
            statusButton = this.add.rectangle(x, y + height / 2 - 25, width - 30, 35, 0x555555);
            statusButton.setStrokeStyle(3, 0x333333);
            statusLabel = this.add.text(x, y + height / 2 - 25, '暂未开放', {
                fontSize: '18px',
                fontStyle: 'bold',
                fill: '#888888'
            }).setOrigin(0.5);
        }

        cardGroup.add(statusButton);
        cardGroup.add(statusLabel);

        // 11. 为未拥有卡片添加暗化效果
        if (!isOwned) {
            const darkOverlay = this.add.rectangle(x, y, width, height, 0x000000);
            darkOverlay.setAlpha(0.6); // 统一暗化程度
            cardGroup.add(darkOverlay);
        }

        // 12. 为不可购买宠物添加独立图层锁头
        if (!isPurchasable) {
            const lockIcon = this.add.text(x + width / 2 - 30, y - height / 2 + 30, '🔒', {
                fontSize: '32px'
            }).setOrigin(0.5);
            lockIcon.setDepth(1000); // 独立图层，高深度
            // 不添加到cardGroup，保持独立
        }

        // 12. 添加交互效果
        cardBg.setInteractive();

        // 悬停效果
        cardBg.on('pointerover', () => {
            this.tweens.add({
                targets: cardGroup.children.entries,
                scaleX: 1.05,
                scaleY: 1.05,
                duration: 200,
                ease: 'Power2'
            });

            // 增强发光效果
            glowLayers.forEach((layer, index) => {
                const glowElement = cardGroup.children.entries[index];
                this.tweens.add({
                    targets: glowElement,
                    alpha: layer.alpha * 1.5,
                    duration: 200
                });
            });
        });

        cardBg.on('pointerout', () => {
            this.tweens.add({
                targets: cardGroup.children.entries,
                scaleX: 1,
                scaleY: 1,
                duration: 200,
                ease: 'Power2'
            });

            // 恢复发光效果
            glowLayers.forEach((layer, index) => {
                const glowElement = cardGroup.children.entries[index];
                this.tweens.add({
                    targets: glowElement,
                    alpha: layer.alpha,
                    duration: 200
                });
            });
        });

        // 点击效果 - 只有可购买且未拥有的精灵才能点击购买
        if (!isOwned && isPurchasable) {
            cardBg.on('pointerdown', () => {
                console.log(`点击购买精灵: ${petData.name}, 价格: ${price}`);

                // 按压动画
                this.tweens.add({
                    targets: cardGroup.children.entries,
                    scaleX: 0.95,
                    scaleY: 0.95,
                    duration: 100,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 实现购买逻辑
                this.purchasePet(petData.id, price);
            });
        }

        // 存储卡片信息
        this.petCards.push({
            petData,
            rarity,
            elements: {
                cardGroup,
                cardBg,
                petIcon,
                petName,
                petDesc,
                weaponInfo,
                statusButton,
                statusLabel
            }
        });
    }

    /**
     * 购买宠物
     * @param {string} petId - 宠物ID
     * @param {number} price - 价格
     */
    purchasePet(petId, price) {
        if (!window.gameData) {
            console.error('游戏数据未初始化');
            return;
        }

        // 检查精灵是否可购买（安全检查）
        const petData = this.getPetDataById(petId);
        if (!petData || petData.purchasable === false) {
            console.log(`精灵 ${petId} 不可购买`);
            return;
        }

        // 检查金币是否足够
        const totalCoins = window.gameData.getTotalCoins();
        if (totalCoins < price) {
            console.log(`金币不足，需要 ${price}，当前 ${totalCoins}`);
            return;
        }

        // 扣除金币
        const newTotal = totalCoins - price;
        window.gameData.currentGameData.player.totalCoins = newTotal;

        // 添加宠物
        const success = window.gameData.addOwnedPet(petId);
        if (success) {
            console.log(`成功购买宠物: ${petId}, 花费: ${price}`);

            // 更新钱币显示
            this.updateCoinDisplay();

            // 重新创建宠物卡片以更新状态
            this.refreshPetCards();
        }
    }

    /**
     * 根据ID获取精灵数据
     * @param {string} petId - 精灵ID
     * @returns {Object|null} 精灵数据
     */
    getPetDataById(petId) {
        // 重新获取精灵数据数组（这里简化处理，实际应该缓存）
        const petData = [
            { id: 'pet001', purchasable: true },
            { id: 'pet002', purchasable: true },
            { id: 'pet003', purchasable: true },
            { id: 'pet004', purchasable: true },
            { id: 'pet006', purchasable: false },
            { id: 'pet007', purchasable: false },
            { id: 'pet008', purchasable: false },
            { id: 'pet009', purchasable: false },
            { id: 'pet010', purchasable: false },
            { id: 'pet011', purchasable: false },
            { id: 'pet012', purchasable: false }
        ];
        return petData.find(pet => pet.id === petId) || null;
    }

    /**
     * 安全清理单个宠物卡片
     * @param {Object} card - 宠物卡片对象
     */
    safeDestroyCard(card) {
        if (!card || !card.elements) return;

        try {
            // 1. 停止所有相关的动画
            if (this.tweens) {
                this.tweens.killTweensOf(card.elements.cardGroup.children.entries);
                card.elements.cardGroup.children.entries.forEach(child => {
                    if (child && child.active) {
                        this.tweens.killTweensOf(child);
                    }
                });
            }

            // 2. 移除所有事件监听器
            if (card.elements.cardBg && card.elements.cardBg.active) {
                card.elements.cardBg.removeAllListeners();
            }

            // 3. 安全销毁cardGroup
            if (card.elements.cardGroup && card.elements.cardGroup.active) {
                card.elements.cardGroup.destroy();
            }
        } catch (error) {
            console.warn('清理宠物卡片时出错:', error);
        }
    }

    /**
     * 刷新宠物卡片显示
     */
    refreshPetCards() {
        // 安全清除现有卡片
        this.petCards.forEach(card => {
            this.safeDestroyCard(card);
        });
        this.petCards = [];

        // 重新创建卡片
        this.createPetCards();
    }

    /**
     * 场景销毁时清理UI
     */
    destroy() {
        // 1. 停止所有动画
        if (this.tweens) {
            this.tweens.killAll();
        }

        // 2. 清理所有定时器
        if (this.time) {
            this.time.removeAllEvents();
        }

        // 3. 安全清理宠物卡片
        if (this.petCards) {
            this.petCards.forEach(card => {
                this.safeDestroyCard(card);
            });
            this.petCards = [];
        }

        // 4. 清理UIManager
        if (this.uiManager) {
            this.uiManager.destroyAll();
            this.uiManager = null;
        }

        // 5. 调用父类销毁方法
        super.destroy();
    }
}
