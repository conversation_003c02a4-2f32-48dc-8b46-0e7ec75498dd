/**
 * 蝙蝠群类 - 继承Enemy基类，具备血量和击杀功能
 * 保持独立的移动轨迹逻辑
 */
class BatSwarm extends Enemy {
    /**
     * 创建蝙蝠群实例
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {number} startX - 起始X坐标
     * @param {number} startY - 起始Y坐标
     * @param {number} targetX - 目标X坐标
     * @param {number} targetY - 目标Y坐标
     * @param {string} batType - 蝙蝠类型 ('E000a' 或 'E000b')
     */
    /** 碰撞箱设置 */
    static COLLISION_BOX = {
        type: 'circle',
        radius: 20,
        offsetX: 0,
        offsetY: 0
    };

    constructor(scene, startX, startY, targetX, targetY, batType = 'E000a') {
        // 调用Enemy构造函数，设置血量为3，伤害为1，速度为300
        const textureKey = batType === 'E000b' ? 'e000b_1' : 'e000_1';
        super(scene, startX, startY, textureKey, 3, 1, 220);

        // 蝙蝠群特有属性
        this.startX = startX;
        this.startY = startY;
        this.targetX = targetX;
        this.targetY = targetY;
        this.batType = batType;

        // 设置自定义类型
        this.sprite.customType = 'BatSwarm';

        // 设置缩放（可调整）
        const scale = batType === 'E000b' ? 0.7 : 0.7;
        this.sprite.setScale(scale);

        // 播放对应的动画
        const animKey = batType === 'E000b' ? 'e000b_anim' : 'e000_anim';
        if (scene.anims.exists(animKey)) {
            this.sprite.play(animKey);
        } else {
            console.warn(`蝙蝠群动画不存在: ${animKey}`);
        }

        // 计算移动方向
        const dx = targetX - startX;
        const dy = targetY - startY;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance > 0) {
            this.directionX = dx / distance;
            this.directionY = dy / distance;
        } else {
            this.directionX = 1;
            this.directionY = 0;
        }

        // 根据移动方向设置翻转
        if (this.directionX < 0) {
            this.sprite.setFlipX(true);
        }

        console.log(`创建蝙蝠群蝙蝠: ${batType} 从(${startX.toFixed(0)}, ${startY.toFixed(0)}) 到(${targetX.toFixed(0)}, ${targetY.toFixed(0)})`);
    }

    /**
     * 更新蝙蝠移动（覆盖Enemy的update方法）
     * @param {Object} playerPosition - 玩家位置（兼容Enemy接口）
     */
    update(playerPosition) {
        if (!this.isAlive || !this.sprite || !this.sprite.active) return;

        // 调用父类的基础更新（处理深度等）
        super.update(playerPosition);

        // 蝙蝠群特有的移动逻辑
        const deltaTime = this.scene.game.loop.delta / 1000; // 获取deltaTime
        const moveDistance = this.speed * deltaTime;

        // 更新位置
        this.sprite.x += this.directionX * moveDistance;
        this.sprite.y += this.directionY * moveDistance;

        // 检查是否到达目标或超出屏幕
        const dx = this.targetX - this.sprite.x;
        const dy = this.targetY - this.sprite.y;
        const distanceToTarget = Math.sqrt(dx * dx + dy * dy);

        // 如果接近目标或超出屏幕边界，销毁蝙蝠
        if (distanceToTarget < 50 || this.isOutOfBounds()) {
            this.die(); // 使用Enemy的die方法，会触发掉落
        }
    }

    /**
     * 检查是否超出屏幕边界
     */
    isOutOfBounds() {
        const margin = 200;
        const camera = this.scene.cameras.main;
        const playerX = this.scene.player ? this.scene.player.sprite.x : 0;
        const playerY = this.scene.player ? this.scene.player.sprite.y : 0;

        return (
            this.sprite.x < playerX - camera.width - margin ||
            this.sprite.x > playerX + camera.width + margin ||
            this.sprite.y < playerY - camera.height - margin ||
            this.sprite.y > playerY + camera.height + margin
        );
    }
}
