/**
 * 深度管理器类
 * 用于统一管理游戏中所有对象的渲染深度
 */
class DepthManager {
    /**
     * 渲染层级常量
     * 每个层级之间预留了足够的空间以便于后续插入新的层级
     */
    static LAYERS = {
        BACKGROUND: 0,     // 背景层（最底层）
        GROUND: 100,       // 地面层
        GROUND_ITEMS: 150, // 地面物品层（如金币、经验宝石等）
        ITEMS: 200,        // 物品层（一般物品）
        WEAPON_EFFECTS_BELOW: 250, // 显示在角色下方的武器效果
        CHARACTERS: 300,    // 角色层（玩家和敌人）
        WEAPON_EFFECTS_ABOVE: 350, // 显示在角色上方的武器效果
        EFFECTS: 400,      // 特效层（如粒子效果）
        FLOATING_TEXT: 500, // 浮动文本层（如伤害数字）
        UI_BACKGROUND: 900, // UI背景层
        UI: 1000           // UI层（最顶层）
    };

    /**
     * 对象类型到渲染层级的映射
     * 用于自动分配对象的渲染深度
     */
    static TYPE_MAPPING = {
        // 背景相关
        'Background': this.LAYERS.BACKGROUND,
        'Tilemap': this.LAYERS.GROUND,

        // 地面物品
        'Coin': this.LAYERS.GROUND_ITEMS,
        'GoldCoin': this.LAYERS.GROUND_ITEMS,
        'Diamond': this.LAYERS.GROUND_ITEMS,
        'ExperienceGem': this.LAYERS.GROUND_ITEMS,

        // 一般物品
        'Item': this.LAYERS.ITEMS,
        'Pickup': this.LAYERS.ITEMS,
        'Powerup': this.LAYERS.ITEMS,

        // 角色
        'Enemy': this.LAYERS.CHARACTERS,
        'Player': this.LAYERS.CHARACTERS,
        'Pet': this.LAYERS.CHARACTERS,
        'NPC': this.LAYERS.CHARACTERS,
        'BatSwarm': this.LAYERS.CHARACTERS, // 蝙蝠群与其他角色同层

        // 武器效果
        'WeaponA': this.LAYERS.WEAPON_EFFECTS_ABOVE, // 鞭子显示在角色上方
        'WeaponB': this.LAYERS.WEAPON_EFFECTS_BELOW, // 其他武器可能显示在角色下方
        'WeaponC': this.LAYERS.WEAPON_EFFECTS_BELOW,
        'WeaponEffect': this.LAYERS.EFFECTS,

        // 特效
        'Particle': this.LAYERS.EFFECTS,
        'Animation': this.LAYERS.EFFECTS,
        'CollectEffect': this.LAYERS.EFFECTS,

        // 浮动文本
        'DamageText': this.LAYERS.FLOATING_TEXT,
        'FloatingText': this.LAYERS.FLOATING_TEXT,

        // UI元素
        'UIBackground': this.LAYERS.UI_BACKGROUND,
        'UI': this.LAYERS.UI,
        'Button': this.LAYERS.UI,
        'Text': this.LAYERS.UI,
        'ProgressBar': this.LAYERS.UI
    };

    /**
     * 根据对象类型获取基础深度值
     * @param {string} objectType - 对象类型
     * @returns {number} 基础深度值
     */
    static getBaseDepthByType(objectType) {
        return this.TYPE_MAPPING[objectType] || this.LAYERS.ITEMS; // 默认放在物品层
    }

    /**
     * 更新对象的深度值
     * @param {Phaser.GameObjects.GameObject} gameObject - 游戏对象
     * @param {string|number} typeOrBaseDepth - 对象类型或基础深度值
     * @param {boolean} useYSort - 是否根据Y坐标排序
     * @param {number} customOffset - 自定义深度偏移值
     */
    static updateDepth(gameObject, typeOrBaseDepth, useYSort = false, customOffset = 0) {
        if (!gameObject) return;

        // 确定基础深度值
        const baseDepth = typeof typeOrBaseDepth === 'string'
            ? this.getBaseDepthByType(typeOrBaseDepth)
            : typeOrBaseDepth;

        // 应用深度值
        if (useYSort) {
            // Y坐标越大，显示在越上层
            // 使用更精确的Y坐标缩放因子，避免深度冲突
            const yFactor = gameObject.y / 10000;
            gameObject.setDepth(baseDepth + yFactor + customOffset);
        } else {
            gameObject.setDepth(baseDepth + customOffset);
        }

        // 存储对象的原始类型和深度设置，便于后续更新
        gameObject.depthType = typeOrBaseDepth;
        gameObject.useYSort = useYSort;
        gameObject.depthOffset = customOffset;
    }

    /**
     * 更新对象的Y轴排序深度
     * 当对象移动时调用此方法更新深度
     * @param {Phaser.GameObjects.GameObject} gameObject - 游戏对象
     */
    static updateYSortDepth(gameObject) {
        if (!gameObject || !gameObject.useYSort) return;

        // 重新应用深度设置
        this.updateDepth(gameObject, gameObject.depthType, true, gameObject.depthOffset || 0);
    }

    /**
     * 获取UI元素的深度值
     * @param {number} offset - 在UI层中的偏移值
     * @returns {number} UI元素的深度值
     */
    static getUIDepth(offset = 0) {
        return this.LAYERS.UI + offset;
    }

    /**
     * 批量更新场景中所有对象的深度
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {boolean} forceUpdate - 是否强制更新所有对象的深度
     */
    static updateAllDepths(scene, forceUpdate = false) {
        if (!scene) return;

        // 更新所有游戏对象的深度
        scene.children.list.forEach(gameObject => {
            // 如果对象已经有深度设置且不强制更新，则跳过
            if (!forceUpdate && gameObject.depthType !== undefined) {
                // 如果使用Y轴排序，则更新Y轴排序深度
                if (gameObject.useYSort) {
                    this.updateYSortDepth(gameObject);
                }
                return;
            }

            // 获取对象类型（可以是自定义属性）
            const objectType = gameObject.type || gameObject.customType || gameObject.constructor.name;

            // 根据对象类型确定是否使用Y轴排序
            let useYSort = false;

            // 角色类对象（Enemy、Player、Pet、NPC、BatSwarm）默认使用Y轴排序
            if (['Enemy', 'Player', 'Pet', 'NPC', 'BatSwarm'].includes(objectType)) {
                useYSort = true;
            }

            // 地面物品也使用Y轴排序
            if (['Coin', 'GoldCoin', 'Diamond', 'ExperienceGem'].includes(objectType)) {
                useYSort = true;
            }

            // 更新深度
            this.updateDepth(gameObject, objectType, useYSort);
        });
    }

    /**
     * 初始化场景的深度管理
     * 在场景的create方法中调用
     * @param {Phaser.Scene} scene - 游戏场景
     */
    static initScene(scene) {
        if (!scene) return;

        // 初始化场景的深度管理
        this.updateAllDepths(scene, true);

        // 添加场景更新事件，在每帧更新需要Y轴排序的对象深度
        scene.events.on('update', () => {
            // 只更新使用Y轴排序的对象
            scene.children.list.forEach(gameObject => {
                if (gameObject.useYSort) {
                    this.updateYSortDepth(gameObject);
                }
            });
        });
    }
}