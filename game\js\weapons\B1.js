/**
 * 武器B1 - 宠物专用武器
 * 攻击最近的敌人
 */
class B1 extends Weapon {
    /**
     * 创建武器B1
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {Pet} player - 宠物对象
     * @param {boolean} isAcquired - 是否已获得武器，默认为true
     */
    constructor(scene, player, isAcquired = true) {
        super(scene, player, isAcquired);

        // 武器属性
        this.name = "B1";
        this.damageMin = 6;         // 最小伤害值
        this.damageMax = 7;         // 最大伤害值
        this.cooldownTime = 2000;    // 冷却时间(毫秒)，调整为2秒
        this.range = 500;           // 攻击范围(像素)，适中的攻击范围
        this.projectileSpeed = 100; // 射弹速度(像素/秒)，适中的速度，便于观察
        this.projectileCount = 1;   // 射弹数量，初始为1个
        this.penetration = 1;       // 穿透数量
        this.knockbackForce = 0;    // 击退力度
        this.level = 1;             // 武器等级
        this.projectileGroupDelay = 300; // 射弹组内的发射间隔(毫秒)

        // 调试标志
        this.debug = window.GameDebug?.weapons || false; // 使用全局调试配置

        // 创建武器精灵组
        this.weaponGroup = this.scene.physics.add.group();

        // 创建特效精灵组（仅用于视觉效果，不参与碰撞）
        this.effectGroup = this.scene.add.group();

        // 特效资源是否已加载完成的标志
        this.effectsLoaded = false;

        // 预加载武器图像
        this.preloadAssets();
    }

    /**
     * 预加载资源
     */
    preloadAssets() {
        // 使用GameScene预加载的图片资源

        // 创建简单的帧动画
        if (!this.scene.anims.exists('b_effect_anim')) {
            this.scene.anims.create({
                key: 'b_effect_anim',
                frames: [
                    { key: 'b_effect_001' },
                    { key: 'b_effect_002' },
                    { key: 'b_effect_003' },
                    { key: 'b_effect_004' }
                ],
                frameRate: 10,
                repeat: -1  // 循环播放
            });
        }

        if (this.debug) {
            console.log(`武器B1使用预加载的图片资源和简单动画`);
        }
    }

    /**
     * 攻击方法
     */
    attack() {
        // 如果没有敌人组，不执行攻击
        if (!this.scene.enemiesGroup || this.scene.enemiesGroup.getChildren().length === 0) {
            return;
        }

        // 计算基础伤害
        const damage = Phaser.Math.Between(this.damageMin, this.damageMax);

        // 根据射弹数量创建多个射弹
        for (let i = 0; i < this.projectileCount; i++) {
            // 延迟创建射弹，使用较短的间隔
            this.scene.time.delayedCall(i * this.projectileGroupDelay, () => {
                // 每次创建射弹时重新查找最近敌人
                const nearestEnemy = this.findNearestEnemy();
                if (nearestEnemy) {
                    this.createProjectile(nearestEnemy, damage);
                }
            });
        }
    }

    /**
     * 创建射弹
     * @param {Phaser.GameObjects.Sprite} target - 目标敌人
     * @param {number} damage - 伤害值
     */
    createProjectile(target, damage) {
        // 如果目标已经不存在，不创建射弹
        if (!target || !target.active) {
            if (this.debug) {
                console.log(`B1攻击：目标不存在或不活跃，取消射弹创建`);
            }
            return;
        }

        if (this.debug) {
            console.log(`B1攻击：创建射弹，目标位置(${target.x}, ${target.y})，伤害：${damage}`);
        }

        // 创建射弹精灵，使用预加载的图片
        const projectile = this.weaponGroup.create(
            this.player.sprite.x,
            this.player.sprite.y,
            'b_effect_001'  // 初始帧
        );

        // 播放动画
        projectile.play('b_effect_anim');

        // 设置射弹属性
        projectile.damage = damage;
        projectile.penetration = this.penetration; // 穿透次数
        projectile.setScale(1.0); // 适当的尺寸
        projectile.setAlpha(1.0); // 完全不透明

        // 设置圆形碰撞边界
        const radius = 8 * projectile.scaleX; // 碰撞圆半径
        projectile.body.setCircle(radius,
            (projectile.width - radius * 2) / 2,  // X轴偏移量，使碰撞圆居中
            (projectile.height - radius * 2) / 2  // Y轴偏移量，使碰撞圆居中
        );

        // 设置深度确保正确显示
        projectile.setDepth(350);

        // 计算射弹方向 - 从宠物到目标的直线
        const angle = Phaser.Math.Angle.Between(
            projectile.x, projectile.y,
            target.x, target.y
        );

        // 设置射弹速度 - 直线飞向目标
        this.scene.physics.velocityFromRotation(
            angle,
            this.projectileSpeed,
            projectile.body.velocity
        );

        // 设置射弹旋转 - 朝向目标方向
        projectile.rotation = angle;

        // 添加射弹碰撞检测
        const collider = this.scene.physics.add.overlap(
            projectile,
            this.scene.enemiesGroup,
            this.onProjectileHitEnemy,
            null,
            this
        );

        // 设置射弹生命周期 - 根据射程计算最大飞行时间
        const maxDistance = this.range;
        const flightTime = (maxDistance / this.projectileSpeed) * 1000;

        this.scene.time.delayedCall(flightTime, () => {
            if (collider) collider.destroy();
            if (projectile && projectile.active) {
                projectile.destroy();
            }
        });
    }

    /**
     * 找到最近的敌人
     * @returns {Phaser.GameObjects.Sprite} 最近的敌人精灵
     */
    findNearestEnemy() {
        const enemies = this.scene.enemiesGroup.getChildren();
        if (enemies.length === 0) {
            if (this.debug) console.log(`B1寻找目标：没有敌人`);
            return null;
        }

        let nearestEnemy = null;
        let minDistance = Infinity;
        let validEnemiesCount = 0;

        for (const enemy of enemies) {
            // 确保敌人是活的
            if (!enemy.active || !enemy.enemyInstance || !enemy.enemyInstance.isAlive) {
                continue;
            }

            validEnemiesCount++;

            // 计算距离
            const distance = Phaser.Math.Distance.Between(
                this.player.sprite.x, this.player.sprite.y,
                enemy.x, enemy.y
            );

            // 如果距离小于当前最小距离，更新最近敌人
            if (distance < minDistance) {
                minDistance = distance;
                nearestEnemy = enemy;
            }
        }

        // 如果没有找到敌人或敌人太远，返回null
        if (!nearestEnemy) {
            if (this.debug) console.log(`B1寻找目标：没有有效敌人`);
            return null;
        }

        if (minDistance > this.range) {
            if (this.debug) console.log(`B1寻找目标：最近敌人距离(${minDistance})超出范围(${this.range})`);
            return null;
        }

        if (this.debug) {
            console.log(`B1寻找目标：找到最近敌人，距离${minDistance}，有效敌人数量：${validEnemiesCount}`);
        }

        return nearestEnemy;
    }

    /**
     * 射弹击中敌人的回调
     * @param {Phaser.GameObjects.Sprite} projectile - 射弹精灵
     * @param {Phaser.GameObjects.Sprite} enemySprite - 敌人精灵
     */
    onProjectileHitEnemy(projectile, enemySprite) {
        // 获取敌人实例
        const enemy = enemySprite.enemyInstance;
        if (!enemy || !enemy.isAlive) return;

        // 对敌人造成伤害
        const damaged = this.damageEnemy(enemy, projectile.damage);

        // 如果敌人还活着，应用击退效果
        if (damaged && enemy.isAlive && enemy.knockback) {
            // 计算击退方向（从射弹指向敌人的方向）
            const knockbackAngle = Phaser.Math.Angle.Between(
                projectile.x, projectile.y,
                enemySprite.x, enemySprite.y
            );

            // 应用击退效果
            enemy.knockback(knockbackAngle, this.knockbackForce);
        }

        // 减少穿透次数
        projectile.penetration--;

        // 如果穿透次数用完，销毁射弹
        if (projectile.penetration <= 0) {
            projectile.destroy();
        }

        if (this.debug) {
            console.log(`B1命中敌人：位置(${enemySprite.x}, ${enemySprite.y})，伤害：${projectile.damage}`);
        }
    }
}
