/**
 * 武器D1 - 宠物专用漩涡武器
 * 产生一个以宠物为中心的圆形力场，对区域内敌人造成持续伤害并击退
 *
 * 行为特点：
 * 1. 以宠物为中心创建漩涡力场，始终跟随宠物
 * 2. 对进入力场的敌人造成持续伤害
 * 3. 对区域内敌人施加向外的击退力
 * 4. 随着等级提升，伤害、范围和击退力增加
 */
class D1 extends Weapon {
    /**
     * 创建武器D1
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {Pet} player - 宠物对象
     * @param {boolean} isAcquired - 是否已获得武器，默认为true
     */
    constructor(scene, player, isAcquired = true) {
        super(scene, player, isAcquired);

        // 武器属性
        this.name = "D1";
        this.damageMin = 5;         // 最小伤害值
        this.damageMax = 15;        // 最大伤害值
        this.cooldownTime = 1300;   // 冷却时间(毫秒)
        this.range = 50;           // 攻击范围(像素)，区域半径
        this.knockbackForce = 30;   // 击退力度
        this.damageInterval = 500;  // 伤害间隔(毫秒)
        this.level = 1;             // 武器等级
        this.activeTime = 2000;     // 每次激活持续时间(毫秒)
        this.pulseDelay = 300;      // 脉冲效果间隔(毫秒)

        // 记录初始武器属性（用于计算比例）
        this.initialRange = 100;    // 初始攻击范围

        // 特效精灵初始尺寸
        this.effectInitialWidth = 200;  // 特效初始宽度
        this.effectInitialHeight = 200; // 特效初始高度

        // 系统状态
        this.effectsLoaded = false; // 特效资源是否已加载完成
        this.debug = window.GameDebug?.weapons || false; // 使用全局调试配置
        this.isActive = false;      // 漩涡是否处于激活状态
        this.affectedEnemies = new Set(); // 当前已受影响的敌人集合

        // 创建武器精灵组（用于碰撞检测）
        this.weaponGroup = this.scene.physics.add.group();

        // 创建特效精灵组（仅用于视觉效果，不参与碰撞）
        this.effectGroup = this.scene.add.group();

        // 预加载武器图像
        this.preloadAssets();
    }

    /**
     * 预加载资源
     */
    preloadAssets() {
        // 始终重新创建武器图像，以确保它反映当前的range
        // 如果纹理已存在，先销毁它
        if (this.scene.textures.exists('weaponD1')) {
            this.scene.textures.remove('weaponD1');
        }

        // 创建一个简单的武器图像（用于碰撞检测）
        const graphics = this.scene.make.graphics({ x: 0, y: 0 });
        graphics.fillStyle(0xFFFFFF, 1);
        graphics.fillCircle(this.range, this.range, this.range);
        graphics.generateTexture('weaponD1', this.range * 2, this.range * 2);
        graphics.destroy();

        console.log(`武器D1纹理已更新: 半径=${this.range}`);

        // 资源已在StartScene中预加载，这里只需创建动画
        try {
            // 检查是否所有图片都已加载成功
            let loadedCount = 0;
            for (let i = 1; i <= 20; i++) {
                const frameNumber = i.toString().padStart(2, '0');
                const key = `d_effect_${frameNumber}`;
                if (this.scene.textures.exists(key)) {
                    loadedCount++;
                }
            }

            console.log(`武器D1特效资源检查: ${loadedCount}/20 帧已加载`);

            // 创建动画
            this.createVortexAnimations();
            this.effectsLoaded = true;
            console.log('武器D1特效动画已创建');
        } catch (error) {
            console.error('武器D1特效动画创建失败:', error);
        }
    }

    /**
     * 创建漩涡动画
     */
    createVortexAnimations() {
        // 如果动画已存在，先删除
        if (this.scene.anims.exists('d_effect_anim')) {
            this.scene.anims.remove('d_effect_anim');
        }

        // 创建主要漩涡动画 - 使用所有20帧
        // 使用序号图片创建动画帧
        const frames = [];

        // 添加所有帧
        for (let i = 1; i <= 20; i++) {
            const frameNumber = i.toString().padStart(2, '0');
            frames.push({ key: `d_effect_${frameNumber}` });
        }

        // 创建动画
        this.scene.anims.create({
            key: 'd_effect_anim',
            frames: frames,
            frameRate: 15,  // 稍微提高帧率使动画更流畅
            repeat: -1 // 循环播放
        });
    }

    /**
     * 创建脉冲环效果纹理
     * @param {string} key - 纹理键名
     * @param {number} size - 纹理大小
     * @returns {Phaser.Textures.Texture} 创建的纹理
     */
    createPulseTexture(key, size) {
        // 如果纹理已存在，直接返回
        if (this.scene.textures.exists(key)) {
            return this.scene.textures.get(key);
        }

        // 创建图形对象
        const graphics = this.scene.add.graphics();
        const radius = size / 2;

        // 绘制脉冲环 - 使用原始的线条粗细和颜色
        graphics.lineStyle(3, 0xff00ff, 1); // 亮紫色
        graphics.strokeCircle(radius, radius, radius - 2);

        // 生成纹理并清理
        const texture = graphics.generateTexture(key, size, size);
        graphics.destroy();

        return texture;
    }

    /**
     * 创建脉冲环效果 - 从内向外扩散的波纹
     */
    createPulseEffect() {
        // 创建脉冲环纹理
        const pulseTexture = this.createPulseTexture(`vortex_pulse_${this.range}`, this.range * 2);

        // 创建脉冲环精灵
        const pulseRing = this.scene.add.image(
            this.player.sprite.x,
            this.player.sprite.y,
            pulseTexture.key
        );

        // 设置精灵属性
        pulseRing.setOrigin(0.5, 0.5);
        pulseRing.setAlpha(0.7);
        pulseRing.setDepth(DepthManager.LAYERS.EFFECTS + 2);
        pulseRing.setScale(0.5); // 从内向外扩散

        // 创建脉冲动画
        this.scene.tweens.add({
            targets: pulseRing,
            scale: 1.2,
            alpha: 0,
            duration: 500,
            ease: 'Sine.easeOut',
            onComplete: () => {
                pulseRing.destroy();
            }
        });
    }

    /**
     * 攻击方法
     */
    attack() {
        // 计算伤害
        const damage = Phaser.Math.Between(this.damageMin, this.damageMax);

        // 确保敌人组存在
        if (!this.scene.enemiesGroup) {
            console.warn('敌人组不存在，无法设置碰撞检测');
            return;
        }

        // 创建漩涡武器
        this.createVortexWeapon(damage);
    }

    /**
     * 创建漩涡武器
     * @param {number} damage - 伤害值
     */
    createVortexWeapon(damage) {
        // 添加Y轴偏移量，使武器中心点与宠物中心点对齐
        const yOffset = 0; // 不需要偏移，居中显示

        // 创建漩涡武器精灵（用于碰撞检测，但设为透明）
        const vortexWeapon = this.weaponGroup.create(
            this.player.sprite.x,
            this.player.sprite.y + yOffset,
            'weaponD1'
        );

        // 设置漩涡武器属性
        vortexWeapon.setOrigin(0.5, 0.5); // 中心点对齐
        vortexWeapon.setAlpha(0); // 完全透明，只用于碰撞
        vortexWeapon.damage = damage;

        // 设置碰撞体
        vortexWeapon.body.setCircle(this.range);
        vortexWeapon.body.setOffset(
            (vortexWeapon.width / 2) - this.range,
            (vortexWeapon.height / 2) - this.range
        );

        // 添加漩涡武器碰撞检测
        const vortexCollider = this.scene.physics.add.overlap(
            vortexWeapon,
            this.scene.enemiesGroup,
            this.onWeaponHitEnemy,
            null,
            this
        );

        // 创建漩涡特效精灵（仅用于视觉效果）
        const vortexEffect = this.scene.add.sprite(
            this.player.sprite.x,
            this.player.sprite.y + yOffset,
            this.effectsLoaded ? 'd_effect_01' : 'weaponD1'
        );
        this.effectGroup.add(vortexEffect);

        // 设置漩涡特效的深度
        vortexEffect.customType = 'WeaponD1';
        if (typeof DepthManager !== 'undefined') {
            DepthManager.updateDepth(vortexEffect, 'WeaponD1', false);
        }

        // 设置漩涡特效属性
        vortexEffect.setOrigin(0.5, 0.5);
        // 计算当前范围相对于初始范围的比例
        const rangeRatio = this.range / this.initialRange;

        // 使用初始特效尺寸乘以比例
        vortexEffect.displayWidth = this.effectInitialWidth * rangeRatio;
        vortexEffect.displayHeight = this.effectInitialHeight * rangeRatio;

        // 如果特效资源已加载，播放动画
        if (this.effectsLoaded) {
            // 播放漩涡特效动画
            vortexEffect.play('d_effect_anim');
            vortexEffect.setAlpha(0.6); // 设置适当的透明度
        } else {
            // 如果特效资源未加载，设置一个简单的淡出效果
            this.scene.tweens.add({
                targets: vortexEffect,
                alpha: 0,
                duration: 300,
                onComplete: () => {
                    vortexEffect.destroy();
                }
            });
        }

        // 设置武器碰撞体的生命周期
        this.scene.time.delayedCall(this.activeTime, () => {
            // 移除碰撞检测
            if (vortexCollider) {
                vortexCollider.destroy();
            }
            // 销毁武器精灵
            vortexWeapon.destroy();
        });

        // 设置特效精灵的淡出效果
        this.scene.tweens.add({
            targets: vortexEffect,
            alpha: 0,
            duration: 300,
            delay: this.activeTime - 300, // 在武器生命周期结束前开始淡出
            onComplete: () => {
                vortexEffect.destroy();
            }
        });

        // 安全机制：确保特效精灵在武器生命周期结束后一定会被销毁
        this.scene.time.delayedCall(this.activeTime + 100, () => {
            if (vortexEffect && vortexEffect.active) {
                vortexEffect.destroy();
            }
        });

        // 设置脉冲效果计时器 - 创建向外扩散的波纹
        const pulseTimer = this.scene.time.addEvent({
            delay: this.pulseDelay,
            callback: this.createPulseEffect,
            callbackScope: this,
            loop: true
        });

        // 在武器生命周期结束时停止脉冲计时器
        this.scene.time.delayedCall(this.activeTime, () => {
            pulseTimer.destroy();
        });
    }

    /**
     * 武器击中敌人的回调
     * @param {Phaser.GameObjects.Sprite} weapon - 武器精灵
     * @param {Phaser.GameObjects.Sprite} enemySprite - 敌人精灵
     */
    onWeaponHitEnemy(weapon, enemySprite) {
        // 获取敌人实例
        const enemy = enemySprite.enemyInstance;
        if (!enemy || !enemy.isAlive) return;

        // 对敌人造成伤害
        const damaged = this.damageEnemy(enemy, weapon.damage);

        // 如果敌人还活着，应用击退效果
        if (damaged && enemy.isAlive && enemy.knockback) {
            // 计算击退方向（从武器中心指向敌人的方向）
            const knockbackAngle = Math.atan2(
                enemySprite.y - weapon.y,
                enemySprite.x - weapon.x
            );

            // 应用击退效果
            enemy.knockback(knockbackAngle, this.knockbackForce);
        }
    }

    /**
     * 重写update方法
     * @param {number} time - 当前时间
     */
    update(time) {
        // 调用父类update来处理攻击冷却
        super.update(time);

        // 更新所有武器和特效的位置，使其跟随宠物
        if (this.player && this.player.sprite) {
            // 更新武器组中的所有元素位置
            this.weaponGroup.getChildren().forEach(weapon => {
                weapon.x = this.player.sprite.x;
                weapon.y = this.player.sprite.y;
                // 更新碰撞体位置
                weapon.body.reset(weapon.x, weapon.y);
            });

            // 更新特效组中的主要漩涡特效位置
            this.effectGroup.getChildren().forEach(effect => {
                // 只更新主要漩涡特效，不更新脉冲环
                if (effect.texture && (effect.texture.key.indexOf('d_effect') !== -1 || effect.texture.key === 'weaponD1')) {
                    effect.x = this.player.sprite.x;
                    effect.y = this.player.sprite.y;
                }
            });
        }
    }

    /**
     * 销毁武器
     */
    destroy() {
        // 清理武器组中的所有精灵
        if (this.weaponGroup) {
            this.weaponGroup.clear(true, true);
        }

        // 清理特效组中的所有精灵
        if (this.effectGroup) {
            this.effectGroup.clear(true, true);
        }

        // 清空受影响敌人集合
        if (this.affectedEnemies) {
            this.affectedEnemies.clear();
        }

        console.log(`${this.name}武器已销毁，所有资源已清理`);
    }
}