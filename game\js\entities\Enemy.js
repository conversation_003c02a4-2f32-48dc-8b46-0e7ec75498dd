/**
 * 敌人基类
 * 所有敌人类型都继承自这个基类（也包含：物品掉落、权重等）
 */
class Enemy {
    // 掉落系统静态变量
    static consecutiveNoDrops = 0; // 连续未掉落次数
    static DROP_BASE_CHANCE = 0.4; // 基础掉落率40%
    static DROP_PITY_THRESHOLD = 4; // 保底阈值：连续4次未掉落后必定掉落
    /**
     * 创建敌人
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {string} texture - 纹理键名
     * @param {number} health - 生命值
     * @param {number} damage - 伤害值
     * @param {number} speed - 移动速度
     */
    constructor(scene, x, y, texture, health, damage, speed, isElite = false) {
        this.scene = scene;
        this.health = health;
        this.damage = damage;
        this.speed = speed;
        this.isAlive = true;
        this.isElite = isElite; // 是否为精英敌人
        this.isBeingKnockedBack = false; // 是否正在被击退

        // 添加击退系统相关属性
        this.knockbackStartPos = null; // 击退开始位置
        this.maxKnockbackDistance = 120; // 最大击退距离（约1-2个身位）
        this.knockbackFriction = 0.85; // 击退摩擦系数（越小减速越快）

        // 添加当前速度变量（用于实现移动惯性）
        this.currentVelocityX = 0;
        this.currentVelocityY = 0;

        // 伤害冷却系统
        this.lastDamageTime = 0;       // 上次受到伤害的时间
        this.damageInterval = 500;     // 伤害间隔（毫秒）

        // 创建精灵
        this.sprite = scene.physics.add.sprite(x, y, texture);
        this.sprite.enemyInstance = this; // 引用回敌人实例

        // 设置自定义类型，用于深度管理
        this.sprite.customType = 'Enemy';

        // 添加到敌人组
        if (scene.enemiesGroup) {
            scene.enemiesGroup.add(this.sprite);
        }

        // 设置碰撞箱
        this.setupCollision();
    }

    /**
     * 设置碰撞箱
     * 子类可以覆盖此方法以自定义碰撞箱
     */
    setupCollision() {
        // 获取子类定义的碰撞箱设置
        const collisionBox = this.constructor.COLLISION_BOX;

        if (collisionBox && this.sprite) {
            if (collisionBox.type === 'circle') {
                // 减小碰撞半径到原来的65%，使敌人更容易被玩家推开/穿过
                const reducedRadius = Math.floor(collisionBox.radius * 0.65);

                // 设置圆形碰撞箱
                this.sprite.body.setCircle(
                    reducedRadius,
                    collisionBox.offsetX + (collisionBox.radius - reducedRadius),
                    collisionBox.offsetY + (collisionBox.radius - reducedRadius)
                );

                // 设置基础物理属性
                this.sprite.body.setBounce(0);
                this.sprite.body.setDrag(0.5);

            } else if (collisionBox.type === 'rectangle') {
                // 减小矩形碰撞箱到原来的65%
                const reducedWidth = Math.floor(collisionBox.width * 0.65);
                const reducedHeight = Math.floor(collisionBox.height * 0.65);

                // 设置矩形碰撞箱
                this.sprite.body.setSize(
                    reducedWidth,
                    reducedHeight,
                    collisionBox.offsetX + (collisionBox.width - reducedWidth) / 2,
                    collisionBox.offsetY + (collisionBox.height - reducedHeight) / 2
                );

                // 设置高反弹系数
                this.sprite.body.setBounce(0.7);
            }
        }
    }

    /**
     * 更新深度值，确保渲染顺序正确
     * 深度值基于Y坐标，使得Y坐标较大的对象（更靠近屏幕底部）显示在前面
     */
    updateDepth() {
        if (!this.sprite) return;

        // 使用DepthManager来统一管理深度排序
        if (typeof DepthManager !== 'undefined') {
            // 确保自定义类型已设置
            if (!this.sprite.customType) {
                this.sprite.customType = 'Enemy';
            }

            // 使用统一的深度管理器更新深度
            DepthManager.updateDepth(this.sprite, this.sprite.customType, true);
        } else {
            // 兼容性代码：如果DepthManager不可用
            const baseDepth = this.scene.DEPTH_LAYERS ? this.scene.DEPTH_LAYERS.CHARACTERS : 300;
            this.sprite.setDepth(baseDepth + this.sprite.y / 10000);
        }
    }

    /**
     * 更新敌人行为
     * @param {Object} playerPosition - 玩家位置 {x, y}
     */
    update(playerPosition) {
        if (!this.isAlive || !this.sprite || !playerPosition) return;

        // 更新深度值
        this.updateDepth();

        // 处理击退状态
        if (this.isBeingKnockedBack) {
            // 如果有记录开始位置，检查最大击退距离
            if (this.knockbackStartPos) {
                const dx = this.sprite.x - this.knockbackStartPos.x;
                const dy = this.sprite.y - this.knockbackStartPos.y;
                const knockbackDistance = Math.sqrt(dx * dx + dy * dy);

                // 如果超过最大击退距离，停止击退状态
                if (knockbackDistance >= this.maxKnockbackDistance) {
                    this.isBeingKnockedBack = false;
                    this.knockbackStartPos = null;
                    return;
                }

                // 应用摩擦力，减缓移动速度
                if (this.sprite.body && this.knockbackFriction) {
                    const vx = this.sprite.body.velocity.x * this.knockbackFriction;
                    const vy = this.sprite.body.velocity.y * this.knockbackFriction;
                    this.sprite.body.setVelocity(vx, vy);

                    // 当速度足够小时结束击退状态
                    if (Math.abs(vx) < 10 && Math.abs(vy) < 10) {
                        this.isBeingKnockedBack = false;
                        this.knockbackStartPos = null;
                    }
                }
            }
            return;
        }

        // 计算朝向玩家的方向
        const dx = playerPosition.x - this.sprite.x;
        const dy = playerPosition.y - this.sprite.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // 检查是否接触玩家并造成伤害
        if (distance < 60 && this.scene.player && this.scene.player.takeDamage) {
            this.scene.player.takeDamage(this.damage);
        }

        // 不再需要手动计算分离，使用Phaser物理引擎

        // 如果玩家在一定距离内，向玩家移动
        if (distance > 10) {
            // 标准化方向向量
            const dirX = dx / distance;
            const dirY = dy / distance;

            // 直接使用朝向玩家的向量，不再需要手动计算分离
            const normalizedMoveX = dirX;
            const normalizedMoveY = dirY;

            // 计算目标速度
            const targetVelocityX = normalizedMoveX * this.speed / 60;
            const targetVelocityY = normalizedMoveY * this.speed / 60;

            // 计算当前速度的大小
            const currentSpeed = Math.sqrt(
                this.currentVelocityX * this.currentVelocityX +
                this.currentVelocityY * this.currentVelocityY
            );

            // 计算目标速度的大小
            const targetSpeed = Math.sqrt(
                targetVelocityX * targetVelocityX +
                targetVelocityY * targetVelocityY
            );

            // 动态惯性系数：
            // - 当速度很低时（刚起步），使用较大的惯性系数，快速达到目标速度
            // - 当已有一定速度时，使用较小的惯性系数，使转向更平滑
            let inertiaFactor;

            if (currentSpeed < targetSpeed * 0.5) {
                // 起步阶段：快速加速
                inertiaFactor = 0.3;
            } else {
                // 已有速度：平滑转向
                inertiaFactor = 0.05;
            }

            // 使用线性插值更新当前速度（添加惯性）
            this.currentVelocityX = this.currentVelocityX * (1 - inertiaFactor) + targetVelocityX * inertiaFactor;
            this.currentVelocityY = this.currentVelocityY * (1 - inertiaFactor) + targetVelocityY * inertiaFactor;

            // 使用物理引擎设置速度，而不是直接修改位置
            this.sprite.body.setVelocity(
                this.currentVelocityX * 60, // 转换为每秒像素
                this.currentVelocityY * 60
            );

            // 根据移动方向翻转精灵
            if (dx < 0) {
                this.sprite.setFlipX(true);
            } else {
                this.sprite.setFlipX(false);
            }
        }
    }

    /**
     * 受到伤害
     * @param {number} amount - 伤害量
     * @returns {boolean} 是否成功造成伤害
     */
    takeDamage(amount) {
        // 获取当前时间
        const currentTime = this.scene.time.now;

        // 检查是否在伤害冷却期内
        if (currentTime - this.lastDamageTime < this.damageInterval) {
            return false; // 在冷却期内，不造成伤害
        }

        // 更新上次受到伤害的时间
        this.lastDamageTime = currentTime;

        // 扣除生命值
        this.health -= amount;

        // 检查是否死亡
        if (this.health <= 0) {
            this.die();
        }

        return true; // 成功造成伤害
    }

    /**
     * 敌人死亡
     */
    die() {
        this.isAlive = false;

        // 随机掉落物品
        this.dropItem();

        // 销毁敌人
        this.destroy();
    }

    /**
     * 掉落物品
     */
    dropItem() {
        if (!this.sprite) return;

        const x = this.sprite.x;
        const y = this.sprite.y;
        let item = null;

        // 精英敌人100%掉落物品
        if (this.isElite) {
            // 1. 血量道具掉落：70%掉1个，30%掉2个
            const healthPotionCount = Math.random() < 0.7 ? 1 : 2;
            for (let i = 0; i < healthPotionCount; i++) {
                if (typeof HealthPotion !== 'undefined') {
                    const offsetX = (Math.random() - 0.5) * 40; // -20到20的随机偏移
                    const offsetY = (Math.random() - 0.5) * 40;
                    const healthPotion = new HealthPotion(this.scene, x + offsetX, y + offsetY, 25);
                    if (this.scene.items) {
                        this.scene.items.push(healthPotion);
                    }
                }
            }

            // 2. 高级货币掉落：70%钻石，30%金币
            const currencyType = Math.random() < 0.7 ? 'diamond' : 'goldCoin';
            const currencyOffsetX = (Math.random() - 0.5) * 40;
            const currencyOffsetY = (Math.random() - 0.5) * 40;

            if (currencyType === 'diamond' && typeof Diamond !== 'undefined') {
                const diamond = new Diamond(this.scene, x + currencyOffsetX, y + currencyOffsetY, 100);
                if (this.scene.items) {
                    this.scene.items.push(diamond);
                }
            } else if (currencyType === 'goldCoin' && typeof GoldCoin !== 'undefined') {
                const goldCoin = new GoldCoin(this.scene, x + currencyOffsetX, y + currencyOffsetY, 10);
                if (this.scene.items) {
                    this.scene.items.push(goldCoin);
                }
            }

            // 3. 高级经验掉落：60%紫色，40%蓝色，数量1-2个随机
            const expGemCount = Math.random() < 0.5 ? 1 : 2;
            for (let i = 0; i < expGemCount; i++) {
                if (typeof ExperienceGem !== 'undefined') {
                    const expType = Math.random() < 0.6 ? 'purple' : 'blue';
                    const expValue = expType === 'purple' ? 50 : 10;
                    const expOffsetX = (Math.random() - 0.5) * 40;
                    const expOffsetY = (Math.random() - 0.5) * 40;

                    const expGem = new ExperienceGem(this.scene, x + expOffsetX, y + expOffsetY, expValue);
                    if (this.scene.items) {
                        this.scene.items.push(expGem);
                    }
                }
            }

            // 重置连续未掉落计数
            Enemy.consecutiveNoDrops = 0;
            return;
        }

        // 普通敌人掉落逻辑
        // 计算实际掉落概率（基础概率 + 保底机制）
        let actualDropChance = Enemy.DROP_BASE_CHANCE;

        // 应用保底机制：连续未掉落次数越多，掉落概率越高
        if (Enemy.consecutiveNoDrops > 0) {
            actualDropChance += Enemy.consecutiveNoDrops * 0.1; // 每次未掉落增加10%概率
        }

        // 保底机制：连续4次未掉落后必定掉落
        const willDrop = Enemy.consecutiveNoDrops >= Enemy.DROP_PITY_THRESHOLD || Math.random() < actualDropChance;

        if (willDrop) {
            // 重置连续未掉落计数
            Enemy.consecutiveNoDrops = 0;

            // 使用权重系统决定掉落物品类型
            const dropType = this.selectDropByWeight();

            switch (dropType) {
                case 'greenGem': // 绿色宝石（小经验）
                    if (typeof ExperienceGem !== 'undefined') {
                        item = new ExperienceGem(this.scene, x, y, 2);
                    }
                    break;
                case 'blueGem': // 蓝色宝石（中经验）
                    if (typeof ExperienceGem !== 'undefined') {
                        item = new ExperienceGem(this.scene, x, y, 10);
                    }
                    break;
                case 'purpleGem': // 紫色宝石（大经验）
                    if (typeof ExperienceGem !== 'undefined') {
                        item = new ExperienceGem(this.scene, x, y, 50);
                        // 使用紫色宝石精灵图
                        if (item.sprite && this.scene.anims.exists('purpleGem_anim')) {
                            item.sprite.setTexture('purpleGem_anim');
                            item.sprite.play('purpleGem_anim');
                        }
                    }
                    break;
                case 'coin': // 银币
                    if (typeof Coin !== 'undefined') {
                        item = new Coin(this.scene, x, y, 1);
                    }
                    break;
                case 'goldCoin': // 金币
                    if (typeof GoldCoin !== 'undefined') {
                        item = new GoldCoin(this.scene, x, y, 10);
                    }
                    break;
                case 'diamond': // 钻石
                    if (typeof Diamond !== 'undefined') {
                        item = new Diamond(this.scene, x, y, 100);
                    }
                    break;
            }

            // 将物品添加到场景的物品数组中
            if (item && this.scene.items) {
                this.scene.items.push(item);
            }
        } else {
            // 增加连续未掉落计数
            Enemy.consecutiveNoDrops++;
        }
    }

    /**
     * 基于权重选择掉落物品类型
     * @returns {string} 选中的物品类型
     */
    selectDropByWeight() {
        // 定义掉落物品权重表
        const dropTable = [
            { type: 'greenGem', weight: 90 },   // 绿色（小经验）
            { type: 'blueGem', weight: 10 },    // 蓝色（中经验）
            { type: 'purpleGem', weight: 2 },   // 紫色（大经验）
            { type: 'coin', weight: 10 },       // 银币
            { type: 'goldCoin', weight: 7 },    // 金币
            { type: 'diamond', weight: 2 }      // 钻石
        ];

        // 计算总权重
        let totalWeight = 0;
        for (const item of dropTable) {
            totalWeight += item.weight;
        }

        // 生成随机数
        const random = Math.random() * totalWeight;

        // 基于权重选择物品
        let currentWeight = 0;
        for (const item of dropTable) {
            currentWeight += item.weight;
            if (random <= currentWeight) {
                return item.type;
            }
        }

        // 默认返回绿色宝石
        return 'greenGem';
    }

    /**
     * 击退效果
     * @param {number} angle - 击退角度
     * @param {number} force - 击退力度
     */
    knockback(angle, force) {
        if (this.sprite && this.isAlive) {
            // 记录击退开始位置
            this.knockbackStartPos = {
                x: this.sprite.x,
                y: this.sprite.y
            };

            // 增加初始冲量，确保开始时效果明显
            const initialForce = force * 1.2; // 加大初始力度

            // 计算击退方向
            const knockbackX = Math.cos(angle) * initialForce;
            const knockbackY = Math.sin(angle) * initialForce;

            // 使用物理引擎应用击退力
            this.sprite.body.setVelocity(knockbackX * 10, knockbackY * 10);

            // 标记正在被击退
            this.isBeingKnockedBack = true;

            // 安全保障：设置一个最大持续时间，防止敌人卡在击退状态
            this.scene.time.delayedCall(800, () => {
                if (this.sprite && this.isBeingKnockedBack) {
                    this.isBeingKnockedBack = false;
                    this.knockbackStartPos = null;
                }
            });
        }
    }

    /**
     * 获取敌人的碰撞半径
     * @returns {number} 碰撞半径
     */
    getCollisionRadius() {
        // 获取子类定义的碰撞箱设置
        const collisionBox = this.constructor.COLLISION_BOX;

        // 如果有定义圆形碰撞箱，返回其半径
        if (collisionBox && collisionBox.type === 'circle') {
            return collisionBox.radius;
        }

        // 如果有定义矩形碰撞箱，返回其对角线的一半作为近似半径
        if (collisionBox && collisionBox.type === 'rectangle') {
            const diagonal = Math.sqrt(
                collisionBox.width * collisionBox.width +
                collisionBox.height * collisionBox.height
            );
            return diagonal / 2;
        }

        // 默认返回30作为碰撞半径
        return 30;
    }

    /**
     * 销毁敌人对象
     */
    destroy() {
        if (this.sprite) {
            this.sprite.destroy();
            this.sprite = null;
        }
    }
}
