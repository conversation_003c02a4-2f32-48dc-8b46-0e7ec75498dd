/**
 * UI事件管理器
 * 管理UI事件的发布订阅系统
 */
class UIEventManager {
    constructor() {
        this.listeners = new Map(); // 存储事件监听器
        this.eventHistory = []; // 事件历史记录（用于调试）
        this.maxHistorySize = 100; // 最大历史记录数量

        console.log('UIEventManager初始化完成');
    }

    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     * @param {Object} context - 上下文对象
     * @returns {Object} 监听器对象，用于移除监听
     */
    on(event, callback, context = null) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }

        const listener = {
            callback,
            context,
            id: this.generateListenerId(),
            remove: () => this.removeListener(event, listener.id)
        };

        this.listeners.get(event).push(listener);

        console.log(`事件监听器已添加: ${event} (ID: ${listener.id})`);
        return listener;
    }

    /**
     * 移除事件监听器
     * @param {string} event - 事件名称
     * @param {string} listenerId - 监听器ID
     */
    removeListener(event, listenerId) {
        const eventListeners = this.listeners.get(event);
        if (eventListeners) {
            const index = eventListeners.findIndex(listener => listener.id === listenerId);
            if (index !== -1) {
                eventListeners.splice(index, 1);
                console.log(`事件监听器已移除: ${event} (ID: ${listenerId})`);

                // 如果没有监听器了，删除事件
                if (eventListeners.length === 0) {
                    this.listeners.delete(event);
                }
            }
        }
    }

    /**
     * 移除指定事件的所有监听器
     * @param {string} event - 事件名称
     */
    removeAllListeners(event = null) {
        if (event) {
            this.listeners.delete(event);
            console.log(`事件的所有监听器已移除: ${event}`);
        } else {
            this.listeners.clear();
            console.log('所有事件监听器已移除');
        }
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {...any} args - 事件参数
     */
    emit(event, ...args) {
        // 记录事件历史
        this.addToHistory(event, args);

        const eventListeners = this.listeners.get(event);
        if (eventListeners && eventListeners.length > 0) {
            // 复制监听器数组，防止在回调中修改原数组导致问题
            const listenersToCall = [...eventListeners];

            listenersToCall.forEach(listener => {
                try {
                    if (listener.context) {
                        listener.callback.call(listener.context, ...args);
                    } else {
                        listener.callback(...args);
                    }
                } catch (error) {
                    console.error(`事件回调执行错误 (${event}):`, error);
                }
            });

            console.log(`事件已触发: ${event}, 监听器数量: ${listenersToCall.length}`);
        }
    }

    /**
     * 一次性事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     * @param {Object} context - 上下文对象
     * @returns {Object} 监听器对象
     */
    once(event, callback, context = null) {
        const onceListener = this.on(event, (...args) => {
            callback.call(context, ...args);
            onceListener.remove();
        }, context);

        return onceListener;
    }

    /**
     * 检查是否有指定事件的监听器
     * @param {string} event - 事件名称
     * @returns {boolean} 是否有监听器
     */
    hasListeners(event) {
        const eventListeners = this.listeners.get(event);
        return eventListeners && eventListeners.length > 0;
    }

    /**
     * 获取指定事件的监听器数量
     * @param {string} event - 事件名称
     * @returns {number} 监听器数量
     */
    getListenerCount(event) {
        const eventListeners = this.listeners.get(event);
        return eventListeners ? eventListeners.length : 0;
    }

    /**
     * 生成监听器ID
     * @returns {string} 唯一ID
     */
    generateListenerId() {
        return 'listener_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    }

    /**
     * 添加事件到历史记录
     * @param {string} event - 事件名称
     * @param {Array} args - 事件参数
     */
    addToHistory(event, args) {
        this.eventHistory.push({
            event,
            args,
            timestamp: Date.now()
        });

        // 限制历史记录大小
        if (this.eventHistory.length > this.maxHistorySize) {
            this.eventHistory.shift();
        }
    }

    /**
     * 获取事件历史记录
     * @param {number} limit - 限制数量
     * @returns {Array} 事件历史记录
     */
    getEventHistory(limit = 10) {
        return this.eventHistory.slice(-limit);
    }

    /**
     * 清空事件历史记录
     */
    clearHistory() {
        this.eventHistory = [];
        console.log('事件历史记录已清空');
    }

    /**
     * 获取所有事件名称
     * @returns {Array} 事件名称数组
     */
    getAllEvents() {
        return Array.from(this.listeners.keys());
    }
}
