/**
 * 宠物004
 */
class Pet004 extends Pet {
    /** 缩放比例 */
    static SCALE = 3.0;

    /** 宠物间距 */
    static SPACING = 36;

    /**
     * 创建呲毛
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {Player|Pet} target - 跟随目标（玩家或其他宠物）
     * @param {Object} config - 配置参数
     */
    constructor(scene, target, config = {}) {
        // 调用父类构造函数，传入基本配置
        super(scene, target, {
            ...config,
            id: 'pet004',
            name: '呲毛',
            scale: Pet004.SCALE
            // 不设置速度，使用玩家速度
        });
    }

    /**
     * 加载宠物资源和创建精灵
     * @param {number} x - 初始X坐标
     * @param {number} y - 初始Y坐标
     */
    loadAssets(x, y) {
        // 检查第一帧纹理是否已加载
        if (!this.scene.textures.exists('pet_pet004_1')) {
            // 如果纹理未加载，尝试加载所有帧
            console.log('呲毛纹理未加载，尝试加载');

            // 加载所有帧图片
            for (let i = 1; i <= 4; i++) {
                const key = `pet_pet004_${i}`;
                const path = `assets/pets/character04/big_f${i}.png`;
                this.scene.load.image(key, path);
            }

            // 加载完成后创建动画和精灵
            this.scene.load.once('complete', () => {
                // 创建动画
                if (!this.scene.anims.exists('pet_pet004_anim')) {
                    this.scene.anims.create({
                        key: 'pet_pet004_anim',
                        frames: Array.from({ length: 4 }, (_, i) => ({
                            key: `pet_pet004_${i + 1}`
                        })),
                        frameRate: 10,
                        repeat: -1
                    });
                }

                this.createPetSprite(x, y);
            });

            // 开始加载
            this.scene.load.start();
        } else {
            // 纹理已加载，直接创建精灵
            this.createPetSprite(x, y);
        }
    }

    /**
     * 创建宠物精灵
     * @param {number} x - 初始X坐标
     * @param {number} y - 初始Y坐标
     */
    createPetSprite(x, y) {
        // 创建精灵，使用第一帧（不使用物理碰撞）
        this.sprite = this.scene.add.sprite(x, y, 'pet_pet004_1');
        this.sprite.setOrigin(0.5, 0.8); // 纹理位置数值
        console.log('创建呲毛');

        // 检查动画是否已创建
        if (this.scene.anims.exists('pet_pet004_anim')) {
            // 播放动画
            this.sprite.play('pet_pet004_anim');
            console.log('播放呲毛动画');
        } else {
            console.warn('呲毛动画未创建');
        }

        // 设置深度和缩放
        if (this.sprite) {
            this.sprite.customType = 'Pet';

            if (typeof DepthManager !== 'undefined') {
                DepthManager.updateDepth(this.sprite, 'Pet', true);
            }

            this.sprite.setScale(this.scale);
        }

        // 初始化宠物武器
        this.weapon = new B1(this.scene, this);
    }

}
