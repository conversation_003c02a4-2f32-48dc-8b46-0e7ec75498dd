/**
 * 通用工作台基类
 * 提供所有工作台的通用功能
 */
class BaseWorkstation extends StaticObject {
    constructor(scene, x, y) {
        // 先用axe纹理创建，后面会替换为精灵表
        super(scene, x, y, 'axe', 3.5);

        // 获取配置并重新设置缩放
        const config = this.constructor.getConfig();
        const scale = config.scale || 3.5;
        this.sprite.setScale(scale);

        // 从静态配置获取属性
        this.workstationId = config.id;
        this.cost = config.cost;
        this.buffType = config.buffType;
        this.spriteKey = config.spriteKey;
        this.spritePath = config.spritePath;
        this.petId = config.petId;

        // 获取当前等级和对应的buff值
        this.currentLevel = this.getWorkstationLevel();
        this.buffValue = this.getCurrentBuffValue();

        // Buff配置
        this.buffDuration = 180000;     // 3分钟(毫秒)
        this.buffCost = 50;             // buff费用

        // 加载精灵表资源
        this.loadWorkstationAssets();

        // 设置交互
        this.sprite.setInteractive();
        this.setupInteraction();

        // 根据拥有状态创建UI
        this.isOwned = window.gameData && window.gameData.isWorkstationOwned(this.workstationId);
        if (this.isOwned) {
            this.createBuffUI();
        } else {
            this.createLockedUI();
        }

        // 启动更新定时器
        this.updateTimer = this.scene.time.addEvent({
            delay: 1000, // 每秒更新
            callback: this.updateBuffUI,
            callbackScope: this,
            loop: true
        });

        // 创建宠物装饰动画
        this.createWorkstationPetAnimation();

        console.log(`${config.name}创建完成`);
    }

    /**
     * 加载工作台精灵表资源
     */
    loadWorkstationAssets() {
        // 检查是否已加载工作台精灵表
        if (!this.scene.textures.exists(this.spriteKey)) {
            // 获取帧配置
            const config = this.constructor.getConfig();
            const frameConfig = config.frameConfig || { frameWidth: 64, frameHeight: 64 };

            // 加载工作台精灵表
            this.scene.load.spritesheet(this.spriteKey, this.spritePath, {
                frameWidth: frameConfig.frameWidth,
                frameHeight: frameConfig.frameHeight
            });

            // 加载完成后创建动画和替换纹理
            this.scene.load.once('complete', () => {
                this.createWorkstationAnimation();
                this.replaceTexture();
            });

            // 开始加载
            this.scene.load.start();
        } else {
            // 精灵表已加载，直接创建动画和替换纹理
            this.createWorkstationAnimation();
            this.replaceTexture();
        }
    }

    /**
     * 创建工作台动画
     */
    createWorkstationAnimation() {
        const animKey = `${this.spriteKey}_anim`;
        if (!this.scene.anims.exists(animKey)) {
            this.scene.anims.create({
                key: animKey,
                frames: this.scene.anims.generateFrameNumbers(this.spriteKey, { start: 0, end: 3 }),
                frameRate: 4,
                repeat: -1
            });
        }
    }

    /**
     * 替换纹理为精灵表
     */
    replaceTexture() {
        if (this.sprite && this.scene.textures.exists(this.spriteKey)) {
            // 保存当前状态
            const oldX = this.sprite.x;
            const oldY = this.sprite.y;
            const oldScale = this.sprite.scaleX;
            const wasTinted = this.sprite.tintTopLeft !== 0xffffff;
            const tintValue = this.sprite.tintTopLeft;
            const oldSprite = this.sprite;

            // 销毁旧的静态精灵
            this.sprite.destroy();

            // 创建新的动画精灵
            this.sprite = this.scene.add.sprite(oldX, oldY, this.spriteKey);
            this.sprite.setScale(oldScale);

            // 恢复tint状态
            if (wasTinted) {
                this.sprite.setTint(tintValue);
            }

            // 启用物理系统
            this.scene.physics.add.existing(this.sprite, true);

            // 应用碰撞配置
            this.applyCollisionConfig();

            // 更新工作台组引用
            if (this.scene.workstationGroup && oldSprite && this.scene.workstationGroup.contains) {
                this.scene.workstationGroup.remove(oldSprite);
                this.scene.workstationGroup.add(this.sprite);
            }

            // 播放动画
            const animKey = `${this.spriteKey}_anim`;
            if (this.scene.anims.exists(animKey)) {
                this.sprite.play(animKey);
            }

            console.log('工作台纹理已替换为精灵表动画');
        }
    }

    /**
     * 检查buff是否激活
     */
    isBuffActive() {
        return window.gameData && window.gameData.isWorkstationBuffActive(this.workstationId);
    }

    /**
     * 获取buff剩余时间(秒)
     */
    getBuffTimeLeft() {
        return window.gameData ? window.gameData.getWorkstationBuffTimeLeft(this.workstationId) : 0;
    }

    /**
     * 获取buff持续时间
     */
    getBuffDuration() {
        // 检查是否拥有对应宠物，如果有则18分钟，否则3分钟
        if (window.gameData && window.gameData.isOwnedPet(this.petId)) {
            return 18 * 60 * 1000; // 18分钟
        }
        return this.buffDuration; // 3分钟
    }

    /**
     * 获取工作台当前等级
     */
    getWorkstationLevel() {
        return window.gameData ? window.gameData.getWorkstationLevel(this.workstationId) : 1;
    }

    /**
     * 获取当前等级对应的buff值
     */
    getCurrentBuffValue() {
        const level = this.getWorkstationLevel();
        const qualities = window.WorkstationConfig.qualities;
        return qualities[level] ? qualities[level].buffValue : 0.1;
    }

    /**
     * 获取当前等级对应的品质配置
     */
    getCurrentQuality() {
        const level = this.getWorkstationLevel();
        const qualities = window.WorkstationConfig.qualities;
        return qualities[level] || qualities[1];
    }

    /**
     * 升级工作台
     */
    upgradeWorkstation() {
        const success = window.gameData.upgradeWorkstation(this.workstationId);
        if (success) {
            // 更新等级和buff值
            this.currentLevel = this.getWorkstationLevel();
            this.buffValue = this.getCurrentBuffValue();

            // 重新创建UI
            this.destroyBuffUI();
            this.createBuffUI();

            // 更新钱币显示
            if (this.scene.updateCoinDisplay) {
                this.scene.updateCoinDisplay();
            }

            console.log(`工作台升级成功: ${this.workstationId} -> 等级 ${this.currentLevel}`);
        }
    }

    /**
     * 销毁buff UI
     */
    destroyBuffUI() {
        if (this.levelText) {
            this.levelText.destroy();
            this.levelText = null;
        }
        if (this.buffText) {
            this.buffText.destroy();
            this.buffText = null;
        }
        if (this.statusText) {
            this.statusText.destroy();
            this.statusText = null;
        }
        if (this.buyButtonText) {
            this.buyButtonText.destroy();
            this.buyButtonText = null;
        }
        if (this.upgradeButtonText) {
            this.upgradeButtonText.destroy();
            this.upgradeButtonText = null;
        }
    }

    /**
     * 购买buff
     */
    buyBuff() {
        if (!window.gameData) return false;

        const duration = this.getBuffDuration();
        return window.gameData.buyWorkstationBuff(this.workstationId, duration, this.buffCost);
    }

    /**
     * 设置交互
     */
    setupInteraction() {
        this.sprite.on('pointerdown', () => {
            this.onInteract();
        });
    }

    /**
     * 交互处理
     */
    onInteract() {
        if (!this.isOwned) {
            this.buyWorkstation();
        } else if (this.isBuffActive()) {
            const timeLeft = this.getBuffTimeLeft();
            console.log(`${this.constructor.getConfig().name}buff还有${timeLeft}秒`);
        } else {
            const success = this.buyBuff();
            if (success && this.scene.updateCoinDisplay) {
                this.scene.updateCoinDisplay();
                this.updateBuffUI();
            }
        }
    }

    /**
     * 创建buff UI显示
     */
    createBuffUI() {
        const x = this.sprite.x;
        const y = this.sprite.y;
        const quality = this.getCurrentQuality();

        // 创建等级和品质显示
        this.levelText = this.scene.add.text(x, y - 80, `等级${this.currentLevel} ${quality.name}`, {
            fontSize: '22px',
            fill: `#${quality.color.toString(16)}`,
            stroke: '#000000',
            strokeThickness: 3
        }).setOrigin(0.5).setDepth(1000);

        // 创建buff效果显示
        const config = this.constructor.getConfig();
        const buffTypeNames = {
            'maxHealth': '生命值上限',
            'moveSpeed': '移动速度',
            'attackPower': '攻击力',
            'attackRange': '攻击范围'
        };
        const buffTypeName = buffTypeNames[config.buffType] || '属性';
        this.buffText = this.scene.add.text(x, y - 60, `${buffTypeName} +${Math.round(this.buffValue * 100)}%`, {
            fontSize: '24px',
            fill: '#ffffff',
            stroke: '#000000',
            strokeThickness: 3
        }).setOrigin(0.5).setDepth(1000);

        // 创建状态文本
        this.statusText = this.scene.add.text(x, y - 40, '', {
            fontSize: '20px',
            fill: '#ffffff',
            stroke: '#000000',
            strokeThickness: 3
        }).setOrigin(0.5).setDepth(1000);

        // 创建购买按钮文本
        const duration = this.getBuffDuration();
        const minutes = Math.floor(duration / 60000);
        this.buyButtonText = this.scene.add.text(x, y + 50, `${minutes}分钟 ${this.buffCost}💰`, {
            fontSize: '22px',
            fill: '#ffffff',
            backgroundColor: '#3498db',
            padding: { x: 15, y: 10 }
        }).setOrigin(0.5).setInteractive().setDepth(1000);

        this.buyButtonText.on('pointerdown', () => {
            this.onInteract();
        });

        // 创建升级按钮（如果可以升级且拥有对应宠物）
        if (this.currentLevel < 5 && window.gameData && window.gameData.isOwnedPet(this.petId)) {
            const nextQuality = window.WorkstationConfig.qualities[this.currentLevel + 1];
            this.upgradeButtonText = this.scene.add.text(x, y - 100, `精灵升级 ${nextQuality.upgradeCost}💰`, {
                fontSize: '22px',
                fill: '#ffffff',
                backgroundColor: '#e74c3c',
                padding: { x: 18, y: 12 }
            }).setOrigin(0.5).setInteractive().setDepth(2000);

            this.upgradeButtonText.on('pointerdown', () => {
                this.upgradeWorkstation();
            });
        }

        // 初始更新UI
        this.updateBuffUI();
    }

    /**
     * 创建锁定状态UI
     */
    createLockedUI() {
        const x = this.sprite.x;
        const y = this.sprite.y;

        // 整个工作台变灰
        this.sprite.setTint(0x808080);

        // 创建购买信息
        this.buyWorkstationButton = this.scene.add.text(x, y + 60, `${this.cost}💰`, {
            fontSize: '24px',
            fill: '#ffffff',
            backgroundColor: '#34495e',
            padding: { x: 18, y: 12 }
        }).setOrigin(0.5).setInteractive().setDepth(1000);

        this.buyWorkstationButton.on('pointerdown', () => {
            this.buyWorkstation();
        });
    }

    /**
     * 购买工作台
     */
    buyWorkstation() {
        if (!window.gameData) return;

        const success = window.gameData.buyWorkstation(this.workstationId, this.cost);
        if (success) {
            // 购买成功，切换到解锁状态
            this.isOwned = true;
            this.sprite.clearTint(); // 恢复正常色彩
            this.destroyLockedUI();
            this.createBuffUI();
            if (this.scene.updateCoinDisplay) {
                this.scene.updateCoinDisplay();
            }
            console.log('工作台购买成功');
        }
    }

    /**
     * 销毁锁定UI
     */
    destroyLockedUI() {
        if (this.buyWorkstationButton) {
            this.buyWorkstationButton.destroy();
            this.buyWorkstationButton = null;
        }
    }

    /**
     * 更新buff UI显示
     */
    updateBuffUI() {
        if (!this.isOwned || !this.statusText || !this.buyButtonText) return;

        if (this.isBuffActive()) {
            const timeLeft = this.getBuffTimeLeft();
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            this.statusText.setText(`${minutes}:${seconds.toString().padStart(2, '0')}`);
            this.statusText.setFill('#27ae60');
            this.buyButtonText.setVisible(false);
        } else {
            this.statusText.setText('未激活');
            this.statusText.setFill('#e74c3c');
            this.buyButtonText.setVisible(true);
        }
    }

    /**
     * 创建工作台宠物装饰动画
     */
    createWorkstationPetAnimation() {
        // 检查是否拥有对应宠物
        if (window.gameData && window.gameData.isOwnedPet(this.petId)) {
            this.createDecorativePet();
        }
    }

    /**
     * 创建装饰性宠物精灵
     */
    createDecorativePet() {
        // 从子类获取宠物显示配置
        const petConfig = this.getPetDisplayConfig();
        if (!petConfig) return;

        const x = this.sprite.x + petConfig.offsetX;
        const y = this.sprite.y + petConfig.offsetY;

        // 根据配置加载资源和创建精灵
        this.loadPetAssets(petConfig, x, y);
    }

    /**
     * 获取宠物显示配置 - 子类必须实现
     */
    getPetDisplayConfig() {
        return null; // 基类返回null，子类重写
    }

    /**
     * 获取碰撞配置 - 子类必须实现
     */
    getCollisionConfig() {
        throw new Error('子类必须实现getCollisionConfig方法');
    }

    /**
     * 应用碰撞配置
     */
    applyCollisionConfig() {
        if (!this.sprite || !this.sprite.body) return;

        const collisionConfig = this.getCollisionConfig();

        if (collisionConfig.type === 'multiple') {
            // 禁用主精灵的碰撞体
            this.sprite.body.enable = false;
            // 处理多个碰撞箱
            this.createMultipleCollisions(collisionConfig.boxes);
        } else if (collisionConfig.type === 'rectangle') {
            this.sprite.body.setSize(collisionConfig.width, collisionConfig.height);
            this.sprite.body.setOffset(collisionConfig.offsetX, collisionConfig.offsetY);
        } else if (collisionConfig.type === 'circle') {
            this.sprite.body.setCircle(collisionConfig.radius, collisionConfig.offsetX, collisionConfig.offsetY);
        }
    }

    /**
     * 创建多个碰撞箱
     */
    createMultipleCollisions(boxes) {
        // 清理旧的额外碰撞体
        if (this.extraColliders) {
            this.extraColliders.forEach(collider => collider.destroy());
        }
        this.extraColliders = [];

        boxes.forEach(box => {
            const collider = this.scene.add.rectangle(
                this.sprite.x + box.offsetX,
                this.sprite.y + box.offsetY,
                box.width,
                box.height
            );
            collider.setVisible(false);
            this.scene.physics.add.existing(collider, true);

            // 添加到工作台组
            if (this.scene.workstationGroup) {
                this.scene.workstationGroup.add(collider);
            }

            this.extraColliders.push(collider);
        });
    }

    /**
     * 根据配置加载宠物资源
     */
    loadPetAssets(petConfig, x, y) {
        if (petConfig.resourceType === 'spritesheet') {
            // 精灵表类型
            if (!this.scene.textures.exists(petConfig.textureKey)) {
                this.scene.load.spritesheet(petConfig.textureKey, petConfig.path, petConfig.frameConfig);
                this.scene.load.once('complete', () => {
                    this.createPetSprite(petConfig, x, y);
                });
                this.scene.load.start();
            } else {
                this.createPetSprite(petConfig, x, y);
            }
        } else {
            // 单独图片类型
            if (!this.scene.textures.exists(`pet_${this.petId}_1`)) {
                for (let i = 1; i <= petConfig.frames; i++) {
                    this.scene.load.image(`pet_${this.petId}_${i}`, `${petConfig.basePath}${i}.png`);
                }
                this.scene.load.once('complete', () => {
                    this.createPetSprite(petConfig, x, y);
                });
                this.scene.load.start();
            } else {
                this.createPetSprite(petConfig, x, y);
            }
        }
    }

    /**
     * 创建宠物精灵
     */
    createPetSprite(petConfig, x, y) {
        // 创建装饰性宠物精灵
        const textureKey = petConfig.resourceType === 'spritesheet' ? petConfig.textureKey : `pet_${this.petId}_1`;
        this.decorativePet = this.scene.add.sprite(x, y, textureKey);

        // 应用翻转
        if (petConfig.flipX) {
            this.decorativePet.setFlipX(true);
        }
        this.decorativePet.setOrigin(petConfig.originX, petConfig.originY);
        this.decorativePet.setScale(petConfig.scale);

        // 创建动画（如果不存在）
        if (!this.scene.anims.exists(petConfig.animKey)) {
            if (petConfig.resourceType === 'spritesheet') {
                this.scene.anims.create({
                    key: petConfig.animKey,
                    frames: this.scene.anims.generateFrameNumbers(petConfig.textureKey, { start: 0, end: petConfig.frames - 1 }),
                    frameRate: petConfig.frameRate,
                    repeat: -1
                });
            } else {
                this.scene.anims.create({
                    key: petConfig.animKey,
                    frames: Array.from({ length: petConfig.frames }, (_, i) => ({
                        key: `pet_${this.petId}_${i + 1}`
                    })),
                    frameRate: petConfig.frameRate,
                    repeat: -1
                });
            }
        }

        // 播放动画
        this.decorativePet.play(petConfig.animKey);

        // 设置深度，启用Y轴排序（与地牢一致）
        this.decorativePet.customType = 'Pet';
        if (typeof DepthManager !== 'undefined') {
            DepthManager.updateDepth(this.decorativePet, 'Pet', true);
        }

        console.log('工作台装饰宠物创建完成');
    }

    /**
     * 销毁工作台
     */
    destroy() {
        // 清理定时器
        if (this.updateTimer) {
            this.updateTimer.destroy();
            this.updateTimer = null;
        }

        // 清理UI元素
        if (this.levelText) {
            this.levelText.destroy();
            this.levelText = null;
        }
        if (this.buffText) {
            this.buffText.destroy();
            this.buffText = null;
        }
        if (this.statusText) {
            this.statusText.destroy();
            this.statusText = null;
        }
        if (this.buyButtonText) {
            this.buyButtonText.destroy();
            this.buyButtonText = null;
        }
        if (this.upgradeButtonText) {
            this.upgradeButtonText.destroy();
            this.upgradeButtonText = null;
        }

        // 清理锁定UI
        this.destroyLockedUI();

        // 清理装饰宠物
        if (this.decorativePet) {
            this.decorativePet.destroy();
            this.decorativePet = null;
        }

        // 调用父类销毁
        super.destroy();
    }

    /**
     * 获取工作台信息
     */
    getInfo() {
        const config = this.constructor.getConfig();
        return {
            id: config.id,
            name: config.name,
            cost: config.cost,
            description: config.description,
            buffType: config.buffType,
            buffValue: config.buffValue
        };
    }
}
