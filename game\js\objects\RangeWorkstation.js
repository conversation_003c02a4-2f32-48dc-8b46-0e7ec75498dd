/**
 * 攻击范围工作台类
 * 继承通用工作台基类
 */
class RangeWorkstation extends BaseWorkstation {
    constructor(scene, x, y) {
        super(scene, x, y);
    }

    /**
     * 获取工作台静态配置
     * @returns {Object} 工作台配置信息
     */
    static getConfig() {
        return {
            id: 'range_workstation',
            name: '范围工作台',
            cost: 4,
            buffValue: 0.2, // +20%攻击范围
            buffType: 'attackRange',
            description: '提升攻击范围 +20%',
            spriteKey: 'range_workstation',
            spritePath: 'assets/tools/Iron_01-Sheet.png',
            petId: 'pet002',
            frameConfig: {
                frameWidth: 32,
                frameHeight: 48
            },
            scale: 3
        };
    }

    /**
     * 获取宠物显示配置
     */
    getPetDisplayConfig() {
        return {
            offsetX: 70,
            offsetY: 60,
            scale: 0.5,
            flipX: true, // 添加翻转配置
            originX: 0.5,
            originY: 0.65,
            resourceType: 'images',
            basePath: 'assets/pets/character02/slm_0',
            frames: 8,
            animKey: 'pet002_range_anim',
            frameRate: 10
        };
    }

    /**
     * 获取碰撞配置
     */
    getCollisionConfig() {
        return {
            type: 'rectangle',
            width: 80,
            height: 110,
            offsetX: 10,
            offsetY: 0
        };
    }
}
