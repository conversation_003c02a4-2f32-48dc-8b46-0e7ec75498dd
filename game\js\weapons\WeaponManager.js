/**
 * 武器管理器
 */
class WeaponManager {
    /**
     * 创建武器管理器
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {Player} player - 玩家对象
     */
    constructor(scene, player) {
        this.scene = scene;
        this.player = player;
        this.activeWeapons = [];  // 已获得的武器
        this.availableWeapons = []; // 所有可用武器（包括未获得的）

        // 初始化所有可用武器
        this.initializeWeapons();
    }

    /**
     * 初始化所有可用武器
     * 创建所有武器实例，但只激活默认武器
     */
    initializeWeapons() {
        // 创建默认武器A并设置为已获得
        const weaponA = new A(this.scene, this.player, true);
        this.availableWeapons.push(weaponA);
        this.activeWeapons.push(weaponA);

        // 创建魔杖武器B但设置为未获得
        const weaponB = new B(this.scene, this.player, false);
        this.availableWeapons.push(weaponB);

        // 创建武器C但设置为未获得
        const weaponC = new C(this.scene, this.player, false);
        this.availableWeapons.push(weaponC);

        // 创建武器D(漩涡)并设置为已获得，方便测试
        const weaponD = new D(this.scene, this.player, false);
        this.availableWeapons.push(weaponD);
        this.activeWeapons.push(weaponD);

        console.log(`武器管理器初始化完成: ${this.availableWeapons.length}个可用武器, ${this.activeWeapons.length}个已获得武器`);
    }

    /**
     * 装备武器
     * @param {Weapon} weapon - 武器对象
     */
    equipWeapon(weapon) {
        // 如果武器已经在activeWeapons中，不重复添加
        if (this.activeWeapons.some(w => w.name === weapon.name)) {
            console.log(`武器已装备: ${weapon.name}`);
            return;
        }

        // 设置为已获得并添加到激活武器列表
        weapon.isAcquired = true;
        this.activeWeapons.push(weapon);
        console.log(`装备武器: ${weapon.name}`);
    }

    /**
     * 获取武器
     * 将未获得的武器标记为已获得，并添加到激活武器列表
     * @param {string} weaponName - 武器名称
     * @returns {boolean} 是否成功获取武器
     */
    acquireWeapon(weaponName) {
        // 查找指定名称的武器
        const weapon = this.availableWeapons.find(w => w.name === weaponName && !w.isAcquired);

        if (weapon) {
            // 设置为已获得并添加到激活武器列表
            weapon.isAcquired = true;
            this.activeWeapons.push(weapon);
            console.log(`获取新武器: ${weapon.name}`);
            return true;
        }

        console.warn(`未找到未获得的武器: ${weaponName}`);
        return false;
    }

    /**
     * 更新所有武器
     * @param {number} time - 当前时间
     */
    update(time) {
        this.activeWeapons.forEach(weapon => {
            weapon.update(time);
        });
    }

    /**
     * 升级指定武器
     * @param {string} weaponName - 武器名称
     * @returns {boolean} 是否成功升级
     */
    upgradeWeapon(weaponName) {
        // 查找指定名称的武器
        const weapon = this.activeWeapons.find(w => w.name === weaponName);

        // 如果找到武器，调用其升级方法
        if (weapon) {
            return weapon.upgrade();
        }

        console.warn(`未找到名为 ${weaponName} 的武器`);
        return false;
    }
}
