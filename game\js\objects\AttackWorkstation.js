/**
 * 攻击力工作台类
 * 继承通用工作台基类
 */
class AttackWorkstation extends BaseWorkstation {
    constructor(scene, x, y) {
        super(scene, x, y);
    }

    /**
     * 获取工作台静态配置
     * @returns {Object} 工作台配置信息
     */
    static getConfig() {
        return {
            id: 'attack_workstation',
            name: '攻击工作台',
            cost: 3,
            buffValue: 0.2, // +20%攻击力
            buffType: 'attackPower',
            description: '提升攻击力 +20%',
            spriteKey: 'attack_workstation',
            spritePath: 'assets/tools/Anvil_02-Sheet.png',
            petId: 'pet003',
            frameConfig: {
                frameWidth: 96,
                frameHeight: 112
            },
            scale: 3
        };
    }

    /**
     * 获取宠物显示配置
     */
    getPetDisplayConfig() {
        return {
            offsetX: -15,
            offsetY: 40,
            scale: 3.8,
            originX: 0.5,
            originY: 0.52,
            resourceType: 'images',
            basePath: 'assets/pets/character03/Hobbit - run',
            frames: 10,
            animKey: 'pet_pet003_anim',
            frameRate: 10
        };
    }

    /**
     * 获取碰撞配置
     */
    getCollisionConfig() {
        return {
            type: 'multiple',
            boxes: [
                { width: 80, height: 100, offsetX: 100, offsetY: 0 },
                { width: 230, height: 30, offsetX: -50, offsetY: -20 },
                { width: 52, height: 120, offsetX: -130, offsetY: 0 }
            ]
        };
    }
}
