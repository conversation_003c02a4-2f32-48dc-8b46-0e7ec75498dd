/**
 * 主菜单场景
 */
class MainMenuScene extends Phaser.Scene {
    constructor() {
        super({ key: 'MainMenuScene' });
        this.uiManager = null;
    }

    create() {
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;

        // 创建UI管理器
        this.uiManager = new UIManager(this);
        this.uiManager.getTheme().setTheme('menu');

        // 添加背景
        this.add.rectangle(
            width / 2,
            height / 2,
            width,
            height,
            0xFF8C42  // 使用温暖橙色背景
        );

        // 创建标题文本
        this.uiManager.createComponent('text', 'title', {
            x: width / 2,
            y: height * 0.3,
            text: 'PALMOW',
            font: 'bold 36px Arial',
            color: '#ffffff',
            strokeColor: '#000000',
            strokeThickness: 6,
            originX: 0.5,
            originY: 0.5
        });

        // 创建地牢探险按钮
        this.uiManager.createComponent('button', 'dungeonButton', {
            x: width / 2,
            y: height * 0.35,
            width: 200,
            height: 60,
            text: '地牢探险',
            backgroundColor: 0x3498db,
            hoverColor: 0x2980b9,
            onClick: () => {
                this.scene.start('GameScene');
            }
        });

        // 创建家园按钮
        this.uiManager.createComponent('button', 'homeButton', {
            x: width / 2,
            y: height * 0.45,
            width: 200,
            height: 60,
            text: '家园',
            backgroundColor: 0x27ae60,
            hoverColor: 0x219653,
            onClick: () => {
                this.scene.start('HomeScene');
            }
        });

        // 创建宠物商店按钮
        this.uiManager.createComponent('button', 'petShopButton', {
            x: width / 2,
            y: height * 0.55,
            width: 200,
            height: 60,
            text: '精灵商店',
            backgroundColor: 0x9b59b6,
            hoverColor: 0x8e44ad,
            onClick: () => {
                this.scene.start('PetShopScene');
            }
        });

        // 创建精灵猎场按钮（灰色，不可点击）
        this.uiManager.createComponent('button', 'huntingButton', {
            x: width / 2,
            y: height * 0.65,
            width: 200,
            height: 60,
            text: '精灵猎场',
            backgroundColor: 0x888888,
            textColor: '#cccccc'
        });

        // 创建PAL三消按钮（灰色，不可点击）
        this.uiManager.createComponent('button', 'puzzleButton', {
            x: width / 2,
            y: height * 0.75,
            width: 200,
            height: 60,
            text: 'PAL三消',
            backgroundColor: 0x888888,
            textColor: '#cccccc'
        });

        // 强制设置灰色按钮样式（覆盖主题）
        const huntingButton = this.uiManager.getComponent('huntingButton');
        if (huntingButton) {
            huntingButton.applyStyle({
                backgroundColor: 0x95A5A6,
                textColor: '#BDC3C7'
            });
        }

        const puzzleButton = this.uiManager.getComponent('puzzleButton');
        if (puzzleButton) {
            puzzleButton.applyStyle({
                backgroundColor: 0x95A5A6,
                textColor: '#BDC3C7'
            });
        }

        // 移除钱币显示
        // this.createCoinDisplay();

        // 创建版权信息
        this.uiManager.createComponent('text', 'copyright', {
            x: width / 2,
            y: height * 0.9,
            text: '© 2023 PalMow',
            font: '16px Arial',
            color: '#ffffff',
            originX: 0.5,
            originY: 0.5
        });

        // 移除游戏介绍弹窗（已移动到StartScene）
        // this.showGameIntro();

        console.log('主菜单场景UI重构完成');
    }

    /**
     * 显示游戏介绍弹窗
     */
    showGameIntro() {
        // 每次都显示介绍弹窗

        // 游戏介绍文本
        const introText = `
#**以下内容经由AI涉密抽象过滤，具象内容请发起QA**

## 核心架构
基于[跨维度生成协同引擎](CDGE)构建神经网络工作流架构，实现任务节点的动态拓扑映射与自动化编排。工作流包含三大核心技术模块：

### 智能节点集群
- **语义指令编译系统**：实时编译自然语言需求为结构化指令流
- **多模态生成中枢**：通过LLM节点通过智能节点集群并行输出多维度开发要素
- **自动化优化引擎**：实现关键数字资产的智能生成与参数调优，减少人工干预环节80%
  -- 新增策划系统高维控制层：
  -- 数值平衡：通过动态均衡引擎实现参数泛化调节
  -- 玩法拓扑：采用决策树概率坍缩模型构建非线性内容架构

### 动态拓扑工作流
- 采用**有向无环图(DAG)结构**组织任务节点，支持并发执行与条件跳转
- 实现**参数化流程版本快照**：所有节点输入/输出端口明确定义数据类型(string/json/embedding)，确保版本回溯零误差
- **热寂周期压缩技术**：将传统90日开发周期压缩至72小时级迭代（验证数据见模块3）

### 效能验证系统
- **神经修剪机制**：HumanReview节点实现人工评估与AI输出的实时校准
- **量化效能看板**：
  -- 场景构建：耗时降至传统流程1/5（编程化表达→三维拓扑映射+自优化）
  -- 逻辑验证：通过CompareOutput节点自动检测代码冲突，减少调试耗时60%

## 制作者声明
- 本内容为技术切片，美术/音频均为临时占位符
- 禁止任何商业化使用，核心专利覆盖CDGE架构及动态拓扑工作流算法
- 效能数据基于同等复杂度任务对比传统UEUN等工作流测得`;

        // 禁用所有按钮交互
        this.setButtonsInteractive(false);

        // 创建全屏模态弹窗
        this.uiManager.createComponent('modal', 'gameIntro', {
            title: 'AIgame制作流实验切片',
            content: introText,
            depth: 1000,
            onClose: () => {
                // 恢复按钮交互
                this.setButtonsInteractive(true);
            }
        });
    }

    /**
     * 设置按钮交互状态
     */
    setButtonsInteractive(interactive) {
        const buttons = ['dungeonButton', 'homeButton', 'petShopButton'];
        buttons.forEach(buttonId => {
            const button = this.uiManager.getComponent(buttonId);
            if (button && button.gameObjects && button.gameObjects[0]) {
                if (interactive) {
                    button.gameObjects[0].setInteractive();
                } else {
                    button.gameObjects[0].disableInteractive();
                }
            }
        });
    }

    /**
     * 创建钱币显示
     */
    createCoinDisplay() {
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;

        // 获取累计钱币数量
        let totalCoins = 0;
        if (window.gameData) {
            try {
                totalCoins = window.gameData.getTotalCoins();
            } catch (error) {
                console.error('获取累计钱币失败:', error);
            }
        }

        // 创建钱币显示文本
        this.uiManager.createComponent('text', 'coinDisplay', {
            x: width / 2,
            y: height * 0.8,
            text: `💰 累计钱币: ${totalCoins}`,
            font: 'bold 18px Arial',
            color: '#f39c12',
            strokeColor: '#000000',
            strokeThickness: 2,
            originX: 0.5,
            originY: 0.5
        });

        console.log(`主菜单显示累计钱币: ${totalCoins}`);
    }

    /**
     * 场景销毁时清理UI
     */
    destroy() {
        if (this.uiManager) {
            this.uiManager.destroyAll();
            this.uiManager = null;
        }
        super.destroy();
    }
}
