/**
 * 局内道具系统
 * 定义所有在游戏中可获得的道具及其效果
 */

// 局内道具列表
const inGameItems = [
    //====================
    // 物品拾取相关道具
    //====================

    // 物品吸引范围提升 - 小幅度
    {
        id: 'attract_radius_small',
        name: '拾取范围+',
        description: '增加10%',
        type: 'player_buff',
        duration: 'temporary', // 临时加成（本局有效）
        target: 'player',
        property: 'attractRadius',
        value: 0.1,
        isMultiplier: true,
        icon: 'big',
        apply: function (player) {
            return BuffUtils.applyBuff(player, 'attractRadius', 0.1, true);
        }
    },

    // 物品吸引范围提升 - 中等幅度
    {
        id: 'attract_radius_medium',
        name: '拾取范围++',
        description: '增加20%',
        type: 'player_buff',
        duration: 'temporary',
        target: 'player',
        property: 'attractRadius',
        value: 0.2,
        isMultiplier: true,
        icon: 'big',
        apply: function (player) {
            return BuffUtils.applyBuff(player, 'attractRadius', 0.2, true);
        }
    },

    // 物品吸引范围提升 - 大幅度
    {
        id: 'attract_radius_large',
        name: '拾取范围+++',
        description: '增加30%',
        type: 'player_buff',
        duration: 'temporary',
        target: 'player',
        property: 'attractRadius',
        value: 0.3,
        isMultiplier: true,
        icon: 'big',
        apply: function (player) {
            return BuffUtils.applyBuff(player, 'attractRadius', 0.3, true);
        }
    },



    //====================
    // 玩家移动相关道具
    //====================

    // 移动速度提升 - 小幅度
    {
        id: 'move_speed_small',
        name: '移动速度+',
        description: '增加10%',
        type: 'player_buff',
        duration: 'temporary',
        target: 'player',
        property: 'moveSpeed',
        value: 0.1,
        isMultiplier: true,
        icon: 'spd',
        apply: function (player) {
            return BuffUtils.applyBuff(player, 'moveSpeed', 0.1, true);
        }
    },

    // 移动速度提升 - 中等幅度
    {
        id: 'move_speed_medium',
        name: '移动速度++',
        description: '增加20%',
        type: 'player_buff',
        duration: 'temporary',
        target: 'player',
        property: 'moveSpeed',
        value: 0.2,
        isMultiplier: true,
        icon: 'spd',
        apply: function (player) {
            return BuffUtils.applyBuff(player, 'moveSpeed', 0.2, true);
        }
    },

    // 移动速度提升 - 大幅度
    {
        id: 'move_speed_large',
        name: '移动速度+++',
        description: '增加30%',
        type: 'player_buff',
        duration: 'temporary',
        target: 'player',
        property: 'moveSpeed',
        value: 0.3,
        isMultiplier: true,
        icon: 'spd',
        apply: function (player) {
            return BuffUtils.applyBuff(player, 'moveSpeed', 0.3, true);
        }
    },

    //====================
    // 武器伤害相关道具
    //====================

    // 武器伤害提升 - 小幅度
    {
        id: 'weapon_damage_small',
        name: '武器伤害+',
        description: '增加10%',
        type: 'weapon_buff',
        duration: 'temporary',
        target: 'weapon',
        property: 'damageMin',
        secondProperty: 'damageMax',
        value: 0.1,
        isMultiplier: true,
        icon: 'atk',
        apply: function (player) {
            let success = false;
            if (player.weaponManager && player.weaponManager.activeWeapons) {
                player.weaponManager.activeWeapons.forEach(weapon => {
                    if (BuffUtils.applyBuff(weapon, 'damageMin', 0.1, true)) {
                        success = true;
                    }
                    if (BuffUtils.applyBuff(weapon, 'damageMax', 0.1, true)) {
                        success = true;
                    }
                });
            }
            return success;
        }
    },

    // 武器伤害提升 - 中等幅度
    {
        id: 'weapon_damage_medium',
        name: '武器伤害++',
        description: '增加20%',
        type: 'weapon_buff',
        duration: 'temporary',
        target: 'weapon',
        property: 'damageMin',
        secondProperty: 'damageMax',
        value: 0.2,
        isMultiplier: true,
        icon: 'atk',
        apply: function (player) {
            let success = false;
            if (player.weaponManager && player.weaponManager.activeWeapons) {
                player.weaponManager.activeWeapons.forEach(weapon => {
                    if (BuffUtils.applyBuff(weapon, 'damageMin', 0.2, true)) {
                        success = true;
                    }
                    if (BuffUtils.applyBuff(weapon, 'damageMax', 0.2, true)) {
                        success = true;
                    }
                });
            }
            return success;
        }
    },

    // 武器伤害提升 - 大幅度
    {
        id: 'weapon_damage_large',
        name: '武器伤害+++',
        description: '增加30%',
        type: 'weapon_buff',
        duration: 'temporary',
        target: 'weapon',
        property: 'damageMin',
        secondProperty: 'damageMax',
        value: 0.3,
        isMultiplier: true,
        icon: 'atk',
        apply: function (player) {
            let success = false;
            if (player.weaponManager && player.weaponManager.activeWeapons) {
                player.weaponManager.activeWeapons.forEach(weapon => {
                    if (BuffUtils.applyBuff(weapon, 'damageMin', 0.3, true)) {
                        success = true;
                    }
                    if (BuffUtils.applyBuff(weapon, 'damageMax', 0.3, true)) {
                        success = true;
                    }
                });
            }
            return success;
        }
    },

    //====================
    // 武器攻击速度相关道具
    //====================

    // 武器攻击速度提升 - 小幅度
    {
        id: 'weapon_cooldown_small',
        name: '攻击速度+',
        description: '增加10%',
        type: 'weapon_buff',
        duration: 'temporary',
        target: 'weapon',
        property: 'cooldownTime',
        value: -0.1, // 负值表示减少冷却时间
        isMultiplier: true,
        icon: 'atkspd',
        apply: function (player) {
            let success = false;
            if (player.weaponManager && player.weaponManager.activeWeapons) {
                player.weaponManager.activeWeapons.forEach(weapon => {
                    // 注意：这里使用负值，因为减少冷却时间意味着攻击速度提升
                    if (BuffUtils.applyBuff(weapon, 'cooldownTime', -0.1, true)) {
                        success = true;
                    }
                });
            }
            return success;
        }
    },

    // 武器攻击速度提升 - 中等幅度
    {
        id: 'weapon_cooldown_medium',
        name: '攻击速度++',
        description: '增加20%',
        type: 'weapon_buff',
        duration: 'temporary',
        target: 'weapon',
        property: 'cooldownTime',
        value: -0.2, // 负值表示减少冷却时间
        isMultiplier: true,
        icon: 'atkspd',
        apply: function (player) {
            let success = false;
            if (player.weaponManager && player.weaponManager.activeWeapons) {
                player.weaponManager.activeWeapons.forEach(weapon => {
                    // 注意：这里使用负值，因为减少冷却时间意味着攻击速度提升
                    if (BuffUtils.applyBuff(weapon, 'cooldownTime', -0.2, true)) {
                        success = true;
                    }
                });
            }
            return success;
        }
    },

    // 武器攻击速度提升 - 大幅度
    {
        id: 'weapon_cooldown_large',
        name: '攻击速度+++',
        description: '增加30%',
        type: 'weapon_buff',
        duration: 'temporary',
        target: 'weapon',
        property: 'cooldownTime',
        value: -0.3, // 负值表示减少冷却时间
        isMultiplier: true,
        icon: 'atkspd',
        apply: function (player) {
            let success = false;
            if (player.weaponManager && player.weaponManager.activeWeapons) {
                player.weaponManager.activeWeapons.forEach(weapon => {
                    // 注意：这里使用负值，因为减少冷却时间意味着攻击速度提升
                    if (BuffUtils.applyBuff(weapon, 'cooldownTime', -0.3, true)) {
                        success = true;
                    }
                });
            }
            return success;
        }
    },

    //====================
    // 武器范围相关道具
    //====================

    // 武器攻击范围提升 - 小幅度
    {
        id: 'weapon_range_small',
        name: '攻击范围+',
        description: '增加10%',
        type: 'weapon_buff',
        duration: 'temporary',
        target: 'weapon',
        property: 'range',
        secondProperty: 'width',
        value: 0.1,
        isMultiplier: true,
        icon: 'aoe',
        apply: function (player) {
            let success = false;
            if (player.weaponManager && player.weaponManager.activeWeapons) {
                player.weaponManager.activeWeapons.forEach(weapon => {
                    // 增加武器长度
                    if (BuffUtils.applyBuff(weapon, 'range', 0.1, true)) {
                        success = true;
                    }

                    // 增加武器宽度（使用更大的倍数让上下扩展更明显）
                    if (BuffUtils.applyBuff(weapon, 'width', 0.166, true)) {
                        success = true;
                    }

                    // 如果武器纹理存在，需要重新生成以适应新的尺寸
                    if (weapon.scene && weapon.scene.textures.exists('weaponA')) {
                        // 如果纹理已存在，先销毁它
                        weapon.scene.textures.remove('weaponA');

                        // 创建一个新的武器图像
                        const graphics = weapon.scene.make.graphics({ x: 0, y: 0 });
                        graphics.fillStyle(0xFFFFFF, 1);
                        graphics.fillRect(0, 0, weapon.range, weapon.width);

                        // 更新纹理
                        graphics.generateTexture('weaponA', weapon.range, weapon.width);
                        graphics.destroy();

                        console.log(`武器范围已更新: 长度=${weapon.range}, 宽度=${weapon.width}`);
                    }
                });
            }
            return success;
        }
    },

    // 武器攻击范围提升 - 中等幅度
    {
        id: 'weapon_range_medium',
        name: '攻击范围++',
        description: '增加20%',
        type: 'weapon_buff',
        duration: 'temporary',
        target: 'weapon',
        property: 'range',
        secondProperty: 'width',
        value: 0.2,
        isMultiplier: true,
        icon: 'aoe',
        apply: function (player) {
            let success = false;
            if (player.weaponManager && player.weaponManager.activeWeapons) {
                player.weaponManager.activeWeapons.forEach(weapon => {
                    // 增加武器长度
                    if (BuffUtils.applyBuff(weapon, 'range', 0.2, true)) {
                        success = true;
                    }

                    // 增加武器宽度（使用更大的倍数让上下扩展更明显）
                    if (BuffUtils.applyBuff(weapon, 'width', 0.33, true)) {
                        success = true;
                    }

                    // 如果武器纹理存在，需要重新生成以适应新的尺寸
                    if (weapon.scene && weapon.scene.textures.exists('weaponA')) {
                        // 如果纹理已存在，先销毁它
                        weapon.scene.textures.remove('weaponA');

                        // 创建一个新的武器图像
                        const graphics = weapon.scene.make.graphics({ x: 0, y: 0 });
                        graphics.fillStyle(0xFFFFFF, 1);
                        graphics.fillRect(0, 0, weapon.range, weapon.width);

                        // 更新纹理
                        graphics.generateTexture('weaponA', weapon.range, weapon.width);
                        graphics.destroy();

                        console.log(`武器范围已更新: 长度=${weapon.range}, 宽度=${weapon.width}`);
                    }
                });
            }
            return success;
        }
    },

    // 武器攻击范围提升 - 大幅度
    {
        id: 'weapon_range_large',
        name: '攻击范围+++',
        description: '增加30%',
        type: 'weapon_buff',
        duration: 'temporary',
        target: 'weapon',
        property: 'range',
        secondProperty: 'width',
        value: 0.3,
        isMultiplier: true,
        icon: 'aoe',
        apply: function (player) {
            let success = false;
            if (player.weaponManager && player.weaponManager.activeWeapons) {
                player.weaponManager.activeWeapons.forEach(weapon => {
                    // 增加武器长度
                    if (BuffUtils.applyBuff(weapon, 'range', 0.3, true)) {
                        success = true;
                    }

                    // 增加武器宽度（使用更大的倍数让上下扩展更明显）
                    if (BuffUtils.applyBuff(weapon, 'width', 0.5, true)) {
                        success = true;
                    }

                    // 如果武器纹理存在，需要重新生成以适应新的尺寸
                    if (weapon.scene && weapon.scene.textures.exists('weaponA')) {
                        // 如果纹理已存在，先销毁它
                        weapon.scene.textures.remove('weaponA');

                        // 创建一个新的武器图像
                        const graphics = weapon.scene.make.graphics({ x: 0, y: 0 });
                        graphics.fillStyle(0xFFFFFF, 1);
                        graphics.fillRect(0, 0, weapon.range, weapon.width);

                        // 更新纹理
                        graphics.generateTexture('weaponA', weapon.range, weapon.width);
                        graphics.destroy();

                        console.log(`武器范围已更新: 长度=${weapon.range}, 宽度=${weapon.width}`);
                    }
                });
            }
            return success;
        }
    }

    // 未来可以在这里添加更多道具
];

/**
 * 获取随机道具选项
 * @param {number} count - 需要的道具数量
 * @returns {Array} - 随机选择的道具数组
 */
// 将函数定义为全局函数，使其可以在其他文件中访问
window.getRandomItems = function (count = 3) {
    // 如果道具数量不足，返回所有道具
    if (inGameItems.length <= count) {
        return [...inGameItems];
    }

    // 随机选择指定数量的道具
    const selectedItems = [];
    const availableIndices = Array.from({ length: inGameItems.length }, (_, i) => i);

    for (let i = 0; i < count; i++) {
        // 随机选择一个索引
        const randomIndex = Math.floor(Math.random() * availableIndices.length);
        const itemIndex = availableIndices[randomIndex];

        // 添加到选中列表并从可用列表中移除
        selectedItems.push(inGameItems[itemIndex]);
        availableIndices.splice(randomIndex, 1);
    }

    return selectedItems;
}

/**
 * 应用道具效果
 * @param {Object} item - 道具对象
 * @param {Object} player - 玩家对象
 * @returns {boolean} - 是否成功应用
 */
window.applyItem = function (item, player) {
    return BuffUtils.applyItem(item, player);
}
