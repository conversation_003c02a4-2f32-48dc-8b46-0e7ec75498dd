/**
 * UI模态弹窗组件
 * 用于显示需要用户确认的重要信息
 */
class UIModal extends UIComponent {
    constructor(scene, config = {}) {
        super(scene, config);

        // 模态弹窗特有配置
        this.title = config.title || '提示';
        this.content = config.content || '';
        this.showCloseButton = config.showCloseButton !== false;
        this.onClose = config.onClose || null;
        this.backgroundColor = config.backgroundColor || 0x000000;
        this.backgroundAlpha = config.backgroundAlpha || 0.7;
        this.modalColor = config.modalColor || 0xffffff;
        this.textColor = config.textColor || '#000000';
        this.buttonColor = config.buttonColor || 0x3498db;

        // 创建组件
        this.create();
    }

    create() {
        const width = this.scene.cameras.main.width;
        const height = this.scene.cameras.main.height;

        // 创建全屏背景遮罩
        this.overlay = this.scene.add.rectangle(
            width / 2,
            height / 2,
            width,
            height,
            0x000000,
            0.9
        );
        this.overlay.setDepth(this.depth);
        this.overlay.setScrollFactor(0);
        this.overlay.setInteractive();
        this.gameObjects.push(this.overlay);

        // 全屏模态框背景
        const modalWidth = width * 0.95;
        const modalHeight = height * 0.9;

        // 创建弹窗背景
        this.modalBg = this.scene.add.rectangle(
            width / 2,
            height / 2,
            modalWidth,
            modalHeight,
            0xffffff
        );
        this.modalBg.setDepth(this.depth + 1);
        this.modalBg.setScrollFactor(0);
        this.modalBg.setStrokeStyle(3, 0x3498db);
        this.gameObjects.push(this.modalBg);

        // 创建标题文本
        this.titleText = this.scene.add.text(
            width / 2,
            height / 2 - modalHeight / 2 + 60,
            this.title,
            {
                font: 'bold 32px Arial',
                fill: '#2c3e50',
                align: 'center'
            }
        );
        this.titleText.setOrigin(0.5);
        this.titleText.setDepth(this.depth + 2);
        this.titleText.setScrollFactor(0);
        this.gameObjects.push(this.titleText);

        // 创建HTML内容区域
        const contentY = height / 2 - modalHeight / 2 + 120;
        const contentHeight = modalHeight - 200;

        // 创建HTML div元素
        const htmlContent = `
            <div style="
                width: ${modalWidth - 40}px;
                height: ${contentHeight}px;
                background: white;
                border: 2px solid #3498db;
                border-radius: 8px;
                padding: 20px;
                box-sizing: border-box;
                overflow-y: auto;
                font-family: Arial, sans-serif;
                font-size: 16px;
                line-height: 1.6;
                color: #2c3e50;
            ">
                <pre style="
                    white-space: pre-wrap;
                    word-wrap: break-word;
                    margin: 0;
                    font-family: Arial, sans-serif;
                ">${this.content}</pre>
            </div>
        `;

        // 创建DOM元素
        this.domElement = this.scene.add.dom(
            width / 2,
            contentY + contentHeight / 2,
            'div'
        ).setHTML(htmlContent);
        this.domElement.setDepth(this.depth + 2);
        this.domElement.setScrollFactor(0);
        this.gameObjects.push(this.domElement);

        // 创建大号关闭按钮
        if (this.showCloseButton) {
            this.closeButton = this.scene.add.rectangle(
                width / 2,
                height / 2 + modalHeight / 2 - 60,
                200,
                60,
                0x3498db
            );
            this.closeButton.setDepth(this.depth + 2);
            this.closeButton.setScrollFactor(0);
            this.closeButton.setInteractive();
            this.gameObjects.push(this.closeButton);

            this.closeButtonText = this.scene.add.text(
                width / 2,
                height / 2 + modalHeight / 2 - 60,
                '我知道了',
                {
                    font: 'bold 24px Arial',
                    fill: '#ffffff'
                }
            );
            this.closeButtonText.setOrigin(0.5);
            this.closeButtonText.setDepth(this.depth + 3);
            this.closeButtonText.setScrollFactor(0);
            this.gameObjects.push(this.closeButtonText);

            // 添加按钮交互
            this.closeButton.on('pointerover', () => {
                this.closeButton.setFillStyle(0x2980b9);
                this.scene.game.canvas.style.cursor = 'pointer';
            });

            this.closeButton.on('pointerout', () => {
                this.closeButton.setFillStyle(0x3498db);
                this.scene.game.canvas.style.cursor = 'default';
            });

            this.closeButton.on('pointerdown', () => {
                this.close();
            });
        }

        console.log('UIModal创建完成');
    }

    /**
     * 关闭模态弹窗
     */
    close() {
        if (this.onClose) {
            this.onClose();
        }
        this.destroy();
    }

    /**
     * 销毁组件
     */
    destroy() {
        if (this.destroyed) return;

        // 恢复鼠标样式
        this.scene.game.canvas.style.cursor = 'default';

        // 销毁所有游戏对象
        this.gameObjects.forEach(obj => {
            if (obj && obj.destroy) {
                obj.destroy();
            }
        });

        this.gameObjects = [];
        this.destroyed = true;

        console.log('UIModal已销毁');
    }
}
