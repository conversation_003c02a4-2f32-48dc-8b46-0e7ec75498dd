/**
 * 经验宝石类 - 敌人死亡后掉落，玩家收集后获得经验
 */
class ExperienceGem {
    /** 缩放比例 */
    static SCALE = 1.3;

    /** 碰撞箱设置 */
    static COLLISION_BOX = {
        type: 'circle',  // 圆形碰撞箱
        radius: 5,       // 半径
        offsetX: 0,      // X偏移
        offsetY: 0       // Y偏移
    };

    /**
     * 创建一个新的经验宝石
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {number} x - 宝石X坐标
     * @param {number} y - 宝石Y坐标
     * @param {number} value - 经验值大小
     */
    constructor(scene, x, y, value = 2) {
        this.scene = scene;
        this.value = value;
        this.type = 'experienceGem'; // 添加类型标识

        // 根据经验值大小选择宝石类型
        let gemType = 'greenGem'; // 默认为绿色宝石（小经验）

        if (value >= 20) {
            gemType = 'purpleGem'; // 紫色宝石（大经验）
        } else if (value >= 10) {
            gemType = 'blueGem'; // 蓝色宝石（中经验）
        }

        // 确保宝石纹理已创建
        if (!scene.textures.exists('greenGem') || !scene.textures.exists('blueGem') || !scene.textures.exists('purpleGem')) {
            this.createGemTextures(scene);
        }

        // 紫色宝石使用与绿色和蓝色宝石相同的实现方式

        // 创建宝石精灵
        this.sprite = scene.physics.add.sprite(x, y, gemType);
        this.sprite.setScale(ExperienceGem.SCALE); // 使用静态缩放比例

        // 设置物理属性
        this.sprite.body.setCircle(
            ExperienceGem.COLLISION_BOX.radius,
            ExperienceGem.COLLISION_BOX.offsetX,
            ExperienceGem.COLLISION_BOX.offsetY
        );

        // 添加引用以便在碰撞时获取
        this.sprite.gemInstance = this;
        this.sprite.customType = 'ExperienceGem'; // 设置自定义类型

        // 确保宝石可见
        this.sprite.setScrollFactor(1); // 确保宝石随相机移动，与游戏世界保持一致

        // 使用DepthManager设置深度
        DepthManager.updateDepth(this.sprite, 'ExperienceGem', true); // 启用Y轴排序

        // 添加缓动动画效果
        this.addAnimations();
    }

    /**
     * 创建宝石纹理
     * @param {Phaser.Scene} scene - 游戏场景
     */
    createGemTextures(scene) {
        try {
            console.log(`ExperienceGem: 开始创建宝石纹理`);

            // 创建图形对象
            let graphics = scene.make.graphics({ x: 0, y: 0, add: false });

            // 绿色宝石（小经验）- 使用12x12尺寸
            graphics.clear();
            graphics.fillStyle(0x0d933d, 1); // 绿色
            graphics.fillCircle(6, 6, 5); // 将坐标和半径都缩小为原来的一半
            // 添加高光效果
            graphics.fillStyle(0xffffff, 0.6);
            graphics.fillCircle(4, 4, 1); // 高光也相应缩小
            // 添加外边框
            graphics.lineStyle(1, 0xffffff, 0.8);
            graphics.strokeCircle(5, 5, 4);

            // 生成绿色宝石纹理 - 使用12x12尺寸
            graphics.generateTexture('greenGem', 12, 12);

            // 蓝色宝石（中经验）- 使用12x12尺寸
            graphics.clear();
            graphics.fillStyle(0x4488ff, 1); // 蓝色
            graphics.fillCircle(6, 6, 5);
            // 添加高光效果
            graphics.fillStyle(0xffffff, 0.6);
            graphics.fillCircle(4, 4, 1);
            // 添加外边框
            graphics.lineStyle(1, 0xffffff, 0.8);
            graphics.strokeCircle(5, 5, 4);

            // 生成蓝色宝石纹理 - 使用12x12尺寸
            graphics.generateTexture('blueGem', 12, 12);

            // 紫色宝石（大经验）- 使用12x12尺寸
            graphics.clear();
            graphics.fillStyle(0x9932cc, 1); // 紫色
            graphics.fillCircle(6, 6, 5);
            // 添加高光效果
            graphics.fillStyle(0xffffff, 0.6);
            graphics.fillCircle(5, 5, 1);
            // 添加外边框
            graphics.lineStyle(1, 0xffffff, 0.8);
            graphics.strokeCircle(6, 6, 5);

            // 生成紫色宝石纹理 - 使用12x12尺寸
            graphics.generateTexture('purpleGem', 12, 12);

            // 清理图形对象
            graphics.destroy();

            return true;
        } catch (error) {
            console.error(`ExperienceGem: 创建宝石纹理时出错: ${error.message}`);
            return false;
        }
    }

    /**
     * 添加动画效果
     */
    addAnimations() {
        try {
            // 脉动效果 - 减小脉动幅度
            this.pulseTween = this.scene.tweens.add({
                targets: this.sprite,
                scale: { from: ExperienceGem.SCALE, to: ExperienceGem.SCALE * 1.2 }, // 使用静态缩放比例
                duration: 500,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        } catch (error) {
            console.error('添加宝石动画时出错:', error);
        }
    }

    // 紫色宝石使用与绿色和蓝色宝石相同的实现方式


    /**
     * 收集宝石
     */
    collect() {
        if (!this.sprite || !this.sprite.active) return;

        // 触发经验收集事件
        this.scene.events.emit('experienceCollected', this.value);

        // 显示收集特效
        this.showCollectEffect();

        // 销毁宝石
        this.destroy();
    }

    /**
     * 显示收集特效
     */
    showCollectEffect() {
        return;
    }

    /**
     * 销毁宝石
     */
    destroy() {
        if (this.sprite) {
            this.sprite.destroy();
            this.sprite = null;
        }

        // 停止所有动画
        if (this.pulseTween) this.pulseTween.stop();
    }
}
