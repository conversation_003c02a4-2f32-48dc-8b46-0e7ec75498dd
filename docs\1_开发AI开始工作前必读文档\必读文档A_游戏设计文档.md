# 《mini》设计文档 / 《mini》 Creature Workshop



## 1. 概述

### 1.1 常规信息

- **类型**：#roguelike #割草 #宠物养成
+ **平台**：
  + 移动设备（iOS/Android）触屏操作
  + 桌面浏览器（鼠标/键盘）
+ **跨平台适配策略**
  + 响应式UI设计：基于CSS Grid和Flexbox实现各尺寸屏幕自适应
  + 输入模式自动检测：根据设备类型切换触控/鼠标键盘操作方式
  + 性能分级渲染：根据设备性能调整粒子效果和同屏单位数量
  + 存储同步：使用IndexedDB+云存储确保跨设备游戏进度同步
  + 网络弱联机制：支持临时离线游戏，恢复网络后同步数据

### 1.2 成长主循环

```mermaid
flowchart LR
    A[肉鸽关卡探索] --> B{资源/宠物获取}
    B -->|成功| C[系统成长]
    B -->|失败| D[战斗强化]
    C --> E[战力提升]
    D --> E
    E --> A
```

### 1.3 局内循环曲线

- **游戏时长**: 标准模式15分钟，每分钟作为一个难度阶段
- **难度增长公式[以实际开发为准]**: `难度系数 = 基础难度 + (当前分钟 × 难度增长率)`
  - 基础难度: 1.0
  - 难度增长率: 约0.1-0.2/分钟(前期)，0.3-0.4/分钟(后期)
- **敌人属性缩放[以实际开发为准]**:
  - 生命值 = 基础生命值 × (1 + (当前分钟-1) × 0.1)
  - 数量 = 基础数量 × (1 + (当前分钟-1) × 0.07)
  - 速度 = 基础速度 × (1 + (当前分钟-1) × 0.03)

### 1.4 帧率与更新频率

<!--技术选型以实际开发为准-->
- **游戏帧率**: 60FPS
- **物理更新**: 每帧计算
- **敌人AI更新**:
  - 屏幕内敌人: 每帧更新
  - 屏幕外敌人: 每3-5帧更新一次
- **碰撞检测优化**: 使用四叉树或空间哈希进行区域划分



## 3.gameplay机制

### Tilemap生成

*   **实现方式**: 使用 Phaser 的 `this.add.tileSprite()` 创建背景。
*   **位置**: `GameScene.create()` 方法中创建 `TileSprite`。
*   **滚动**: 在 `GameScene.update()` 中，根据 `this.player.sprite.body.velocity` 更新 `tileSprite.tilePositionX` 和 `tileSprite.tilePositionY` 实现无限滚动效果。
*   **地图元素**(暂不实现):  随机生成树木等。

### 4.1 敌人生成算法

- **生成区域**: 玩家周围一定范围外，屏幕外
  - 最小距离: 屏幕对角线长度 × 0.6
  - 最大距离: 屏幕对角线长度 × 1.2

- **生成频率**:
  - 基础生成间隔: 1-3秒
  - 随时间缩短: `实际间隔 = 基础间隔 × (1 - 当前分钟 × 0.02)`
  - 最小间隔: 0.3秒

- **生成数量**:
  - 基础数量: 3-5个/波
  - 随时间增加: `实际数量 = 基础数量 × (1 + 当前分钟 × 0.1)`
  - 最大数量: 30个/波

### player等级机制

**经验值计算公式**：
- 玩家初始等级：0级
- 累计经验公式：n=角色等级，累计经验 = 5 × n²。例如：10级累计经验为 5×10²=500，20级累计经验为 5×20²=2000
- 每级所需经验：0级升1级需要5XP，1级升2级需要（20-5）XP，2级升3级需要（45-20）XP

| 等级 | 累计经验 | 本级所需经验 |
|------|---------|-------------|
| 0→1  | 5       | 5           |
| 1→2  | 20      | 15          |
| 2→3  | 45      | 25          |
| 3→4  | 80      | 35          |
| 4→5  | 125     | 45          |
| 5→6  | 180     | 55          |
| 6→7  | 245     | 65          |
| 7→8  | 320     | 75          |
| 8→9  | 405     | 85          |
| 9→10 | 500     | 95          |
……

**战斗强化机制**：
- 每次升级时，玩家可以选择一项升级（武器或被动道具）
- 升级选项由升级选项生成算法决定（详见"升级选项生成算法"章节）
- 等级本身不直接提升角色属性，但通过选择的升级间接提升

### 升级选项生成算法

- **选项数量**: 每次角色升级提供2个选项
- **选项池分类**:
  - 武器池: 未获得武器 > 已有武器升级
  - 被动道具池: 未获得道具 > 已有道具升级
  - 特殊选项池（暂不开发）: 跳过、重选、金币等

- **选项生成步骤**:
  1. 确定选项类型分布(武器/道具/特殊)
  2. 应用权重系统选择具体项目
   - *已拥有项目权重提升(+20%)*
   - *长时间未出现项目权重提升(+5%/次)*
   - *玩家选择历史影响(常选项目权重降低)*
  3. 确保不重复且符合解锁条件



## 宠物系统

生命工作台	Grill_03-Sheet.png	pet004(呲毛)	血量上限+20%	health_workstation
移动速度工作台	Alchemy_Table_03-Sheet.png	pet002(草)	移动速度+20%	speed_workstation
攻击力工作台	Anvil_02-Sheet.png	pet003(人)	攻击力+20%	attack_workstation
攻击范围工作台	Iron_01-Sheet.png	pet001(喵)	攻击范围+20%	range_workstation


## 怪物体系

### 局内战斗系统

**系统架构设计**
- 生成控制系统
  *动态怪物池 : 基于游戏时间构建可生成怪物类型集合
  *累积解锁机制 : 新时间点解锁新怪物类型，保留已解锁类型
- 难度参数调整
<!--该部分数值以实际开发为准-->
  *生成频率 : 初始5秒/波 → 后期2秒/波
  *批量生成量 : 初始1-2只 → 后期4-6只
  *同屏上限 : 初始20只 → 后期100只
- 事件触发系统
  *时间驱动触发 : 基于时序表在特定时间点触发预设事件
  *独立生成逻辑 : 特殊事件使用独立生成逻辑，不受普通刷新规则限制
  *资源管理 : 事件期间可暂停或降低普通刷新频率，避免性能问题

**特殊事件设计**
蝙蝠群突袭
- 战斗节奏变化点 (Combat Pacing Variation)+创造高强度瞬时压力 (Intensity Spike)
- 实现方式 : 屏幕边缘随机方向生成3-6组蝙蝠，形成直线穿越轨迹
- 技术参数 :
  * 移动速度 : 基础值+200%
  * 生命值 : 低，确保可一击必杀
  * 行为模式 : 出画后自动销毁，不计入怪物上限

花墙环形包围
- 创造空间约束 (Space Constraint)强制玩家在受限区域应对威胁
- 实现方式 : 以玩家为中心，半径200px圆周生成环形敌人阵列
- 技术参数 :
  * 移动模式 : 低速向中心收缩
  * 生命值 : 中等，可被摧毁形成突破口
  * 持续时间 : 30秒，之后自动销毁

海量波次
- 设计意图 : 提供武器系统压力测试 (Weapon System Stress Test)，创造高密度战斗场景
- 实现方式 : 短时间内生成普通刷新3-5倍数量的敌人
- 技术参数 :
  * 组成 : 多种类型敌人混合生成
  * 生成位置 : 更接近玩家，减少反应时间
  * 持续时间 : 30秒，后恢复正常刷新频率

**时间序列表**
|    时间    |   刷新怪物    |   特殊事件    |
|------------|--------------|--------------|
| 0'         | 000a,000b    | 初始状态 (Initial State) |
| 1'         | 001          | 精英怪物出现 (Elite Spawn) |
| 2'         | 002          | 蝙蝠群突袭(3组) (Bat Swarm x3) |
| 3'         | 003          | 精英怪物出现 (Elite Spawn) |
| 4'         | 004          | 蝙蝠群突袭(3组) (Bat Swarm x3) |
| 5'         | 002,003,004  | 海量怪物涌现 (Enemy Surge) |
| 6'         | 006          | 花墙环形包围(24只) (Floral Wall x24)；精英怪物 |
| 7'         | 007          | 蝙蝠群突袭(5组) (Bat Swarm x5) |
| 8'         | 008          | 特殊道具刷新 (Special Item Spawn) |
| 9'         | 009          | 蝙蝠群突袭(5组)；喷火道具 (Fire Item Drop) |
| 10'        | 007,008,009  | 海量怪物涌现 (Enemy Surge) |
| 11'        | 011          | 花墙环形包围(15只) (Floral Wall x15) |
| 12'        | 012          | 幽灵群突袭(6组) (Ghost Swarm x6) |
| 13'        | 013          | 金色宝箱刷新 (Golden Chest Spawn) |
| 14'        | 011,012,013  | 幽灵群突袭(6组) (Ghost Swarm x6) |
| 15'        | 015          | 清空所有小怪 (Clear All Minions) |

### 怪物模型

**怪物基础属性表 (Enemy Base Attribute Table)**

<!--怪物编号基于首次出现时间（分钟），便于编程对照，序号无强制连续考量-->
```
ID   | HP            | ATK    | SPD    | 备注 (Notes)
-----|---------------|--------|--------|------------------
000a | 3~6 (random)  | 5      | 140    | 蝙蝠变种A (Bat Variant A)
000b | 3~6 (random)  | 5      | 140    | 蝙蝠变种B (Bat Variant B)
001  | 10            | 10     | 100    |
002  | 15            | 10     | 100    |
003  | 10            | 5      | 200    | 高速型 (High-speed Type)
004  | 70~150 (rand) | 10     | 100    | 坦克型 (Tank Type)
006  | 30            | 1      | 20     | 环形阻挡 (Circular Blocker)
007  | 270           | 10     | 140    |
008  | 180           | 14     | 130    |
009  | 500           | 20     | 80     |
011  | 500           | 20     | 80     |
012  | 500           | 20     | 80     |
013  | 500           | 20     | 140    |
015  | 655350        | 65535  | TBD    | 最终Boss (Final Boss)
```

- 怪物伤害逻辑
 - 接触伤害 ： 怪物只需要接触玩家就造成伤害
 - 固定伤害值 ： 每个怪物有固定的Power值作为基础伤害
 - 简单计算 : Math.max(1, 敌人伤害 - 玩家防御)
 - 扣除血量 : 更新currentHealth
 - 死亡检测 : 血量<=0时处理死亡

**怪物类型权重**

<!-- 目前只实现精英敌人类与普通敌人类 -->
| 时间段 | 小型敌人 | 中型敌人 | 大型敌人 | 精英敌人 |
|-------|---------|---------|---------|---------|
| 0-3分钟 | 90% | 10% | 0% | 0% |
| 3-6分钟 | 70% | 25% | 5% | 5% |
| 6-9分钟 | 50% | 30% | 15% | 10% |
| 9-12分钟 | 30% | 40% | 20% | 15% |
| 12-15分钟 | 10% | 20% | 50% | 20% |

**精英怪物**
- 视觉标识 : 在基础模型外添加特定效果，不创建新模型
- 属性增强 : HP为普通版本2-3倍，ATK与SPD保持不变
- 奖励机制 : 稀有掉落（详见掉落设计细则）
- 设计意图 : 作为局内微型挑战与奖励节点，增强游戏节奏变化



## 物品掉落系统

### 核心设计

- **多层嵌套的掉落系统设计**
 - 精确控制资源获取x
 - 创造多层次惊喜
 - 平衡游戏体验
 - 适应不同玩家类型
+ **保底系统的心理学原理**:
  + 减轻"运气不好"的挫折感
  + 创造"命运修正"的感觉，玩家相信系统是公平的
  - 产生"就快要掉了"的期待感，鼓励继续游戏
- **幸运加成计算**:
  - 公式: `实际掉落率 = 基础掉落率 × (1 + 幸运加成)`
  - 例如: 30% × (1 + 50%幸运) = 45%掉落率
  - 幸运值的设计使其成为"感觉良好"的属性，玩家能明显感受到提升
  - 幸运值的边际效用递减设计：从0%到10%的提升比从40%到50%的提升感受更明显

### 掉落系统多层嵌套逻辑
```mermaid
flowchart TD
    %% 第一层：掉落触发
    A[敌人死亡] --> B{是否掉落物品?}
    B -->|是 30%基础概率| C[进入物品类型决定]
    B -->|否 70%概率| Z[无掉落]

    %% 保底机制
    Z --> Z1[连续未掉落计数+1]
    Z1 --> Z2{达到保底阈值?}
    Z2 -->|是 连续4次| Z3[下次必定掉落]
    Z2 -->|否| Z4[提高下次掉落概率]
    Z4 --> A
    Z3 --> A

    %% 第二层：物品类型决定
    C --> D{物品类型权重抽取}

    %% 普通敌人物品大类
    D -->|权重102| E[经验精魄]
    D -->|权重17| F[货币道具]
    D -->|权重9.5| G[消耗道具]
    D -->|权重0.5| H[宝箱]

    %% 第三层：具体物品决定
    %% 经验精魄细分
    E --> E1{经验精魄类型}
    E1 -->|权重90| E11[绿色精魄\n+2经验]
    E1 -->|权重10| E12[蓝色精魄\n+10经验]
    E1 -->|权重2| E13[紫色精魄\n+50经验]

    %% 货币道具细分
    F --> F1{货币类型}
    F1 -->|权重10| F11[银币\n+1银币]
    F1 -->|权重5| F12[金币\n+10银币]
    F1 -->|权重2| F13[钻石\n+100银币]

    %% 消耗道具细分
    G --> G1{消耗道具类型}
    G1 -->|权重3| G11[烤鸡\n恢复30点生命值]
    G1 -->|权重0.5| G12[三叶草\n幸运+10%]
    G1 -->|权重1| G13[念珠\n清除视野内敌人]
    G1 -->|权重2| G14[真空\n吸取所有经验精魄]
    G1 -->|权重2| G15[怀表\n冻结敌人10秒]
    G1 -->|权重1| G16[辣酱烘蛋\n喷火10秒]

    %% 宝箱细分
    H --> H1{宝箱类型}
    H1 -->|权重0.5| H11[宝箱]

    %% 宝箱内容决定
    H11 --> I1[决定物品数量\n1-5个]

    %% 宝箱内容抽取
    I1 --> J1[为每个位置\n单独抽取内容]

    %% 宝箱内容池
    J1 --> K1[宝箱内容\n必定包含金币类物品\n必定包含至少1个升级道具]

    %% 精英敌人特殊处理
    L[精英敌人死亡] --> M[精英敌人掉落]
    M --> M1[100%掉落宝箱]

    %% Boss敌人特殊处理
    R[Boss敌人死亡] --> S[Boss掉落]
    S --> S1[掉落大量金币/钻石]
    S --> S2[掉落特殊宠物道具]
    S --> S3[解锁永久系统升级]

    %% 幸运值影响
    W[玩家幸运值] --> W1[提高基础掉落率]
    W --> W2[提高稀有物品权重]
    W --> W3[提高宝箱出现率]

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style H1 fill:#bbf,stroke:#333,stroke-width:2px
    style L fill:#f9f,stroke:#333,stroke-width:2px
    style R fill:#f9f,stroke:#333,stroke-width:2px
    style W fill:#bfb,stroke:#333,stroke-width:2px
```

### 掉落系统多层嵌套逻辑说明

1. **第一层：掉落触发决定**
   - 基础掉落率：30%（受幸运值影响）
   - 保底机制：连续未掉落增加概率，连续4次后必定掉落
   - 输出：决定是否掉落任何物品

2. **第二层：物品类型决定**
   - 统一物品池：所有可能物品共用一个权重系统
   - 普通敌人权重分配：经验精魄(权重102)、货币道具(权重17)、消耗道具(权重9.5)、宝箱(权重0.5)
   - 总权重：129
   - 精英敌人：100%掉落宝箱
   - 输出：决定掉落哪种类型的物品

3. **第三层：具体物品决定**
   - 每种物品类型内部有自己的权重分配
   - 经验精魄：绿色(权重90)、蓝色(权重10)、紫色(权重2)
   - 货币道具：银币(权重10)、金币(权重5)、钻石(权重2)
   - 消耗道具：烤鸡(权重3)、三叶草(权重0.5)、念珠(权重1)、真空(权重2)、怀表(权重2)、辣酱烘蛋(权重1)
   - 输出：决定具体掉落哪个物品

4. **第四层：宝箱内容决定**（仅适用于宝箱）
   - 决定物品数量：1-5个
   - 宝箱内容特性：
     - 必定包含金币类物品
     - 必定包含至少1个升级道具，最多可有5个
     - 升级道具与升级选项相同，不包含消耗品
   - 输出：决定宝箱开启后获得的物品

5. **Boss掉落特性**
   - 掉落大量金币/钻石
   - 掉落特殊宠物道具，用于解锁家园系统功能
   - 解锁永久系统升级

### 物品掉落权重表

| 物品类型 | 外观 | 效果 | 权重(普通敌人) | 权重(精英敌人) |
|---------|------|------|--------------|--------------|
*经验*
| 绿色精魄 | 小型绿色精魄 | +2经验 | 90 | 0 |
| 蓝色精魄 | 小型蓝色精魄 | +10经验 | 10 | 0 |
| 紫色精魄 | 大型紫色精魄 | +50经验 | 2 | 0 |
*货币*
| 银币 | 小型单个银币 | +1银币 | 10 | 0 |
| 金币 | 小型单个金币 | +10银币 | 5 | 0 |
| 钻石 | 大型一颗钻石 | +100银币 | 2 | 0 |
*消耗道具*（暂不开发）
| 烤鸡 | 烤鸡腿 | 恢复30点生命值 | 3 | 0 |
| 三叶草 | 绿色三叶草 | 幸运+10% | 0.5 | 0 |
| 念珠 | 佛珠串 | 消灭视野内的所有敌人 | 1 | 0 |
| 真空 | 蓝色漩涡 | 拾取所有存在的经验精魄 | 2 | 0 |
| 怀表 | 金色怀表 | 冻结视野内的所有敌人10秒 | 2 | 0 |
| 辣酱烘蛋 | 红色食物 | 朝面前喷火10秒 | 1 | 0 |
| 宝箱 | 木质宝箱 | 特殊 | 0.5 | 100 |
<!--
  说明:
  - 普通敌人总权重: 129
  - 精英敌人只掉落宝箱(100%)
  - Boss敌人必定掉落宝箱和紫色精魄
-->

**权重系统计算**
- 物品掉落概率 = 物品权重 ÷ 总权重
- 幸运值影响: 每点幸运值增加稀有物品权重2%
- 计算公式: 实际权重 = 基础权重 × (1 + 幸运值 × 0.02)

### 掉落物品吸收机制

- 物品会在玩家接近时（默认100像素范围内）自动向玩家移动
- 玩家的【念力】属性影响吸收范围
- 当玩家非常接近物品时（默认30像素范围内），会自动收集
- 物品移动速度随距离减小而增加，形成加速效果

### 宝箱系统

宝箱是获取高价值物品的重要途径，只从精英敌人和Boss处获得。

**宝箱特性**:
- 宝箱在地图上有箭头指引，即使离开屏幕也能找到
- 宝箱必定包含金币/银币类物品
- 宝箱必定包含至少1个升级道具，最多可有5个
- 宝箱内道具与升级选项相同，不包含消耗品

**宝箱内容**:
- **货币**: 大量银币、金币或钻石(100%)
- **道具**: 1-5个随机升级道具(100%)
  - 武器升级道具
  - 被动属性道具
  - 与升级选项相同的道具池

### 物体层级管理
带补充



## 武器系统

### 概述

**第一阶段实现**：
- 武器之间相互独立，无特殊协同效果
- 每种武器有自己的攻击逻辑、冷却时间和伤害计算

**武器独立性保证**：
1. 每种武器有独立的实例和状态
2. 武器更新和攻击判定相互不影响
3. 伤害计算各自独立，不共享伤害冷却



### 武器伤害计算

- **基础公式**: `最终伤害 = 基础伤害 × (1 + 攻击力加成) × 暴击系数 × 敌人易伤系数`
  - 攻击力加成: 来自被动道具和升级
  - 暴击系数: 默认1.0，暴击时2.0
  - 敌人易伤系数: 默认1.0，特定敌人可能有抗性或弱点

- **伤害随机化**:
  - 实际伤害 = 计算伤害 × (0.9 + 随机值 × 0.2)
  - 随机值范围: [0, 1)

### 武器与被动道具权重计算

- 武器总权重 = 所有武器稀有度之和 = 100(A：光波) + 100(B) + 80(C) + 70(D) + 80(E) + 60(F) = 490
- 被动道具总权重 = 所有被动道具稀有度之和 = 100(力量) + 100(防御) + 80(生命上限) + 70(恢复) + ... = 400 (假设16种被动道具平均稀有度为80，则总和约为400)
- 武器选项概率 = 490/(490+400) ≈ 55%
- 被动道具概率 = 400/(490+400) ≈ 45%

武器和被动道具的出现概率直接基于数量和稀有度

### 3.4 武器冷却与触发

- **冷却计算**: `实际冷却 = 基础冷却 × (1 - 冷却减少)`
  - 冷却减少上限: 65%
- **触发机制**: 基于内部计时器，达到冷却时间自动触发
- **优先级系统**: 不同武器有触发优先级，确保屏幕上武器效果不过载

### 3.3 武器升级效果

- **通用升级模式**:
  - 1级: 基础效果
  - 2级: 伤害+20%
  - 3级: 范围/数量+1
  - 4级: 伤害+30%
  - 5级: 特殊效果增强
  - 6级: 伤害+40%
  - 7级: 范围/数量+1
  - 8级: 伤害+50%

## 基础武器

<!-- 暂定只开发以下几种武器，后续逐步增加，暂无增加计划 -->
| 武器名称 | 描述 |  稀有度 | 解锁方法 |
|---------|------|--------|---------|
| A：光波  | 水平攻击，可穿透敌人 | 100 | 默认解锁 |
| B：火球  | 攻击最近的敌人 | 100 | 默认解锁 |
| C：炸弹  | 产生破坏性区域 | 80 | 击杀50个怪物 |
| D：漩涡 | 周身形成圆形保护区域 | 70 | 找到5个烤鸡 |
| E：旋风 |  | 100 | 默认解锁 |

### 武器基础属性

| 属性 | 说明 | 计算方式 |
|------|------|---------|
| 攻击伤害 | 单次攻击伤害 | 固定值+随机波动(±10%) |
| 攻击间隔 | 攻击触发频率 | 秒/次，受冷却减少影响 |
| 攻击范围 | 武器影响区域 | 像素单位，受范围加成影响 |
<!-- 以下暂不开发 -->
| 穿透次数 | 可穿透敌人数 | 固定值，特定武器可无限 |
| 投射物数 | 单次攻击数量 | 固定值+额外投射物加成 |
| 持续时间 | 效果持续时长 | 秒，受持续时间加成影响 |

### A:光波

基本信息
| 武器图标 | 武器名称 | 解锁方法 | 稀有度 |
|---------|---------|---------|-------|
| [图标] | 光波 | 默认解锁 | 100 |

| 武器描述 | 进化所需 | 进化后武器 |
|---------|---------|-------|
| 水平攻击，可穿透敌人。 | [生命上限] | [进化图标] |

初始数据
| 属性 | 数值 |
|------|------|
| 伤害 | 10~40 |
| 攻击范围 | 1~1.2 |
| 射弹速度 | 1 |
| 射弹数量 | 1~2 |
| 持续时间 | - |
| 冷却时间 | 1.35s |
| 机会 | 20% |
| 攻击间隔 | 0.1s |
| 穿透 | 全穿透 |
| 伤害间隔 | - |
| 屏幕限制 | 15 |
| 暴击系数 | 2× |
| 击退 | 1 |
| 穿墙 | ✓ |

升级效果
| 等级 | 效果 |
|------|------|
| 1级 | - |
| 2级 | 伤害+5、范围+10% |
| 3级 | 伤害+5 |
| 4级 | 伤害+5、范围+10% |
| 5级 | 伤害+5 |
| 6级 | 伤害+5、范围+10% |
| 7级 | 伤害+5 |
| 8级 | 伤害+5 |

进阶武器：
| 进阶武器 | 伤害 | 攻击范围 | 射弹速度 | 射弹数量 |
|---------|------|---------|---------|---------|
| [进化图标] | 40 | 1.3 | 1 | 2× |

| 穿透 | 持续时间 | 冷却时间 | 攻击间隔 |
|------|---------|---------|---------|
| 全穿透 | - | 1.35s | 0.1s |

| 击退 | 穿墙 | 伤害间隔 | 屏幕限制 | 机会 | 暴击系数 |
|------|------|---------|---------|------|---------|
| 1 | ✓ | - | 15 | 10% | 2× |

小贴士
· 范围穿透伤害，前期清兵利器。
· 光波是可以穿墙的，所以可以利用墙体进行输出。



### B：火焰

基本信息
| 武器图标 | 武器名称 | 解锁方法 | 稀有度 |
|---------|---------|---------|-------|
| [图标] | B | 默认解锁 | 100 |

| 武器描述 | 进化所需 | 进化后 |
|---------|---------|-------|
| [攻击最近的敌人] | [图标] | [进化图标] |

初始数据
| 伤害 | 攻击范围 |
|------|---------|
| 10~30 | 1 |
| 射弹速度 | 射弹数量 |
|---------|---------|
| 1 | 1~4 |
| 持续时间 | 冷却时间 |
|---------|---------|
| 1.0s | 1.2s~1.0s |
| 机会 | 攻击间隔 |
|------|---------|
| - | 0.1s |
| 穿透 | 伤害间隔 |
|------|---------|
| 1~2 | - |
| 屏幕限制 | 暴击系数 |
|---------|---------|
| 60 | - |
| 击退 | 穿墙 |
|------|------|
| 1 | ✗ |

升级效果
| 等级 | 效果 |
|------|------|
| 1级 | - |
| 2级 | 射弹+1 |
| 3级 | 冷却-0.2 |
| 4级 | 射弹+1 |
| 5级 | 伤害+10 |
| 6级 | 射弹+1 |
| 7级 | 穿透+1 |
| 8级 | 伤害+10 |

进阶武器
| 进阶武器 | 伤害 | 攻击范围 | 射弹速度 | 射弹数量 |
|---------|------|---------|---------|---------|
| [进化图标] | 30 | 1 | 2 | 4 |
| 穿透 | 持续时间 | 冷却时间 | 攻击间隔 |
|------|---------|---------|---------|
| 2 | - | 0.5s | 0.1s |
| 击退 | 穿墙 | 伤害间隔 | 屏幕限制 | 机会 | 暴击系数 |
|------|------|---------|---------|------|---------|
| 1 | ✓ | - | 60 | - | - |

小贴士
· 每发子弹都是单独判定的。
· 虽然是在攻击最近的敌人，但很多情况下并不是攻击你想要攻击的敌人。
· 不要将魔杖作为主武器使用，这把武器的输出并不稳定。



### C：炸弹

**概述**
+ 持久性伤害区域：
  - 在地面上创建固定位置的圆形区域
  - 区域持续存在一段时间（通常3-5秒）
  - 敌人进入区域时持续受到伤害（有伤害间隔）
  - 区域内敌人减速
+ 升级路径设计：
  - 初始只能创建1个区域
  - 升级增加区域数量（最多可达4个）

```mermaid
flowchart LR
  A[武器C.attack方法调用] --> B{根据projectileCount循环}
  B --> C[确定放置位置]
  C --> D[创建持续伤害区域]
  D --> E[设置区域视觉效果]
  D --> F[添加区域碰撞检测]
  F --> G[计时器检测区域内敌人]
  G --> H[对区域内敌人造成伤害]
  D --> I[设置区域生命周期]
  I --> J[区域消失]
```

**技术实现关键点**
*区域创建方式*
  - 使用现有的物理碰撞系统和`physics.add.overlap`
  - 创建固定位置的圆形精灵并设置`setCircle`碰撞
  - 区域位置可采用"围绕玩家的随机位置"或"以玩家为中心的固定角度分布"
*持续伤害机制*
  - 使用`scene.time.addEvent`创建定时器
  - 在回调中检测区域内敌人并造成伤害
  - 设置伤害间隔（如每0.5秒造成一次伤害）
*区域生命周期*
  - 使用`scene.time.delayedCall`设置区域持续时间
  - 到期后通过`alpha`渐变消失并销毁
*升级思路*
  - 通过增加`projectileCount`增加区域数量
  - 增加区域半径、持续时间和伤害值

**详细参数**
| 武器图标 | 武器名称 | 解锁方法 | 稀有度 |
|---------|---------|---------|-------|
| [图标] | c | 默认解锁 | 100 |
| 武器描述 | 进化所需 | 进化后武器 |
|---------|---------|-------|
| [产生破坏性区域] | [图标] | [进化图标] |

初始数据
| 属性 | 数值 |
|------|------|
| 伤害 | 10~40 |
| 攻击范围 | 1~1.8 |
| 射弹速度 | 1 |
| 射弹数量 | 1~4 |
| 持续时间 | 2.0s~3.0s |
| 冷却时间 | 4.5s |
| 机会 | - |
| 攻击间隔 | 0.3s |
| 穿透 | 全穿透 |
| 伤害间隔 | 0.5s |
| 屏幕限制 | 20 |
| 暴击系数 | - |
| 击退 | 0 |
| 穿墙 | ✓ |

升级效果
| 等级 | 效果 |
|------|------|
| 1级 | - |
| 2级 | 射弹+1、范围+20% |
| 3级 | 伤害+10、持续时间+0.5s |
| 4级 | 射弹+1、范围+20% |
| 5级 | 伤害+10、持续时间+0.3s |
| 6级 | 射弹+1、范围+20% |
| 7级 | 伤害+5、持续时间+0.3s |
| 8级 | 伤害+5、持续时间+0.3s |

进阶武器
| 进阶武器 | 伤害 | 攻击范围 | 射弹速度 | 射弹数量 |
|---------|------|---------|---------|---------|
| [进化图标] | 40 | 2 | 1 | 4 |
| 穿透 | 持续时间 | 冷却时间 | 攻击间隔 |
|------|---------|---------|---------|
| 全穿透 | 4.0s | 4.0s | 0.3s |
| 击退 | 穿墙 | 伤害间隔 | 屏幕限制 | 机会 | 暴击系数 |
|------|------|---------|---------|------|---------|
| 0 | ✓ | 0.5s | 30 | - | - |

小贴士
| 小贴士 |
|--------|
| 提升持续时间和冷却时间，让地图上补满圣水，再配合国王圣经+大蒜，就能让敌人完全无法靠近。 |
| 前期的圣水不确定性很高，基本上只能"我命由天不由我"，一定要找一个能很好度过前期的武器。 |
| ·攻击范围可以影响炸点的大小，所以多找一些能提升攻击范围的被动物品。 |
| ·随机性较大，前期不建议使用。 |



**D：漩涡**
基本信息
| 武器图标 | 武器名称 | 解锁方法 | 稀有度 |
|---------|---------|---------|-------|
| [图标] | 漩涡 | 找到5个烤鸡 | 70 |
| 武器描述 | 进化所需 | 进化后 |
|---------|---------|-------|
| [周身形成圆形保护区域] | [图标] | [图标] |

初始数据
| 初始数据 | |
|---------|--|
| 伤害 | 攻击范围 |
| 5~15 | 1~2 |
| 射弹速度 | 射弹数量 |
| 1 | 1 |
| 持续时间 | 冷却时间 |
| - | 1.3s~1.0s |
| 机会 | 攻击间隔 |
| - | - |
| 穿透 | 伤害间隔 |
| 全穿透 | 0 |
| 屏幕限制 | 暴击系数 |
| 50 | - |
| 击退 | 穿墙 |
| 0 | ✓ |

升级效果
| 升级效果 | |
|---------|--|
| 等级 | 效果 |
| 1级 | - |
| 2级 | 范围+40%、伤害+2 |
| 3级 | 冷却-0.1s、伤害+1 |
| 4级 | 范围+20%、伤害+1 |
| 5级 | 冷却-0.1s、伤害+2 |
| 6级 | 范围+20%、伤害+1 |
| 7级 | 冷却-0.1s、伤害+1 |
| 8级 | 范围+20%、伤害+1 |

进阶武器
| 进阶武器 | 伤害 | 攻击范围 | 射弹速度 | 射弹数量 |
|---------|------|---------|---------|---------|
| [金色球体图标] | 20 | 3 | 1 | 1 |
| 穿透 | 持续时间 | 冷却时间 | 攻击间隔 |
|------|---------|---------|---------|
| 全穿透 | - | 1.0s | 0s |
| 击退 | 穿墙 | 伤害间隔 | 屏幕限制 | 机会 | 暴击系数 |
|------|------|---------|---------|------|---------|
| 0 | ✓ | - | 50 | - | - |

小贴士
| 小贴士 |
|--------|
| 冷却时间就是对敌人造成伤害的间隔，如：敌人进圈内受到伤害，那么在冷却时间结束前，敌人都不会受到伤害。 |
| 大蒜作为前期神器，可以轻松秒杀小怪，无论是蝙蝠还是骷髅，都非常好用。 |
| 大蒜可以搭配时钟柳叶刀或者国王圣经，让自己完全处于安全状态。 |



### 详细设计

**进化触发条件**：
1. 武器达到最高级(8级)
2. 玩家拥有对应的被动道具
3. 满足上述条件后，击杀怪物有特殊掉落机制

**宝箱掉落机制**：
| 条件 | 掉落概率 | 宝箱内容 |
|------|---------|---------|
| 武器8级+对应被动道具 | 20只怪物内必定掉落 | 100%提供进化道具 |

**武器进化对照表**：
| 基础武器 | 所需被动道具 | 进化武器 | 进化效果 |
|---------|------------|---------|---------|
| 鞭子(8级) | 心形道具 | 红色鞭子 | 伤害+50%，范围+30%，穿透+全穿透 |
| 魔杖(8级) | 书 | 进化魔杖 | 射弹速度+100%，冷却-50%，穿透+1 |
| 国王圣经(8级) | 骷髅 | 红色书 | 范围+75%，射弹+1，击退+3 |
| 大蒜(8级) | 心形道具 | 金色球体 | 范围+50%，伤害+33%，冷却-0.3s |
| 闪电戒指(8级) | 齿轮 | 进化闪电戒指 | 范围保持不变，伤害特效增强 |
| 五芒星(8级) | 火焰 | 五彩五芒星 | 触发概率100%，冷却-30s |

**进化武器特性**：
1. 进化武器不再有升级选项
2. 进化武器受通用属性加成影响（如力量+30%对进化武器同样有效）
3. 进化武器有特殊视觉效果，明显区别于基础武器

- **武器进化条件**:
  - 武器达到8级
  - 拥有对应被动道具
  - 两者同时满足时自动进化

**选项生成流程**：
1. 玩家升级时，系统生成3个不重复选项
2. 首先确定每个选项的类型（武器/被动道具）
   - 武器选项概率：基于武器总权重/(武器总权重+被动道具总权重)
   - 被动道具概率：基于被动道具总权重/(武器总权重+被动道具总权重)
3. 确定类型后，从对应池中按稀有度权重随机选择具体项目



**选项池分类**：
| 选项类型 | 包含内容 | 优先级 |
|---------|---------|-------|
| A类：未获得武器 | 已解锁但未获得的武器 | 最高 |
| B类：武器升级 | 已获得且未满级的武器 | 高 |
| C类：被动道具(新) | 已解锁但未获得的被动道具 | 中 |
| D类：被动道具(升级) | 已获得且未满级的被动道具 | 低 |

**选项生成规则**：
1. 优先从A类选项中选择（确保玩家有机会获得所有武器）
2. 如果A类选项不足，从B类补充
3. 如果武器选项总数不足应出现的数量，从C类和D类补充
4. 已拥有的武器/道具的稀有度提升20%（增加再次出现概率）
5. 如果所有类别选项都不足3个，提供金币/经验值作为备选

### 2.3 升级选项生成算法

- **选项数量**: 每次升级提供3个选项
- **选项池分类**:
  - 武器池: 未获得武器 > 已有武器升级
  - 被动道具池: 未获得道具 > 已有道具升级
  - 特殊选项池: 跳过、重选、金币等

- **选项生成步骤**:
  1. 确定选项类型分布(武器/道具/特殊)
  2. 应用权重系统选择具体项目
  3. 确保不重复且符合解锁条件

- **权重调整因素**:
  - 已拥有项目权重提升(+20%)
  - 长时间未出现项目权重提升(+5%/次)
  - 玩家选择历史影响(常选项目权重降低)
























### 敌人生成算法

- **生成区域**: 玩家周围一定范围外，屏幕外
  - 最小距离: 屏幕对角线长度 × 0.6
  - 最大距离: 屏幕对角线长度 × 1.2

- **生成频率**:
  - 基础生成间隔: 1-3秒
  - 随时间缩短: `实际间隔 = 基础间隔 × (1 - 当前分钟 × 0.02)`
  - 最小间隔: 0.3秒

- **生成数量**:
  - 基础数量: 3-5个/波
  - 随时间增加: `实际数量 = 基础数量 × (1 + 当前分钟 × 0.1)`
  - 最大数量: 30个/波

### 人物属性

**属性初始参数表**
| 图标 | 属性名称 | 属性效果                                   | 初始值 |
|------|---------|------------------------------------------|--------|
|      | 生命上限 | 影响角色的最大生命值。                     | 100   |
|      | 生命恢复 | 影响每秒为角色恢复少生命值。                | 0      |
|      | 防御     | 影响每次受到减少的伤害                     | 0      |

|      | 攻击力   | 影响所有攻击的伤害                         | 0      |
|      | 移动速度 | 影响角色的移动速度                         | 100%   |
|      | 攻击范围 | 影响所有攻击的范围                         | 100%   |
|      | 攻击速度 | 影响所有射弹的速度                         | 100%   |
<!-- 以下暂不开发 -->
|      | 持续时间 | 影响武器效果的持续时间                     | 100%   |
|      | 发射数量 | 影响武器拥有的额外弹射数量                 | 0%     |
|      | 冷却时间 | 影响攻击之间的冷却时间                     | 100%   |
|      | 运气     | 影响某些东西的概率（掉落几率和宝箱质量）    | 100%   |
|      | 成长     | 影响收集经验宝石获得的经验量。             | 100%   |
|      | 贪欲     | 影响从拾取物和宝箱中获得的金币数量。        | 100%   |
|      | 诅咒     | 影响敌人的速度、生命值、数量和出现次数。    | 0%     |
|      | 念力     | 影响物品拾取范围。                        | 30     |
|      | 复活机会 | 影响玩家复活次数。                        | 0      |
|      | 重新选择 | 影响在升级时获得不同选择的次数             | 0      |
|      | 跳过     | 影响在升级时跳过选择的次数                 | 0      |

### 被动物品资料


**被动物品表格**
<!--被动物品是为玩家提供加成效果。，补充玩家的基本属性。-->

| 图标 | 名称 | 权重 | 描述 | 解锁方法 |
|------|------|-------|------|---------|

| 🛡️ | 防御 | - | 收到攻击减少20%  | 默认解锁 |
| 💙 | 生命上限 | - | 最大生命值+20% | 默认解锁 |
| 🍅 | 生命恢复 | - | 恢复+0.2~1 | 默认解锁 |

| 🥊 | 攻击力 | - | 力量+10%~50% | 默认解锁 |

| 👟 | 移动速度 | - | 移动速度+10%~50% | 默认解锁 |
| 🔵 | 拾取范围 | - | 扩大拾取范围50%~300% | 默认解锁 |
| 🔄 | 攻击范围 | - | 攻击范围+10%~50% | 默认解锁 |
| 🏹 | 攻击速度 | - | 速度+10%~50% | 默认解锁 |

【以下暂不开发】
~~| ⏳ | 持续时间 | 100 | 持续时间+10%~50% | 将符文追踪者升到4级 |~~
~~| ​​⚔️ | 攻击数量 | 50 | 射弹+1~2 | 将魔杖升到7级 |~~
~~| 🕯️ | 冷却时间 | 50 | 冷却时间-8%~40% | 同时拥有6种不同的武器 |~~
~~| 🍀 | 运气 | 100 | 幸运+10%~50% | 捡起一株三叶草 |~~
~~| 👑 | 成长速度 | 80 | 经验+8%~40% | 任意角色达到10级 |~~
~~| 👻 | 诅咒 | 40 | 敌人的速度、生命值、数量和出现次数+10%~50% | 使用拉玛存活15分钟 |~~
~~| 🧿 | 复活机会 | 40 | 复活次数+1~2 | 使用克罗奇存活20分钟 |~~
~~| 🎭️ | 贪欲 | 80 | 贪婪+10%~50% | 在镇魂图书馆拾取解锁 |~~


~~Tips：（暂不开发tips部分）地图上都有上述道具可以拾取（获得地图后可以看到位置）。如果你当时没有这个能力，那么可以马上获得此能力；如果你有这个能力，那么就会升级此能力；如果你有这个能力且已经满级，那么将无法获得此能力；如果你没有这个能力且你的被动格子已经满了，那么你可以额外获得此能力，并且还能升级此能力。~~





### 掉落物科普
局内击败敌人、摧毁光源都可以获得掉落物。（光源：地图中可破坏的物品）



**辣酱烘蛋武器属性**
| 辣酱烘蛋 | 伤害 | 攻击范围 | 射弹速度 | 射弹数量 |
|---------|------|---------|---------|---------|
| [辣酱烘蛋图标] | 30 | 2 | 3 | 12 |
| 穿透 | 持续时间 | 冷却时间 | 攻击间隔 |
|------|---------|---------|---------|
| 全穿透 | 1.0s | 0.5s | 0.04s |
| 击退 | 穿墙 | 伤害间隔 | 屏幕限制 | 机会 | 暴击系数 |
|------|------|---------|---------|------|---------|
| 1 | ✓ | - | 50 | - | - |

Tips：三叶草不占用被动格子还能加幸运，但确实最稀有的掉落物。

**辣酱烘蛋武器属性**:
- 伤害: 30
- 攻击范围: 2
- 射弹速度: 3
- 射弹数量: 12
- 持续时间: 1.0秒
- 冷却时间: 0.5秒
- 攻击间隔: 0.04秒
- 穿透: 全穿透
- 击退: 1
- 穿墙: ✓
- 屏幕限制: 50










## 4. 稀有度影响计算

### 详细设计

**稀有度权重表**：
| 稀有度值 | 相对权重 | 百分比概率(单独) | 实际概率(在池中) |
|---------|---------|----------------|----------------|
| 100 | 100 | 3.672% | 高 |
| 90 | 90 | 3.305% | 中高 |
| 80 | 80 | 2.938% | 中高 |
| 70 | 70 | 2.571% | 中 |
| 60 | 60 | 2.203% | 中 |
| 50 | 50 | 1.836% | 中低 |
| 40 | 40 | 1.469% | 低 |
| 30 | 30 | 1.102% | 很低 |
| 20 | 20 | 0.734% | 极低 |
| 10 | 10 | 0.367% | 稀有 |
| 1 | 1 | 0.037% | 传说 |

**选择算法**：
1. 计算所有可选项的稀有度总和
2. 生成0到总和之间的随机数
3. 遍历可选项，累加稀有度值，当累加值超过随机数时选中当前项

**已拥有项目的权重提升**：
- 已拥有但未满级的武器/道具稀有度提升20%
- 例如：稀有度80的已拥有武器，在升级选项中的实际稀有度为96

**选择算法伪代码**：
```javascript
function selectRandomByWeight(items, count) {
    const selected = [];
    let remainingItems = [...items];

    for (let i = 0; i < count && remainingItems.length > 0; i++) {
        // 计算总权重
        let totalWeight = 0;
        remainingItems.forEach(item => {
            // 已拥有项目权重提升20%
            const weight = item.owned ? item.rarity * 1.2 : item.rarity;
            totalWeight += weight;
        });

        // 生成随机数
        const random = Math.random() * totalWeight;

        // 选择项目
        let cumulativeWeight = 0;
        for (let j = 0; j < remainingItems.length; j++) {
            const item = remainingItems[j];
            // 已拥有项目权重提升20%
            const weight = item.owned ? item.rarity * 1.2 : item.rarity;
            cumulativeWeight += weight;

            if (cumulativeWeight >= random) {
                selected.push(item);
                remainingItems.splice(j, 1); // 移除已选项目
                break;
            }
        }
    }

    return selected;
}
```

## 5. 武器解锁持久化

### 详细设计

**解锁状态存储**：
```javascript
// 存档数据结构
const saveData = {
    // 已解锁武器列表
    unlockedWeapons: ["whip", "magicWand", "kingBible", ...],

    // 已解锁被动道具列表
    unlockedPassives: ["strength", "defense", "maxHealth", ...],

    // 游戏统计数据（用于解锁条件）
    stats: {
        totalKills: 5000,
        longestSurvivalTime: 1200, // 秒
        maxLevel: 25,
        // 其他统计...
    },

    // 永久升级数据
    permanentUpgrades: {
        strength: 2,
        defense: 1,
        // 其他永久升级...
    }
};
```

**解锁条件检查**：
```javascript
function checkUnlockConditions(player, saveData) {
    // 检查所有武器的解锁条件
    WEAPONS.forEach(weapon => {
        if (!saveData.unlockedWeapons.includes(weapon.id) && checkWeaponUnlockCondition(weapon, saveData)) {
            // 解锁武器
            saveData.unlockedWeapons.push(weapon.id);
            showUnlockNotification(weapon.name);
        }
    });

    // 检查所有被动道具的解锁条件
    PASSIVE_ITEMS.forEach(item => {
        if (!saveData.unlockedPassives.includes(item.id) && checkPassiveUnlockCondition(item, saveData)) {
            // 解锁被动道具
            saveData.unlockedPassives.push(item.id);
            showUnlockNotification(item.name);
        }
    });

    // 保存更新后的数据
    saveGameData(saveData);
}
```

**游戏开始时的武器初始化**：
```javascript
function initializePlayerWeapons(player, saveData) {
    // 默认装备鞭子
    player.addWeapon("whip");

    // 如果有其他默认武器，也可以在这里添加
    // player.addWeapon("otherDefaultWeapon");
}
```





**经验值获取方式**：
- 主要通过击杀敌人掉落的经验精魄获取
- 经验精魄受【成长】属性加成
- 特殊事件（如宝箱）也可能提供经验值

#### 经验精魄设计

**经验精魄类型**：

| 精魄类型 | 外观 | 基础经验值 | 掉落概率 | 特殊效果 |
|---------|------|-----------|---------|---------|
| 绿色精魄 | 小型绿色精魄 | 2 | 80% | 基础经验精魄 |
| 蓝色精魄 | 中型蓝色精魄 | 10 | 20% | 提供更多经验 |
| 紫色精魄 | 大型紫色精魄 | 50 | 特殊掉落 | 稀有精魄，通常由精英怪或宝箱掉落 |

**精魄掉落机制**：
- 普通敌人死亡时，有80%概率掉落绿色精魄，20%概率掉落蓝色精魄
- 精英敌人必定掉落蓝色精魄，有10%概率掉落紫色精魄
- Boss战斗后掉落大量精魄，包括多个紫色精魄

**精魄吸收机制**：
- 精魄会自动向玩家移动，移动速度为每秒100像素
- 玩家的【念力】属性影响精魄开始移动的距离（默认30像素）
- 当地图上精魄数量超过400个时，新生成的精魄会变为蓝色精魄，并吸收所有现有精魄

**经验值加成**：
- 【成长】属性直接影响获得的经验值
- 成长+10%时，绿色精魄提供2.2经验，蓝色精魄提供11经验
- 成长加成公式：实际经验 = 基础经验 × (1 + 成长百分比)
















## 4 技术实现指南



#### 1.2 保存机制 (MINI_110)
*   **实现方式**: 使用浏览器 `localStorage` API。创建一个 `SaveManager` 类。
*   **位置**:
    *   `SaveManager` 类：封装 `localStorage.setItem(key, JSON.stringify(value))` 和 `JSON.parse(localStorage.getItem(key))` 方法。
    *   `GameScene.create()`: 初始化 `SaveManager` 实例。
    *   `GameScene.endGame()`: 调用 `saveManager.saveGameData()` 保存当局统计数据 (生存时间、金币、最高等级等) 和永久数据 (总金币、解锁状态)。
    *   游戏启动时 (`PreloadScene` 或 `GameScene.create()`): 调用 `saveManager.loadGameData()` 读取永久数据。
*   **数据结构**: 使用简单的 JavaScript 对象存储，例如：
    ```javascript
    {
      totalCoins: 1500,
      unlockedWeapons: ['whip', 'magic_wand'],
      permanentUpgrades: { power: 1, moveSpeed: 0 },
      stats: { longestSurvival: 900, totalKills: 5000 }
    }
    ```

#### 1.3 跨平台适配 (MINI_101)
*   **实现方式**:
    *   **输入**: 在 `InputManager` 中检测 `this.scene.sys.game.device.os.desktop`。如果是桌面，监听键盘事件 (WASD/箭头)；否则，创建并监听虚拟摇杆事件。
    *   **UI**: (暂未涉及复杂UI) 使用 Phaser 的 `setScrollFactor(0)` 将 UI 元素固定在屏幕上。响应式布局可后续使用 Phaser 的 `ScaleManager` 或 CSS 实现。

### 2. 游戏机制实现

#### 2.1 敌人AI (MINI_102)
<!--
技术实现细节将在开发完成后补充。当前阶段专注于设计，具体实现方式可能随开发进展调整。
-->

#### 2.2 武器进化系统 (MINI_104 及以后)
*   **触发条件**: 特定武器达到最高等级 (例如 8 级) **并且** 玩家拥有对应的被动道具。
*   **实现位置**: 可能在 `Player.levelUp()` 准备升级选项时，或者在 `UpgradeUI.show()` 筛选选项时检查。
*   **逻辑**:
    1.  获取玩家当前所有武器及其等级，以及所有被动道具。
    2.  遍历武器，检查是否达到最高级。
    3.  如果达到最高级，检查玩家是否拥有其进化所需的被动道具。
    4.  如果条件都满足，则将该武器的"进化形态"作为一个可能的升级选项添加到列表中。
*   **(当前阶段不实现)**

### 3. 资源管理 (MINI_101 及以后)

#### 3.1 资源获取与预加载
*   **实现方式**: 使用 Phaser 的加载器 (`this.load`)。
*   **位置**: 集中在 `PreloadScene` (如果创建了该场景) 或 `GameScene` 的 `preload()` 方法中。
*   **示例**:
```javascript
    preload() {
        // 加载精灵图
        this.load.spritesheet('player', 'assets/player_spritesheet.png', { frameWidth: 64, frameHeight: 64 });
        this.load.spritesheet('bat', 'assets/bat_spritesheet.png', { frameWidth: 32, frameHeight: 32 });
        this.load.spritesheet('ghost', 'assets/ghost_spritesheet.png', { frameWidth: 32, frameHeight: 32 });
        // 加载武器/物品图标
        this.load.image('whip_icon', 'assets/icons/whip.png');
        // 加载音效 (按需加载或预加载)
        // this.load.audio('hit_sound', 'assets/audio/hit.wav');
    }
    ```
*   **精灵图使用**: 在创建 `GameObject` (如 `Player`, `Enemy`) 时，指定 `key` 和 `frame`。动画在各自类的 `constructor` 或 `create` 方法中使用 `this.scene.anims.create()` 定义。

### 4. 数值平衡与概率计算

#### 4.1 稀有度计算 (MINI_104 及以后)
*   **实现方式**: 基于权重的随机选择。
*   **位置**: 在生成升级选项 (`UpgradeUI.show()` 或相关逻辑) 时使用。
*   **逻辑**:
    1.  定义一个包含所有可能选项及其权重(稀有度)的列表，例如:
        ```javascript
        const availableOptions = [
          { item: 'whip', weight: 100, type: 'weapon' },
          { item: 'spinach', weight: 100, type: 'passive' },
          { item: 'empty_tome', weight: 50, type: 'passive' },
          // ... 其他武器和被动
        ];
        ```
    2.  过滤掉不符合条件的选项 (例如已满级的武器/道具)。
    3.  计算剩余选项的总权重 `totalWeight`。
    4.  生成一个 0 到 `totalWeight` 之间的随机数 `randomWeight`。
    5.  遍历剩余选项列表，依次减去每个选项的权重，直到 `randomWeight` 小于等于当前选项的权重，该选项即为选中项。
    ```javascript
    let cumulativeWeight = 0;
    for (const option of filteredOptions) {
        cumulativeWeight += option.weight;
        if (randomWeight <= cumulativeWeight) {
            selectedOption = option;
            break;
        }
    }
    ```
*   **(当前阶段不实现)**

#### 4.2 升级选择概率 (MINI_104 及以后)
*   **实现方式**: 与稀有度计算类似，只是权重池更大。
*   **逻辑**: 可以先按大类（武器/被动）进行一次加权随机，确定本次升级是提供武器还是被动，然后再在对应的小类池中进行一次加权随机选择具体的项目。或者直接将所有可升级项放入一个大池子进行加权随机。
*   **(当前阶段不实现)**

### 5. 性能优化与降级渲染 (后续优化阶段)

#### 5.1 大量敌人优化
<!--
技术实现细节将在开发完成后补充。当前阶段专注于设计，具体实现方式可能随开发进展调整。
-->

#### 5.2 低性能设备适配
*   **检测**: 可以使用 `this.sys.game.device.performance.getBenchmark()` (如果可用) 或基于 FPS 检测。
*   **降级**:
    *   在 `Weapon` 或特效相关的类中，根据性能标志减少粒子数量或简化效果。
    *   在 `EnemySpawner.adjustDifficulty` 中，根据性能标志降低 `maxEnemies` 的上限。
*   **(当前阶段不实现)**





**核心设计原则**
- （前期）静态属性模型 : 怪物属性（HP/ATK/SPD）为固定值，不随游戏时间动态缩放
- （前期）递进难度曲线 : 通过引入更高基础属性的新怪物类型及调整生成密度实现难度递增







**国王圣经（暂不开发）**

基本信息
| 武器图标 | 武器名称 | 解锁方法 | 稀有度 |
|---------|---------|---------|-------|
| [蓝色圣经图标] | 国王圣经 | 默认解锁 | 80 |
| 武器描述 | 进化所需 | 进化后 |
|---------|---------|-------|
| 围绕角色旋转。 | [骷髅图标] | [红色书图标] |

初始数据
| 初始数据 | |
|---------|--|
| 伤害 | 攻击范围 |
| 10~30 | 1~1.5 |
| 射弹速度 | 射弹数量 |
| 1~1.6 | 1~4 |
| 持续时间 | 冷却时间 |
| 3.0s~4.0s | 3.0s |
| 机会 | 攻击间隔 |
| - | 0 |
| 穿透 | 伤害间隔 |
| 全穿透 | 1.7s |
| 屏幕限制 | 暴击系数 |
| 25 | - |
| 击退 | 穿墙 |
| 1 | ✓ |

升级效果
| 升级效果 | |
|---------|--|
| 等级 | 效果 |
| 1级 | - |
| 2级 | 射弹+1 |
| 3级 | 速度+30%、范围+25% |
| 4级 | 持续时间+0.5s、伤害+10 |
| 5级 | 射弹+1 |
| 6级 | 速度+30%、范围+25% |
| 7级 | 持续时间+0.5s、伤害+10 |
| 8级 | 射弹+1 |
进阶武器
| 进阶武器 | 伤害 | 攻击范围 | 射弹速度 | 射弹数量 |
|---------|------|---------|---------|---------|
| [红色圣经图标] | 30 | 1.75 | 1.5 | 4 |
| 穿透 | 持续时间 | 冷却时间 | 攻击间隔 |
|------|---------|---------|---------|
| 全穿透 | 3.0s | 3.0s | 0 |
| 击退 | 穿墙 | 伤害间隔 | 屏幕限制 | 机会 | 暴击系数 |
|------|------|---------|---------|------|---------|
| 4 | ✓ | 1.7s | 25 | - | - |

小贴士
| 小贴士 |
|--------|
| 圣经是兼容性非常高的武器，几乎可以兼容任何属性。 |
| 射弹速度影响圣经旋转速度，攻击范围影响圆圈的大小和圣经的大小，持续时间可以增加圣经的持续时间。 |
| 圣经的冷却时间从上一个圣经消失开始计算。 |
| 使用大蒜（喷涌者）可以降低敌人击退抗性，这能让我们把所有敌人推得远远的。 |
| 虽然伤害间隔是1.7s，意味着同一个敌人在1.7s不会受到第二次伤害，但是每次圣经出现时的时间重置。 |





