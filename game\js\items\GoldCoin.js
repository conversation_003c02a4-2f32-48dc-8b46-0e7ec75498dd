/**
 * 金币类 - 比普通银币更有价值的收集品
 * 金币可以被玩家收集，增加玩家的金币数量
 */
class GoldCoin {
    /** 缩放比例 */
    static SCALE = 1.5;

    /** 碰撞箱设置 */
    static COLLISION_BOX = {
        type: 'circle',  // 圆形碰撞箱
        radius: 4,      // 半径
        offsetX: 4,      // X偏移
        offsetY: 4       // Y偏移
    };

    /**
     * 创建一个新的金币
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} value - 金币价值
     */
    constructor(scene, x, y, value = 10) {
        this.scene = scene;
        this.value = value;
        this.type = 'goldCoin';

        // 创建精灵
        this.sprite = scene.physics.add.sprite(x, y, 'coin_1');
        this.sprite.setScale(GoldCoin.SCALE);
        this.sprite.setScrollFactor(1); // 确保金币随相机移动

        // 设置物理属性
        this.sprite.body.setCircle(
            GoldCoin.COLLISION_BOX.radius,
            GoldCoin.COLLISION_BOX.offsetX,
            GoldCoin.COLLISION_BOX.offsetY
        );

        // 添加引用以便在碰撞时获取
        this.sprite.coinInstance = this;

        // 加载图片和创建动画
        this.loadAssets();
    }

    /**
     * 加载图片和创建动画
     */
    loadAssets() {
        // 检查是否已加载金币精灵表
        if (!this.scene.textures.exists('gold_coin')) {
            // 加载金币精灵表
            this.scene.load.spritesheet('gold_coin', 'assets/images/coin/MonedaD.png', {
                frameWidth: 16,  // 每帧宽度
                frameHeight: 16  // 每帧高度
            });

            // 加载完成后创建动画
            this.scene.load.once('complete', () => {
                // 创建金币动画
                if (!this.scene.anims.exists('gold_coin_spin')) {
                    this.scene.anims.create({
                        key: 'gold_coin_spin',
                        frames: this.scene.anims.generateFrameNumbers('gold_coin', { start: 0, end: 4 }),
                        frameRate: 10,
                        repeat: -1
                    });
                }

                // 设置精灵并播放动画
                if (this.sprite) {
                    this.sprite.setTexture('gold_coin');
                    this.sprite.setScale(GoldCoin.SCALE);
                    this.sprite.play('gold_coin_spin');
                }
            });

            // 开始加载
            this.scene.load.start();
        } else {
            // 精灵表已加载，直接播放动画
            if (this.sprite) {
                this.sprite.setTexture('gold_coin');
                this.sprite.setScale(GoldCoin.SCALE);
                this.sprite.play('gold_coin_spin');
            }
        }
    }

    /**
     * 收集金币
     */
    collect() {
        if (!this.sprite || !this.sprite.active) return;

        // 触发金币收集事件
        this.scene.events.emit('coinCollected', this.value);

        // 销毁金币对象
        this.destroy();
    }

    /**
     * 销毁金币
     */
    destroy() {
        if (this.sprite) {
            this.sprite.destroy();
            this.sprite = null;
        }
    }
}