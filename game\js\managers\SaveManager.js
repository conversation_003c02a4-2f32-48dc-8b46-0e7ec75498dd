/**
 * 存档管理器
 * 统一管理游戏的所有持久化数据
 */
class SaveManager {
    constructor() {
        this.saveKey = 'palmow_game_save';
        this.backupKey = 'palmow_game_save_backup';
        this.version = '1.0.0';

        // 默认游戏数据结构
        this.defaultData = {
            version: this.version,
            player: {
                totalCoins: 50000,           // 累计金币
                totalGames: 0,           // 总游戏次数
                totalSurvivalTime: 0,    // 总生存时间（秒）
                bestSurvivalTime: 0,     // 最佳生存时间（秒）
                totalKills: 0,           // 总击杀数
                maxLevel: 1,             // 达到的最高等级
                unlockedWeapons: [],     // 已解锁武器
                unlockedItems: [],       // 已解锁道具
                achievements: [],        // 已获得成就
                ownedPets: [],   // 已拥有宠物
                ownedWorkstations: []    // 已拥有工作台
            },
            settings: {
                soundEnabled: true,
                musicEnabled: true,
                volume: 1.0
            },
            statistics: {
                lastPlayTime: Date.now(),
                firstPlayTime: Date.now(),
                totalPlayTime: 0
            }
        };

        console.log('SaveManager初始化完成');
    }

    /**
     * 加载游戏数据
     * @returns {Object} 游戏数据对象
     */
    loadGameData() {
        try {
            // 尝试加载主存档
            let saveData = this.loadFromStorage(this.saveKey);

            if (!saveData) {
                // 主存档不存在，尝试加载备份
                console.log('主存档不存在，尝试加载备份存档');
                saveData = this.loadFromStorage(this.backupKey);

                if (saveData) {
                    // 备份存档存在，恢复到主存档
                    this.saveToStorage(this.saveKey, saveData);
                    console.log('从备份存档恢复数据成功');
                }
            }

            if (!saveData) {
                // 都不存在，创建新的存档
                console.log('未找到存档，创建新的游戏数据');
                saveData = this.createNewSave();
                this.saveGameData(saveData);
            } else {
                // 验证和迁移数据
                saveData = this.validateAndMigrateData(saveData);
            }

            console.log('游戏数据加载完成:', saveData);
            return saveData;

        } catch (error) {
            console.error('加载游戏数据时出错:', error);
            // 出错时返回默认数据
            const newSave = this.createNewSave();
            this.saveGameData(newSave);
            return newSave;
        }
    }

    /**
     * 保存游戏数据
     * @param {Object} gameData - 要保存的游戏数据
     * @returns {boolean} 保存是否成功
     */
    saveGameData(gameData) {
        try {
            // 更新版本和时间戳
            gameData.version = this.version;
            gameData.statistics.lastPlayTime = Date.now();

            // 验证数据
            const validatedData = this.validateData(gameData);

            // 先备份当前数据
            const currentData = this.loadFromStorage(this.saveKey);
            if (currentData) {
                this.saveToStorage(this.backupKey, currentData);
            }

            // 保存新数据
            const success = this.saveToStorage(this.saveKey, validatedData);

            if (success) {
                console.log('游戏数据保存成功');
                return true;
            } else {
                console.error('游戏数据保存失败');
                return false;
            }

        } catch (error) {
            console.error('保存游戏数据时出错:', error);
            return false;
        }
    }

    /**
     * 从localStorage加载数据
     * @param {string} key - 存储键
     * @returns {Object|null} 加载的数据或null
     */
    loadFromStorage(key) {
        try {
            const dataString = localStorage.getItem(key);
            if (!dataString) {
                return null;
            }

            const data = JSON.parse(dataString);
            return data;

        } catch (error) {
            console.error(`从localStorage加载数据失败 (${key}):`, error);
            return null;
        }
    }

    /**
     * 保存数据到localStorage
     * @param {string} key - 存储键
     * @param {Object} data - 要保存的数据
     * @returns {boolean} 保存是否成功
     */
    saveToStorage(key, data) {
        try {
            const dataString = JSON.stringify(data);
            localStorage.setItem(key, dataString);
            return true;

        } catch (error) {
            console.error(`保存数据到localStorage失败 (${key}):`, error);

            // 检查是否是存储空间不足
            if (error.name === 'QuotaExceededError') {
                console.error('localStorage存储空间不足');
                // 可以在这里实现清理旧数据的逻辑
            }

            return false;
        }
    }

    /**
     * 创建新的存档数据
     * @returns {Object} 新的游戏数据
     */
    createNewSave() {
        const newData = JSON.parse(JSON.stringify(this.defaultData));
        newData.statistics.firstPlayTime = Date.now();
        newData.statistics.lastPlayTime = Date.now();
        return newData;
    }

    /**
     * 验证和迁移数据
     * @param {Object} data - 要验证的数据
     * @returns {Object} 验证后的数据
     */
    validateAndMigrateData(data) {
        try {
            // 检查版本并进行迁移
            if (!data.version || data.version !== this.version) {
                console.log(`数据版本不匹配，从 ${data.version || 'unknown'} 迁移到 ${this.version}`);
                data = this.migrateData(data);
            }

            // 验证数据完整性
            return this.validateData(data);

        } catch (error) {
            console.error('验证和迁移数据时出错:', error);
            return this.createNewSave();
        }
    }

    /**
     * 数据迁移
     * @param {Object} oldData - 旧版本数据
     * @returns {Object} 迁移后的数据
     */
    migrateData(oldData) {
        // 创建新的数据结构
        const newData = this.createNewSave();

        // 迁移玩家数据
        if (oldData.player) {
            newData.player.totalCoins = oldData.player.totalCoins || 0;
            newData.player.totalGames = oldData.player.totalGames || 0;
            newData.player.totalSurvivalTime = oldData.player.totalSurvivalTime || 0;
            newData.player.bestSurvivalTime = oldData.player.bestSurvivalTime || 0;
            newData.player.totalKills = oldData.player.totalKills || 0;
            newData.player.maxLevel = oldData.player.maxLevel || 1;
            newData.player.unlockedWeapons = oldData.player.unlockedWeapons || [];
            newData.player.unlockedItems = oldData.player.unlockedItems || [];
            newData.player.achievements = oldData.player.achievements || [];
        }

        // 迁移设置数据
        if (oldData.settings) {
            newData.settings = { ...newData.settings, ...oldData.settings };
        }

        // 迁移统计数据
        if (oldData.statistics) {
            newData.statistics.firstPlayTime = oldData.statistics.firstPlayTime || newData.statistics.firstPlayTime;
            newData.statistics.totalPlayTime = oldData.statistics.totalPlayTime || 0;
        }

        console.log('数据迁移完成');
        return newData;
    }

    /**
     * 验证数据完整性
     * @param {Object} data - 要验证的数据
     * @returns {Object} 验证后的数据
     */
    validateData(data) {
        const validatedData = JSON.parse(JSON.stringify(this.defaultData));

        // 合并和验证数据
        if (data.player) {
            validatedData.player = { ...validatedData.player, ...data.player };

            // 确保数值类型正确
            validatedData.player.totalCoins = Math.max(0, Number(validatedData.player.totalCoins) || 0);
            validatedData.player.totalGames = Math.max(0, Number(validatedData.player.totalGames) || 0);
            validatedData.player.totalSurvivalTime = Math.max(0, Number(validatedData.player.totalSurvivalTime) || 0);
            validatedData.player.bestSurvivalTime = Math.max(0, Number(validatedData.player.bestSurvivalTime) || 0);
            validatedData.player.totalKills = Math.max(0, Number(validatedData.player.totalKills) || 0);
            validatedData.player.maxLevel = Math.max(1, Number(validatedData.player.maxLevel) || 1);

            // 确保数组类型正确
            validatedData.player.unlockedWeapons = Array.isArray(validatedData.player.unlockedWeapons) ? validatedData.player.unlockedWeapons : [];
            validatedData.player.unlockedItems = Array.isArray(validatedData.player.unlockedItems) ? validatedData.player.unlockedItems : [];
            validatedData.player.achievements = Array.isArray(validatedData.player.achievements) ? validatedData.player.achievements : [];
            validatedData.player.ownedPets = Array.isArray(validatedData.player.ownedPets) ? validatedData.player.ownedPets : ['pet001'];
            validatedData.player.ownedWorkstations = Array.isArray(validatedData.player.ownedWorkstations) ? validatedData.player.ownedWorkstations : [];
        }

        if (data.settings) {
            validatedData.settings = { ...validatedData.settings, ...data.settings };
        }

        if (data.statistics) {
            validatedData.statistics = { ...validatedData.statistics, ...data.statistics };
        }

        return validatedData;
    }

    /**
     * 添加金币
     * @param {number} amount - 要添加的金币数量
     * @returns {boolean} 操作是否成功
     */
    addCoins(amount) {
        try {
            const gameData = this.loadGameData();
            gameData.player.totalCoins += Math.max(0, Number(amount) || 0);
            return this.saveGameData(gameData);
        } catch (error) {
            console.error('添加金币时出错:', error);
            return false;
        }
    }

    /**
     * 获取累计金币数量
     * @returns {number} 累计金币数量
     */
    getTotalCoins() {
        try {
            const gameData = this.loadGameData();
            return gameData.player.totalCoins || 0;
        } catch (error) {
            console.error('获取累计金币时出错:', error);
            return 0;
        }
    }

    /**
     * 清除所有存档数据（用于调试）
     */
    clearAllData() {
        try {
            localStorage.removeItem(this.saveKey);
            localStorage.removeItem(this.backupKey);
            console.log('所有存档数据已清除');
        } catch (error) {
            console.error('清除存档数据时出错:', error);
        }
    }
}
