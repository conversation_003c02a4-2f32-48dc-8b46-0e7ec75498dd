# 地狱潜兵2完整系统复刻文档
## 详细到可直接填表落地的程度

---

## 系统架构总览

```
超级地球战舰（家园系统）
├── 银河战争地图（全服共同目标）
├── 战舰升级系统（个人成长核心）
├── 装备库（收藏展示）
├── 战略支援研究（科技树）
└── 社交系统（好友/战队）
```

---

## 一、家园系统：超级地球战舰

### 1.1 战舰房间布局
```
战舰内部房间：
├── 驾驶舱（Bridge）- 银河战争地图
├── 爱国主义管理中心（Patriotic Administration Center）- 支援武器管理
├── 轨道炮台（Orbital Cannons）- 轨道支援管理
├── 机库（Hangar）- 鹰式支援管理
├── 工程舱（Engineering Bay）- 工事支援管理
└── 机器人工坊（Robotics Workshop）- 哨兵支援管理
```

### 1.2 战舰升级系统（Ship Modules）

**升级材料需求**
- 普通样本（Common Sample）：绿色，基础升级材料
- 稀有样本（Rare Sample）：橙色，中级升级材料  
- 超级样本（Super Sample）：粉色，高级升级材料
- 征用券（Requisition Slips）：4-5级升级专用货币

**各房间升级路径（5级升级系统）**

**爱国主义管理中心升级**
```
1级：捐赠许可证 - 60普通样本
效果：支援武器部署时携带最大弹药数

2级：简化申请流程 - 80普通+40稀有样本
效果：支援武器战略支援冷却时间-10%

3级：手推车 - 80普通+60稀有+5超级样本
效果：所有背包类战略支援冷却时间-10%

4级：优化包装方法 - 150普通+150稀有+15超级+20,000征用券
效果：补给箱为支援武器提供最大弹药数

5级：薪资管理系统 - 200普通+200稀有+20超级+30,000征用券
效果：所有支援武器装弹时间-10%
```

**轨道炮台升级**
```
1级：爆炸弹片 - 100普通样本
效果：减少轨道支援爆炸中心伤害衰减

2级：更多火炮 - 80普通+60稀有样本
效果：弹幕类轨道支援+1轮射击

3级：零重力装弹 - 80普通+80稀有+10超级样本
效果：轨道支援冷却时间-10%

4级：大气监测 - 200普通+150稀有+15超级+25,000征用券
效果：轨道HE弹幕散布-15%

5级：高密度炸药 - 250普通+200稀有+20超级+35,000征用券
效果：轨道支援爆炸半径+10%
```

**机库升级**
```
1级：液体通风驾驶舱 - 80普通样本
效果：鹰式支援冷却时间-50%

2级：地勤危险津贴 - 80普通+40稀有样本
效果：鹰式重新装备时间-20%

3级：扩展武器舱 - 80普通+80稀有+10超级样本
效果：鹰式支援每次重装+1次使用

4级：XXL武器舱 - 150普通+150稀有+15超级+25,000征用券
效果：投掷多枚炸弹的鹰式支援+1枚炸弹

5级：高级机组训练 - 200普通+250稀有+25超级+30,000征用券
效果：在鹰式仍有使用次数时呼叫重装，冷却时间额外-10%
```

**驾驶舱升级**
```
1级：瞄准软件升级 - 60普通样本
效果：所有轨道支援呼叫时间-1秒

2级：核雷达 - 80普通+40稀有样本
效果：小地图敌人探测半径+50%

3级：动力转向 - 80普通+80稀有+10超级样本
效果：改善地狱舱部署时的操控性

4级：增强燃烧 - 200普通+150稀有+15超级+25,000征用券
效果：战略支援火焰伤害+25%

5级：士气增强 - 250普通+200稀有+20超级+35,000征用券
效果：所有战略支援冷却时间-5%
```

**工程舱升级**
```
1级：合成补充 - 60普通+10稀有样本
效果：哨兵、工事、补给支援冷却时间-10%

2级：高级建造 - 80普通+60稀有+5超级样本
效果：哨兵支援生命值+50%

3级：快速发射系统 - 80普通+80稀有+10超级样本
效果：所有工事支援立即发射，减少部署时间

4级：电路扩展 - 200普通+150稀有+20超级+20,000征用券
效果：闪电弧线跳跃到额外1个敌人

5级：简化发射流程 - 250普通+200稀有+25超级+30,000征用券
效果：所有支援武器支援立即发射，减少部署时间
```

**机器人工坊升级**
```
1级：动态追踪 - 60普通+20稀有样本
效果：所有哨兵支援立即发射，减少部署时间

2级：冲击吸收凝胶 - 80普通+40稀有+5超级样本
效果：所有哨兵支援弹药+50%

3级：高质量润滑剂 - 80普通+80稀有+10超级样本
效果：哨兵转向新目标速度更快

4级：爆炸吸收 - 150普通+150稀有+20超级+25,000征用券
效果：哨兵受到的爆炸伤害-50%

5级：跨平台兼容性 - 250普通+200稀有+30超级+35,000征用券
效果：迫击炮哨兵优先攻击标记目标
```

**总升级成本**
- 普通样本：3,830个
- 稀有样本：2,920个  
- 超级样本：305个
- 征用券：335,000个

---

## 二、货币系统

### 2.1 货币类型
```
征用券（Requisition Slips）
├── 获得：任务完成基础奖励
├── 用途：购买战略支援、武器解锁
└── 特点：主要货币，获得稳定

勋章（Medals）
├── 获得：战争债券进度、特殊成就
├── 用途：战争债券解锁、高级装备
└── 特点：获得较慢，价值较高

超级地球币（Super Credits）
├── 获得：任务探索、付费购买
├── 用途：超级商店、战争债券购买
└── 特点：稀有货币，可付费获得

样本（Samples）
├── 普通样本：任务中收集，用于1-3级升级
├── 稀有样本：困难任务收集，用于2-4级升级
├── 超级样本：高难度任务，用于3-5级升级
└── 特点：升级专用，需要主动收集
```

### 2.2 样本收集机制
**样本分布规律**
- 普通样本：每个任务3-8个，随机分布
- 稀有样本：每个任务1-3个，特定位置
- 超级样本：困难任务1个，固定位置

**样本携带限制**
- 普通样本：最大携带500个
- 稀有样本：最大携带250个
- 超级样本：最大携带100个

---

## 三、战略支援系统（Stratagems）

### 3.1 支援武器类（Patriotic Administration Center）

**机枪类**
```
MG-43机枪
├── 解锁：1级免费
├── 冷却：480秒
├── 特点：高伤害，高后坐力，固定使用

M-105轻机枪
├── 解锁：3,500征用券
├── 冷却：480秒
├── 特点：低伤害，易操控，快速装弹

MG-206重机枪
├── 解锁：6,000征用券
├── 冷却：480秒
├── 特点：极高伤害，极高后坐力
```

**反装甲武器**
```
EAT-17一次性反坦克
├── 解锁：3,000征用券
├── 冷却：70秒
├── 特点：一次性使用，快速冷却

GR-8无后坐力炮
├── 解锁：6,000征用券
├── 冷却：480秒
├── 特点：需要队友协助装弹

RS-422轨道炮
├── 解锁：10,000征用券
├── 冷却：480秒
├── 特点：需要蓄力，极高穿透

FAF-14长矛
├── 解锁：9,000征用券
├── 冷却：480秒
├── 特点：自动锁定，制导导弹
```

### 3.2 轨道支援类（Orbital Cannons）

**轨道打击**
```
轨道加特林弹幕
├── 解锁：1,500征用券
├── 冷却：70秒
├── 效果：高速旋转机炮弹幕

轨道空爆打击
├── 解锁：4,000征用券
├── 冷却：100秒
├── 效果：空中爆炸，弹片攻击

轨道120mm高爆弹幕
├── 解锁：4,000征用券
├── 冷却：180秒
├── 效果：精确炮击，小范围

轨道380mm高爆弹幕
├── 解锁：7,500征用券
├── 冷却：240秒
├── 效果：大范围持续轰炸

轨道激光
├── 解锁：10,000征用券
├── 冷却：300秒
├── 效果：激光扫射，持续伤害

轨道轨道炮打击
├── 解锁：10,000征用券
├── 冷却：210秒
├── 效果：自动瞄准最大目标
```

### 3.3 鹰式支援类（Hangar）

**鹰式空袭**
```
鹰式扫射
├── 解锁：1,500征用券
├── 冷却：8秒
├── 效果：机炮扫射，清理小目标

鹰式空袭
├── 解锁：4,000征用券
├── 冷却：8秒
├── 效果：炸弹地毯式轰炸

鹰式500kg炸弹
├── 解锁：10,000征用券
├── 冷却：8秒
├── 效果：单发重型炸弹

鹰式集束炸弹
├── 解锁：4,000征用券
├── 冷却：8秒
├── 效果：集束炸弹，范围攻击

鹰式凝固汽油弹
├── 解锁：5,000征用券
├── 冷却：8秒
├── 效果：火焰攻击，持续燃烧
```

**鹰式重装机制**
- 每种鹰式支援有使用次数限制
- 用完后需要呼叫"鹰式重装"
- 重装时间：基础时间可通过升级缩短
- 重装后恢复所有鹰式支援的使用次数

### 3.4 哨兵支援类（Robotics Workshop）

**自动哨兵**
```
机枪哨兵
├── 解锁：1,500征用券
├── 冷却：90秒
├── 特点：全自动，会误伤队友

加特林哨兵
├── 解锁：4,000征用券
├── 冷却：150秒
├── 特点：极高射速，快速消耗弹药

迫击炮哨兵
├── 解锁：7,000征用券
├── 冷却：180秒
├── 特点：曲射攻击，可越过掩体

自动炮哨兵
├── 解锁：6,000征用券
├── 冷却：150秒
├── 特点：反装甲，远程攻击

火箭哨兵
├── 解锁：7,500征用券
├── 冷却：150秒
├── 特点：优先攻击大型目标
```

---

## 四、银河战争系统

### 4.1 战争地图机制
**星球状态**
```
解放星球（蓝色）
├── 状态：超级地球控制
├── 任务：防御任务，维持控制
└── 奖励：稳定资源产出

争夺星球（黄色）
├── 状态：激战中
├── 任务：解放任务，推进前线
└── 奖励：解放进度贡献

敌占星球（红色）
├── 状态：敌人控制
├── 任务：暂时无法进入
└── 特点：需要先解放相邻星球
```

**解放进度机制**
- 每个星球有解放百分比（0-100%）
- 全服玩家共同推进解放进度
- 完成任务增加解放进度
- 失败任务减少解放进度
- 达到100%即解放成功

### 4.2 主要指令系统（Major Orders）
**指令类型**
```
解放指令
├── 目标：解放指定星球
├── 时限：通常72-168小时
├── 奖励：全服奖励+个人奖励
└── 失败：敌人反攻，失去星球

防御指令
├── 目标：保卫指定星球
├── 时限：通常24-72小时
├── 奖励：防御成功奖励
└── 失败：星球失守

特殊指令
├── 目标：特定任务类型
├── 时限：变动
├── 奖励：特殊装备解锁
└── 特点：推进剧情发展
```

### 4.3 个人指令系统（Personal Orders）
**日常指令**
- 完成X次任务
- 击杀X个敌人
- 收集X个样本
- 使用特定战略支援

**奖励机制**
- 征用券：500-2000
- 勋章：5-20
- 经验值：固定数量

---

## 五、难度系统

### 5.1 难度等级（1-10级）
```
难度1-2：微不足道/简单
├── 敌人：基础单位，数量少
├── 目标：1-2个主要目标
├── 样本：普通样本为主
└── 适合：新手学习

难度3-4：中等/挑战
├── 敌人：精英单位出现
├── 目标：2-3个主要目标
├── 样本：稀有样本出现
└── 适合：熟练玩家

难度5-6：困难/极端
├── 敌人：重装单位，巡逻增加
├── 目标：3个主要目标+副目标
├── 样本：超级样本出现
└── 适合：团队配合

难度7-8：自杀任务/不可能
├── 敌人：BOSS单位，密集巡逻
├── 目标：多重复杂目标
├── 样本：稀有样本增加
└── 适合：专家玩家

难度9-10：地狱潜兵
├── 敌人：最强配置，持续增援
├── 目标：极限挑战
├── 样本：最高收益
└── 适合：顶级玩家
```

### 5.2 敌人缩放机制
**数值缩放公式**
```
敌人血量 = 基础血量 × 难度系数 × 队伍人数系数
敌人伤害 = 基础伤害 × 难度系数
敌人数量 = 基础数量 × 难度系数 × 队伍人数系数
巡逻频率 = 基础频率 × 难度系数
```

**队伍人数影响**
- 1人：100%基础难度
- 2人：120%基础难度
- 3人：140%基础难度
- 4人：160%基础难度

---

## 六、任务系统

### 6.1 任务类型
**主要任务类型**
```
消灭任务
├── 目标：清除虫族巢穴/机器人工厂
├── 机制：摧毁指定建筑
├── 时间：15-25分钟
└── 奖励：基础征用券+样本

收集任务
├── 目标：收集指定物品
├── 机制：探索地图，寻找目标
├── 时间：20-30分钟
└── 奖励：额外样本奖励

护送任务
├── 目标：护送载具/人员
├── 机制：移动防御
├── 时间：25-35分钟
└── 奖励：高额征用券

防御任务
├── 目标：防守指定区域
├── 机制：波次防御
├── 时间：20-30分钟
└── 奖励：防御专用奖励
```

### 6.2 副目标系统
**常见副目标**
- 上传逃生舱数据
- 地质勘探
- 启动SEAF炮台
- 摧毁指挥掩体
- 收集稀有样本

**副目标奖励**
- 额外征用券：100-500
- 额外样本：1-3个
- 额外经验：固定数量

---

## 七、友伤与平衡机制

### 7.1 友伤系统
**友伤规则**
- 所有武器对队友造成100%伤害
- 爆炸物对队友造成100%伤害
- 战略支援对队友造成100%伤害
- 无法关闭友伤

**友伤设计目的**
- 鼓励精准射击
- 增加战术规划重要性
- 提升团队配合要求
- 增加游戏挑战性

### 7.2 增援机制
**增援规则**
- 每个玩家有5次增援机会
- 死亡后需要队友呼叫增援
- 增援冷却时间：2分钟
- 全队死亡则任务失败

**增援战略支援**
- 呼叫代码：↑↓→←↑
- 冷却时间：无（消耗增援次数）
- 特点：队友必须呼叫

---

## 八、战争债券系统（Warbonds）

### 8.1 战争债券类型
**免费战争债券**
- 地狱潜兵动员令
- 解锁：游戏自带
- 内容：基础装备、战略支援

**付费战争债券**
- 钢铁勇士
- 切割者
- 民主爆破
- 极地爱国者
- 价格：1000超级地球币

### 8.2 解锁机制
**勋章获得**
- 任务完成：1-5勋章
- 难度奖励：高难度额外勋章
- 完美任务：额外勋章奖励
- 副目标：额外勋章

**解锁路径**
- 每个战争债券有固定解锁路径
- 需要按顺序解锁
- 某些装备需要前置解锁
- 总计需要300-400勋章完全解锁

---

## 九、武器系统

### 9.1 主武器类别
**突击步枪**
```
R-63解放者
├── 伤害：60
├── 射速：640 RPM
├── 弹匣：30发
├── 解锁：免费
└── 特点：平衡型，适合新手

R-63CS解放者渗透者
├── 伤害：45
├── 射速：640 RPM
├── 弹匣：30发
├── 解锁：战争债券
└── 特点：中等护甲穿透

R-36厄拉迪卡特
├── 伤害：95
├── 射速：535 RPM
├── 弹匣：30发
├── 解锁：战争债券
└── 特点：高伤害，高后坐力
```

**冲锋枪**
```
MP-98骑士
├── 伤害：55
├── 射速：800 RPM
├── 弹匣：30发
├── 解锁：免费
└── 特点：高射速，近距离

MP-9冲击者
├── 伤害：60
├── 射速：720 RPM
├── 弹匣：25发
├── 解锁：战争债券
└── 特点：平衡型冲锋枪
```

**霰弹枪**
```
SG-8惩罚者
├── 伤害：405（单发）
├── 射速：300 RPM
├── 弹匣：16发
├── 解锁：免费
└── 特点：近距离高伤害

SG-225破坏者
├── 伤害：300（单发）
├── 射速：300 RPM
├── 弹匣：12发
├── 解锁：战争债券
└── 特点：爆炸弹药
```

**狙击步枪**
```
R-90神射手
├── 伤害：140
├── 射速：200 RPM
├── 弹匣：15发
├── 解锁：免费
└── 特点：中距离精确射击

SG-8S惩罚者狙击型
├── 伤害：330
├── 射速：85 RPM
├── 弹匣：6发
├── 解锁：战争债券
└── 特点：霰弹狙击，独特机制
```

### 9.2 副武器系统
**手枪**
```
P-19复仇者
├── 伤害：60
├── 弹匣：15发
├── 解锁：免费
└── 特点：标准手枪

P-113裁决者
├── 伤害：150
├── 弹匣：6发
├── 解锁：战争债券
└── 特点：高伤害左轮

GP-31榴弹手枪
├── 伤害：350（爆炸）
├── 弹匣：6发
├── 解锁：战争债券
└── 特点：爆炸弹药
```

### 9.3 投掷物系统
**手雷类**
```
G-12高爆手雷
├── 伤害：400
├── 爆炸半径：6米
├── 引信时间：3.5秒
├── 解锁：免费
└── 特点：标准爆炸手雷

G-6破片手雷
├── 伤害：300+破片
├── 爆炸半径：8米
├── 引信时间：3.5秒
├── 解锁：战争债券
└── 特点：破片伤害

G-23燃烧手雷
├── 伤害：200+燃烧
├── 燃烧时间：8秒
├── 引信时间：2秒
├── 解锁：战争债券
└── 特点：持续火焰伤害

G-13冲击手雷
├── 伤害：350
├── 爆炸半径：5米
├── 引信时间：接触爆炸
├── 解锁：战争债券
└── 特点：撞击即爆
```

---

## 十、护甲系统

### 10.1 护甲等级
**轻型护甲（100-125护甲值）**
```
B-01战术
├── 护甲：100
├── 速度：100%
├── 耐力：100%
├── 解锁：免费
└── 特点：无被动技能

SC-30无畏
├── 护甲：100
├── 速度：100%
├── 耐力：100%
├── 被动：额外手雷
└── 解锁：战争债券
```

**中型护甲（150-175护甲值）**
```
B-27坚韧
├── 护甲：150
├── 速度：95%
├── 耐力：95%
├── 解锁：免费
└── 特点：平衡型

CM-09邦克
├── 护甲：150
├── 速度：95%
├── 耐力：95%
├── 被动：爆炸抗性
└── 解锁：战争债券
```

**重型护甲（200-250护甲值）**
```
B-08轻型工程
├── 护甲：200
├── 速度：85%
├── 耐力：85%
├── 解锁：免费
└── 特点：高防护

FS-55毁灭者
├── 护甲：200
├── 速度：85%
├── 耐力：85%
├── 被动：火焰抗性
└── 解锁：战争债券
```

### 10.2 护甲被动技能
**防护类被动**
- 爆炸抗性：爆炸伤害-50%
- 火焰抗性：火焰伤害-95%
- 酸液抗性：酸液伤害-95%

**实用类被动**
- 额外手雷：+2手雷携带
- 工程套件：可修复载具和工事
- 医疗套件：可治疗队友

**机动类被动**
- 民主保护：增加潜行能力
- 侦察：增加移动速度
- 突击：减少冲刺耐力消耗

---

## 十一、增强剂系统（Boosters）

### 11.1 增强剂类型
**战斗增强**
```
肌肉增强剂
├── 效果：投掷距离+30%
├── 解锁：免费
└── 影响：手雷、战略支援信标

活力增强剂
├── 效果：耐力恢复+100%
├── 解锁：战争债券
└── 影响：跑步、冲刺恢复

坚韧增强剂
├── 效果：肢体伤害抗性+50%
├── 解锁：战争债券
└── 影响：减少肢体损伤
```

**战术增强**
```
地狱炸弹增强剂
├── 效果：地狱炸弹爆炸半径+50%
├── 解锁：战争债券
└── 影响：任务目标炸弹

UAV增强剂
├── 效果：雷达扫描范围+100%
├── 解锁：战争债券
└── 影响：小地图敌人显示

灵活增强剂
├── 效果：潜行检测-30%
├── 解锁：战争债券
└── 影响：敌人发现距离
```

**团队增强**
```
激励增强剂
├── 效果：增援时间-25%
├── 解锁：战争债券
└── 影响：全队增援冷却

补给增强剂
├── 效果：补给箱弹药+50%
├── 解锁：战争债券
└── 影响：补给箱内容

医疗增强剂
├── 效果：医疗包治疗+30%
├── 解锁：战争债券
└── 影响：治疗效果
```

### 11.2 增强剂机制
**使用规则**
- 每个玩家可装备1个增强剂
- 增强剂效果影响全队
- 相同增强剂不叠加
- 不同增强剂可以叠加

**解锁方式**
- 基础增强剂：免费获得
- 高级增强剂：战争债券解锁
- 特殊增强剂：特定成就解锁

---

## 十二、敌人系统

### 12.1 虫族敌人（Terminids）
**基础单位**
```
小虫（Scavenger）
├── 血量：45
├── 护甲：无
├── 攻击：近战撕咬
├── 特点：群体攻击，速度快
└── 威胁：低

战士虫（Warrior）
├── 血量：450
├── 护甲：轻型
├── 攻击：近战+酸液喷射
├── 特点：中等威胁，常见
└── 弱点：头部

猎手虫（Hunter）
├── 血量：200
├── 护甲：无
├── 攻击：跳跃攻击
├── 特点：高机动性
└── 弱点：全身
```

**精英单位**
```
战车虫（Charger）
├── 血量：2000
├── 护甲：重型（正面）
├── 攻击：冲撞
├── 特点：高血量，需要反装甲武器
└── 弱点：后腿、腹部

胆汁泰坦（Bile Titan）
├── 血量：5000
├── 护甲：重型
├── 攻击：酸液喷射+践踏
├── 特点：BOSS级单位
└── 弱点：头部、腹部
```

### 12.2 机器人敌人（Automatons）
**基础单位**
```
步兵机器人（Trooper）
├── 血量：125
├── 护甲：轻型
├── 攻击：激光步枪
├── 特点：远程攻击，精准射击
└── 弱点：头部

重装机器人（Heavy Devastator）
├── 血量：800
├── 护甲：中型
├── 攻击：重型激光炮
├── 特点：高伤害，慢速移动
└── 弱点：背部散热器
```

**载具单位**
```
侦察步行者（Scout Strider）
├── 血量：1200
├── 护甲：中型
├── 攻击：机炮
├── 特点：双腿载具，中等威胁
└── 弱点：驾驶舱、腿部关节

坦克（Tank）
├── 血量：3500
├── 护甲：重型
├── 攻击：主炮+机枪
├── 特点：重装甲，需要反装甲武器
└── 弱点：后部散热器
```

### 12.3 敌人AI行为
**巡逻机制**
- 固定路线巡逻
- 发现玩家后呼叫增援
- 增援强度随难度提升
- 巡逻密度随难度增加

**警戒机制**
- 听到枪声会调查
- 发现尸体会提高警戒
- 警戒状态下增援更频繁
- 隐蔽击杀可避免警戒

---

**文档总结**：本文档提供了地狱潜兵2完整系统的详细复刻方案，包含所有核心系统的具体数值、机制和实现细节。所有数据均基于游戏实际参数，可直接用于系统开发和平衡调整。文档涵盖了从家园系统到战斗机制的完整游戏循环，确保复刻的完整性和准确性。
