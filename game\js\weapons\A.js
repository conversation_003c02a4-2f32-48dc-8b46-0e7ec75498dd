/**
 * 武器A
 */
class A extends Weapon {
    /**
     * 升级路径定义
     * 每个等级对应的升级效果和描述
     */
    static UPGRADE_PATH = [
        { level: 1, description: "基础武器A" },
        { level: 2, description: "射弹+1", effect: "增加1个射弹" },
        { level: 3, description: "伤害+5", effect: "伤害提升5点" },
        { level: 4, description: "伤害+5、范围+10%", effect: "伤害和范围提升" },
        { level: 5, description: "伤害+5", effect: "伤害提升5点" },
        { level: 6, description: "伤害+5、范围+10%", effect: "伤害和范围提升" },
        { level: 7, description: "伤害+5", effect: "伤害提升5点" },
        { level: 8, description: "伤害+5", effect: "伤害提升5点" }
    ];

    /**
     * 创建武器A
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {Player} player - 玩家对象
     * @param {boolean} isAcquired - 是否已获得武器，默认为true
     */
    constructor(scene, player, isAcquired = true) {
        super(scene, player, isAcquired);

        // 武器属性
        this.name = "A";
        this.damageMin = 5;       // 最小伤害值
        this.damageMax = 8;       // 最大伤害值
        this.cooldownTime = 1350;  // 冷却时间(毫秒)
        this.range = 96;           // 攻击范围(像素)
        this.width = 40;           // 武器宽度(像素)
        this.knockbackForce = 40;  // 击退力度
        this.level = 1;            // 武器等级
        this.projectileCount = 1;  // 射弹数量

        // 记录初始武器属性（用于计算比例）
        this.initialRange = 120;   // 初始攻击范围
        this.initialWidth = 50;    // 初始武器宽度

        // 特效精灵初始尺寸（与武器属性不同）
        this.effectInitialWidth = 120;  // 特效初始宽度
        this.effectInitialHeight = 160; // 特效初始高度

        // 创建武器精灵组
        this.weaponGroup = this.scene.physics.add.group();

        // 创建特效精灵组（仅用于视觉效果，不参与碰撞）
        this.effectGroup = this.scene.add.group();

        // 特效资源是否已加载完成的标志
        this.effectsLoaded = false;

        // 调试标志
        this.debug = window.GameDebug?.weapons || false; // 使用全局调试配置

        // 预加载武器图像
        this.preloadAssets();
    }

    /**
     * 重写update方法，使武器效果跟随玩家移动
     * @param {number} time - 当前时间
     */
    update(time) {
        // 调用父类update来处理攻击冷却
        super.update(time);

        // 更新所有武器和特效的位置，使其跟随玩家
        if (this.player && this.player.sprite) {
            // 更新武器组中的所有元素位置
            this.weaponGroup.getChildren().forEach(weapon => {
                weapon.x = this.player.sprite.x;
                weapon.y = this.player.sprite.y - 10; // 保持原有的Y轴偏移量
                // 更新碰撞体位置
                weapon.body.reset(weapon.x, weapon.y);
            });

            // 更新特效组中的所有元素位置
            this.effectGroup.getChildren().forEach(effect => {
                effect.x = this.player.sprite.x;
                effect.y = this.player.sprite.y - 10; // 保持原有的Y轴偏移量
            });
        }
    }

    /**
     * 预加载资源
     */
    preloadAssets() {
        // 始终重新创建武器图像，以确保它反映当前的range和width
        // 如果纹理已存在，先销毁它
        if (this.scene.textures.exists('weaponA')) {
            this.scene.textures.remove('weaponA');
        }

        // 创建一个简单的武器图像（用于碰撞检测）
        const graphics = this.scene.make.graphics({ x: 0, y: 0 });
        graphics.fillStyle(0xFFFFFF, 1);
        graphics.fillRect(0, 0, this.range, this.width);
        graphics.generateTexture('weaponA', this.range, this.width);
        graphics.destroy();

        console.log(`武器纹理已更新: 长度=${this.range}, 宽度=${this.width}`);

        // 资源已在StartScene中预加载，这里只需创建动画
        // 创建向右的动画
        if (!this.scene.anims.exists('a_effect_right')) {
            this.scene.anims.create({
                key: 'a_effect_right',
                frames: Array.from({ length: 5 }, (_, i) => {
                    const frameNumber = (i + 1).toString().padStart(3, '0');
                    return { key: `a_effect_${frameNumber}` };
                }),
                frameRate: 15, // 调整帧率以保持动画总时长大致相同
                repeat: 0
            });
        }

        // 检查资源是否已加载
        let loadedCount = 0;
        for (let i = 1; i <= 5; i++) {
            const frameNumber = i.toString().padStart(3, '0');
            const key = `a_effect_${frameNumber}`;
            if (this.scene.textures.exists(key)) {
                loadedCount++;
            }
        }

        console.log(`武器A特效资源检查: ${loadedCount}/5 帧已加载`);

        // 标记特效资源已加载完成
        this.effectsLoaded = true;
        console.log('武器A特效动画已创建');
    }

    /**
     * 攻击方法
     */
    attack() {
        // 计算伤害
        const damage = Phaser.Math.Between(this.damageMin, this.damageMax);

        // 获取玩家朝向
        const direction = this.player.sprite.flipX ? -1 : 1;

        // 添加Y轴偏移量，使武器中心点与玩家中心点对齐
        const yOffset = -10; // 向上偏移10像素

        // 确保敌人组存在
        if (!this.scene.enemiesGroup) {
            console.warn('敌人组不存在，无法设置碰撞检测');
            return;
        }

        // 根据射弹数量创建多个武器实例
        for (let i = 0; i < this.projectileCount; i++) {
            // 创建前方武器精灵（用于碰撞检测，但设为透明）
            const frontWeapon = this.weaponGroup.create(
                this.player.sprite.x,
                this.player.sprite.y + yOffset,
                'weaponA'
            );

            // 设置前方武器属性
            // 根据方向设置原点，使内侧边缘与玩家中心线重合
            frontWeapon.setOrigin(direction === 1 ? 0 : 1, 0.5);
            frontWeapon.setFlipX(direction === -1);
            frontWeapon.setAlpha(0); // 完全透明，只用于碰撞
            frontWeapon.damage = damage;

            // 创建后方武器精灵（方向相反）
            const backWeapon = this.weaponGroup.create(
                this.player.sprite.x,
                this.player.sprite.y + yOffset,
                'weaponA'
            );

            // 设置后方武器属性
            // 根据方向设置原点，使内侧边缘与玩家中心线重合
            backWeapon.setOrigin(direction === 1 ? 1 : 0, 0.5);
            backWeapon.setFlipX(direction !== -1); // 反向翻转
            backWeapon.setAlpha(0); // 完全透明，只用于碰撞
            backWeapon.damage = damage;

            // 添加前方武器碰撞检测
            const frontCollider = this.scene.physics.add.overlap(
                frontWeapon,
                this.scene.enemiesGroup,
                this.onWeaponHitEnemy,
                null,
                this
            );

            // 添加后方武器碰撞检测
            const backCollider = this.scene.physics.add.overlap(
                backWeapon,
                this.scene.enemiesGroup,
                this.onWeaponHitEnemy,
                null,
                this
            );

            // 创建前方特效精灵（仅用于视觉效果）
            const frontEffect = this.scene.add.sprite(
                this.player.sprite.x,
                this.player.sprite.y + yOffset,
                this.effectsLoaded ? 'a_effect_001' : 'weaponA'
            );
            this.effectGroup.add(frontEffect);

            // 设置前方特效的深度（如果DepthManager可用）
            frontEffect.customType = 'WeaponA';
            if (typeof DepthManager !== 'undefined') {
                DepthManager.updateDepth(frontEffect, 'WeaponA', false);
            }

            // 设置前方特效属性
            // 根据方向设置原点，使内侧边缘与玩家中心线重合
            frontEffect.setOrigin(direction === 1 ? 0 : 1, 0.5);
            // 计算当前范围相对于初始范围的比例
            const rangeRatio = this.range / this.initialRange;
            const widthRatio = this.width / this.initialWidth;

            // 使用初始特效尺寸乘以比例
            frontEffect.displayWidth = this.effectInitialWidth * rangeRatio; // 宽度按范围比例缩放
            frontEffect.displayHeight = this.effectInitialHeight * widthRatio; // 高度按宽度比例缩放
            frontEffect.setFlipX(direction === -1);

            // 创建后方特效精灵（仅用于视觉效果）
            const backEffect = this.scene.add.sprite(
                this.player.sprite.x,
                this.player.sprite.y + yOffset,
                this.effectsLoaded ? 'a_effect_001' : 'weaponA'
            );
            this.effectGroup.add(backEffect);

            // 设置后方特效的深度（如果DepthManager可用）
            backEffect.customType = 'WeaponA';
            if (typeof DepthManager !== 'undefined') {
                DepthManager.updateDepth(backEffect, 'WeaponA', false);
            }

            // 设置后方特效属性
            // 根据方向设置原点，使内侧边缘与玩家中心线重合
            backEffect.setOrigin(direction === 1 ? 1 : 0, 0.5);
            // 使用与前方特效相同的比例
            backEffect.displayWidth = this.effectInitialWidth * rangeRatio; // 宽度按范围比例缩放
            backEffect.displayHeight = this.effectInitialHeight * widthRatio; // 高度按宽度比例缩放
            backEffect.setFlipX(direction !== -1);

            // 如果特效资源已加载，播放动画
            if (this.effectsLoaded) {
                // 播放前方特效动画
                frontEffect.play('a_effect_right');

                // 播放后方特效动画
                backEffect.play('a_effect_right');

                // 监听前方特效动画完成事件
                frontEffect.on('animationcomplete', () => {
                    frontEffect.destroy();
                });

                // 监听后方特效动画完成事件
                backEffect.on('animationcomplete', () => {
                    backEffect.destroy();
                });
            } else {
                // 如果特效资源未加载，设置一个简单的淡出效果
                this.scene.tweens.add({
                    targets: [frontEffect, backEffect],
                    alpha: 0,
                    duration: 300,
                    onComplete: () => {
                        frontEffect.destroy();
                        backEffect.destroy();
                    }
                });
            }

            // 设置武器碰撞体的生命周期（与动画时长匹配）
            this.scene.time.delayedCall(350, () => { // 调整为5帧动画的大致时长
                // 移除碰撞检测
                if (frontCollider) {
                    frontCollider.destroy();
                }
                // 销毁武器精灵
                frontWeapon.destroy();

                // 移除碰撞检测
                if (backCollider) {
                    backCollider.destroy();
                }
                // 销毁武器精灵
                backWeapon.destroy();
            });

            // 安全机制：确保特效精灵在400毫秒后一定会被销毁（即使动画完成事件没有触发）
            this.scene.time.delayedCall(400, () => { // 调整为略大于动画时长
                if (frontEffect && frontEffect.active) {
                    frontEffect.destroy();
                }
                if (backEffect && backEffect.active) {
                    backEffect.destroy();
                }
            });
        }
    }

    /**
     * 武器击中敌人的回调
     * @param {Phaser.GameObjects.Sprite} weapon - 武器精灵
     * @param {Phaser.GameObjects.Sprite} enemySprite - 敌人精灵
     */
    onWeaponHitEnemy(weapon, enemySprite) {
        // 获取敌人实例
        const enemy = enemySprite.enemyInstance;
        if (!enemy || !enemy.isAlive) return;

        // 对敌人造成伤害
        const damaged = this.damageEnemy(enemy, weapon.damage);

        // 如果敌人还活着，应用击退效果
        if (damaged && enemy.isAlive && enemy.knockback) {
            // 计算击退方向（从武器中心指向敌人的方向）
            const knockbackAngle = Math.atan2(
                enemySprite.y - weapon.y,
                enemySprite.x - weapon.x
            );

            // 应用击退效果
            enemy.knockback(knockbackAngle, this.knockbackForce);
        }
    }

    /**
     * 升级武器
     * 根据设计文档中的升级路径实现
     * @returns {boolean} 是否成功升级
     */
    upgrade() {
        // 检查是否已达到最高级
        if (this.level >= 8) {
            console.log(`${this.name}已达到最高级，无法继续升级`);
            return false;
        }

        // 升级前保存当前属性，用于日志
        const oldDamageMin = this.damageMin;
        const oldDamageMax = this.damageMax;
        const oldRange = this.range;
        const oldWidth = this.width;
        const oldProjectileCount = this.projectileCount;

        // 根据当前等级应用升级效果
        this.level++;

        switch (this.level) {
            case 2:
                // 伤害+5、范围+20%
                this.damageMin += 5;
                this.damageMax += 5;
                this.range *= 1.2;
                this.width *= 1.2;
                break;
            case 3:
                // 伤害+5
                this.damageMin += 5;
                this.damageMax += 5;
                break;
            case 4:
                // 伤害+5、范围+20%
                this.damageMin += 5;
                this.damageMax += 5;
                this.range *= 1.2;
                this.width *= 1.2;
                break;
            case 5:
                // 伤害+5
                this.damageMin += 5;
                this.damageMax += 5;
                break;
            case 6:
                // 伤害+5、范围+20%
                this.damageMin += 5;
                this.damageMax += 5;
                this.range *= 1.2;
                this.width *= 1.2;
                break;
            case 7:
                // 伤害+5
                this.damageMin += 5;
                this.damageMax += 5;
                break;
            case 8:
                // 伤害+5
                this.damageMin += 5;
                this.damageMax += 5;
                break;
        }

        // 更新武器纹理以反映新的尺寸
        this.preloadAssets();

        // 记录升级日志
        console.log(`${this.name}升级到${this.level}级`);
        console.log(`伤害: ${oldDamageMin}-${oldDamageMax} -> ${this.damageMin}-${this.damageMax}`);
        console.log(`范围: ${oldRange} -> ${this.range}`);
        console.log(`宽度: ${oldWidth} -> ${this.width}`);
        console.log(`射弹数: ${oldProjectileCount} -> ${this.projectileCount}`);

        return true;
    }
}
