/**
 * 武器基类
 */
class Weapon {
    /**
     * 创建武器
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {Player} player - 玩家对象
     * @param {boolean} isAcquired - 是否已获得武器，默认为true
     */
    constructor(scene, player, isAcquired = true) {
        this.scene = scene;
        this.player = player;
        this.cooldownTime = 1000; // 默认冷却时间(毫秒)
        this.nextAttackTime = 0;  // 下次可攻击时间
        this.damage = 10;         // 默认伤害
        this.name = "基础武器";    // 武器名称
        this.level = 1;           // 武器等级
        this.isAcquired = isAcquired; // 是否已获得武器
    }

    /**
     * 更新武器状态
     * @param {number} time - 当前时间
     */
    update(time) {
        // 如果武器未获得，不执行攻击
        if (!this.isAcquired) {
            return;
        }

        // 检查是否可以攻击
        if (time > this.nextAttackTime) {
            this.attack();
            // 设置下次攻击时间
            this.nextAttackTime = time + this.cooldownTime;
        }
    }

    /**
     * 攻击方法（由子类实现）
     */
    attack() {
        // 由子类实现
    }

    /**
     * 升级武器（由子类实现）
     * @returns {boolean} 是否成功升级
     */
    upgrade() {
        // 基类中的默认实现，子类应该覆盖此方法
        console.warn(`${this.name}的upgrade方法未实现`);
        return false;
    }

    /**
     * 对敌人造成伤害
     * @param {Enemy} enemy - 敌人对象
     * @param {number} damage - 伤害值
     * @returns {boolean} 是否成功造成伤害
     */
    damageEnemy(enemy, damage) {
        if (enemy && enemy.takeDamage && enemy.sprite) {
            // 保存敌人位置（在造成伤害前）
            const enemyX = enemy.sprite.x;
            const enemyY = enemy.sprite.y;

            // 应用攻击力加成
            damage = damage * (1 + this.player.attackPower / 100);

            // 计算是否暴击
            const isCrit = Math.random() < 0.1; // 10%暴击率
            const finalDamage = isCrit ? damage * 2 : damage; // 暴击2倍伤害

            // 对敌人造成伤害，检查是否成功（不在冷却期内）
            const damaged = enemy.takeDamage(finalDamage);

            // 只有成功造成伤害时才显示伤害数字
            if (damaged) {
                // 显示伤害数字（使用之前保存的位置）
                this.showDamageNumber(enemyX, enemyY, finalDamage, isCrit);
                return true;
            }
        }
        return false;
    }

    /**
     * 显示伤害数字
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} damage - 伤害值
     * @param {boolean} isCrit - 是否暴击
     */
    showDamageNumber(x, y, damage, isCrit) {
        // 创建伤害文本
        const damageText = this.scene.add.text(
            x,
            y - 20,
            Math.round(damage).toString(),
            {
                font: isCrit ? 'bold 24px Arial' : '16px Arial',
                fill: isCrit ? '#ff0000' : '#ffffff',
                stroke: '#000000',
                strokeThickness: 3
            }
        );

        // 设置自定义类型
        damageText.customType = 'DamageText';

        // 设置深度 - 使用浮动文本层级，并添加偏移确保在武器特效之上
        if (this.scene.DEPTH_LAYERS && this.scene.DEPTH_LAYERS.FLOATING_TEXT) {
            damageText.setDepth(this.scene.DEPTH_LAYERS.FLOATING_TEXT + 10);
        } else if (typeof DepthManager !== 'undefined' && DepthManager.LAYERS) {
            damageText.setDepth(DepthManager.LAYERS.FLOATING_TEXT + 10);
        } else {
            // 如果没有深度管理器，使用一个较高的固定值
            damageText.setDepth(510); // FLOATING_TEXT(500) + 10
        }

        // 设置文本动画
        this.scene.tweens.add({
            targets: damageText,
            y: y - 50,
            alpha: 0,
            duration: 800,
            onComplete: () => {
                damageText.destroy();
            }
        });
    }
}
