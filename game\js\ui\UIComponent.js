/**
 * UI组件基类
 * 所有UI组件的基础类，提供通用功能
 */
class UIComponent {
    constructor(scene, config = {}) {
        this.scene = scene;
        this.config = config;
        this.id = null;
        this.uiManager = null;

        // 基础属性
        this.x = config.x || 0;
        this.y = config.y || 0;
        this.width = config.width || 100;
        this.height = config.height || 50;
        this.visible = config.visible !== false;
        this.interactive = config.interactive !== false;

        // 深度管理
        this.depth = config.depth || DepthManager.LAYERS.UI;

        // 滚动因子
        this.scrollFactor = config.scrollFactor !== undefined ? config.scrollFactor : 0;

        // Phaser游戏对象容器
        this.gameObjects = [];

        // 事件监听器
        this.eventListeners = [];

        // 是否已销毁
        this.destroyed = false;

        console.log('UIComponent基类初始化');
    }

    /**
     * 创建组件 - 子类必须实现
     */
    create() {
        throw new Error('UIComponent子类必须实现create方法');
    }

    /**
     * 更新组件 - 子类可选实现
     */
    update() {
        // 子类可以重写此方法
    }

    /**
     * 设置位置
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     */
    setPosition(x, y) {
        this.x = x;
        this.y = y;

        // 更新所有游戏对象的位置
        this.gameObjects.forEach(obj => {
            if (obj && obj.setPosition) {
                obj.setPosition(x, y);
            }
        });

        return this;
    }

    /**
     * 设置可见性
     * @param {boolean} visible - 是否可见
     */
    setVisible(visible) {
        this.visible = visible;

        // 更新所有游戏对象的可见性
        this.gameObjects.forEach(obj => {
            if (obj && obj.setVisible) {
                obj.setVisible(visible);
            }
        });

        return this;
    }

    /**
     * 设置深度
     * @param {number} depth - 深度值
     */
    setDepth(depth) {
        this.depth = depth;

        // 更新所有游戏对象的深度
        this.gameObjects.forEach((obj, index) => {
            if (obj && obj.setDepth) {
                obj.setDepth(depth + index);
            }
        });

        return this;
    }

    /**
     * 添加游戏对象到组件
     * @param {Phaser.GameObjects.GameObject} gameObject - 游戏对象
     */
    addGameObject(gameObject) {
        if (gameObject) {
            this.gameObjects.push(gameObject);

            // 设置基础属性
            if (gameObject.setScrollFactor) {
                if (typeof this.scrollFactor === 'number') {
                    gameObject.setScrollFactor(this.scrollFactor);
                } else {
                    gameObject.setScrollFactor(this.scrollFactor, this.scrollFactor);
                }
            }
            if (gameObject.setDepth) {
                gameObject.setDepth(this.depth + this.gameObjects.length - 1);
            }
            if (gameObject.setVisible) {
                gameObject.setVisible(this.visible);
            }
        }
    }

    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     * @param {Object} context - 上下文
     */
    on(event, callback, context = this) {
        if (this.uiManager) {
            const listener = this.uiManager.getEventManager().on(event, callback, context);
            this.eventListeners.push(listener);
        }
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {...any} args - 事件参数
     */
    emit(event, ...args) {
        if (this.uiManager) {
            this.uiManager.getEventManager().emit(event, ...args);
        }
    }

    /**
     * 销毁组件
     */
    destroy() {
        if (this.destroyed) return;

        // 移除所有事件监听器
        this.eventListeners.forEach(listener => {
            if (listener && listener.remove) {
                listener.remove();
            }
        });
        this.eventListeners = [];

        // 销毁所有游戏对象
        this.gameObjects.forEach(obj => {
            if (obj && obj.destroy) {
                obj.destroy();
            }
        });
        this.gameObjects = [];

        this.destroyed = true;
        console.log(`UI组件已销毁: ${this.id || 'unknown'}`);
    }
}
