/**
 * 武器升级工具类
 * 提供生成武器升级选项的函数
 */
class WeaponUpgradeUtils {
    /**
     * 生成武器升级选项
     * 生成与InGameItems.js中道具格式兼容的武器升级选项
     * @param {WeaponManager} weaponManager - 武器管理器
     * @returns {Array} 武器升级选项数组
     */
    static generateWeaponUpgradeOptions(weaponManager) {
        if (!weaponManager || !weaponManager.availableWeapons) {
            return [];
        }

        const upgradeOptions = [];

        // 遍历所有可用武器
        for (const weapon of weaponManager.availableWeapons) {
            // 如果武器未获得，生成获取选项
            if (!weapon.isAcquired) {
                const acquireOption = this.createAcquireOption(weapon, weaponManager);
                upgradeOptions.push(acquireOption);
            }
            // 如果武器已获得且未达到最高级，生成升级选项
            else if (weapon.level < 8) {
                const upgradeOption = this.createUpgradeOption(weapon, weaponManager);
                upgradeOptions.push(upgradeOption);
            }
        }

        return upgradeOptions;
    }

    /**
     * 创建获取武器选项
     * @param {Weapon} weapon - 武器对象
     * @param {WeaponManager} weaponManager - 武器管理器
     * @returns {Object} 获取武器选项
     */
    static createAcquireOption(weapon, weaponManager) {
        // 获取武器类的升级路径第一级信息
        const weaponInfo = weapon.constructor.UPGRADE_PATH ?
            weapon.constructor.UPGRADE_PATH[0] : null;

        const description = weaponInfo ?
            weaponInfo.description || `获得${weapon.name}` :
            `获得${weapon.name}`;

        // 获取武器类型
        const weaponType = weapon.name.replace('武器', '');

        return {
            id: `acquire_${weapon.name}`,
            name: `获取${weapon.name}`,
            description: description,
            type: 'new_weapon',
            target: 'weapon',
            weaponName: weapon.name,
            weaponType: weaponType,
            // 应用函数，调用weaponManager的acquireWeapon方法
            apply: function (player) {
                if (player && player.weaponManager) {
                    return player.weaponManager.acquireWeapon(weapon.name);
                }
                return false;
            }
        };
    }

    /**
     * 创建升级武器选项
     * @param {Weapon} weapon - 武器对象
     * @param {WeaponManager} weaponManager - 武器管理器
     * @returns {Object} 升级武器选项
     */
    static createUpgradeOption(weapon, weaponManager) {
        // 获取下一级的升级信息
        const nextLevel = weapon.level + 1;

        // 获取武器类的升级路径
        const upgradePathInfo = weapon.constructor.UPGRADE_PATH ?
            weapon.constructor.UPGRADE_PATH[nextLevel - 1] : null;

        const description = upgradePathInfo ?
            upgradePathInfo.effect || `提升${weapon.name}的能力` :
            `提升${weapon.name}的能力`;

        // 获取武器类型
        const weaponType = weapon.name.replace('武器', '');

        return {
            id: `upgrade_${weapon.name}_to_${nextLevel}`,
            name: `${weapon.name} 升级到 ${nextLevel}级`,
            description: description,
            type: 'weapon_upgrade',
            target: 'weapon',
            weaponName: weapon.name,
            weaponType: weaponType,
            nextLevel: nextLevel,
            // 应用函数，调用weaponManager的upgradeWeapon方法
            apply: function (player) {
                if (player && player.weaponManager) {
                    return player.weaponManager.upgradeWeapon(weapon.name);
                }
                return false;
            }
        };
    }
}
