/**
 * 钻石类 - 敌人死亡后掉落，玩家收集后获得金币
 */
class Diamond {
    /** 缩放比例 */
    static SCALE = 1.5;

    /** 碰撞箱设置 */
    static COLLISION_BOX = {
        type: 'circle',
        radius: 10,
        offsetX: 7,
        offsetY: 6
    };

    /**
     * 创建一个新的钻石
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} value - 金币价值
     */
    constructor(scene, x, y, value = 10) {
        this.scene = scene;
        this.value = value;
        this.type = 'diamond';

        // 加载钻石精灵表
        this.loadAssets();

        // 创建精灵
        this.sprite = scene.physics.add.sprite(x, y, 'diamond_0');
        this.sprite.setScale(Diamond.SCALE);
        this.sprite.setScrollFactor(1);

        // 设置物理属性
        this.sprite.body.setCircle(
            Diamond.COLLISION_BOX.radius,
            Diamond.COLLISION_BOX.offsetX,
            Diamond.COLLISION_BOX.offsetY
        );

        // 添加引用以便在碰撞时获取
        this.sprite.diamondInstance = this;

        // 创建并播放动画
        this.createAnimation();
    }

    /**
     * 加载钻石资源
     */
    loadAssets() {
        // 检查是否已加载动画帧
        if (!this.scene.textures.exists('diamond_0')) {
            // 加载钻石精灵表
            this.scene.load.spritesheet('diamond_sheet',
                'assets/images/diamond/diamond-sh.png',
                { frameWidth: 32, frameHeight: 32 }
            );

            // 立即启动加载
            this.scene.load.start();

            // 等待加载完成
            this.scene.load.once('complete', () => {
                // 创建动画
                this.createAnimation();

                // 如果精灵已存在，播放动画
                if (this.sprite && this.sprite.active) {
                    this.sprite.play('diamond_anim');
                }
            });
        }
    }

    /**
     * 创建钻石动画
     */
    createAnimation() {
        // 检查动画是否已存在
        if (this.scene.anims.exists('diamond_anim')) {
            // 如果精灵存在，播放动画
            if (this.sprite && this.sprite.active) {
                this.sprite.play('diamond_anim');
            }
            return;
        }

        // 确保精灵表已加载
        if (!this.scene.textures.exists('diamond_sheet')) {
            return;
        }

        // 创建帧名称数组
        const frameNames = this.scene.anims.generateFrameNumbers('diamond_sheet', {
            start: 0,
            end: 7 // 假设有8帧，从截图看大约是这个数量
        });

        // 创建动画
        this.scene.anims.create({
            key: 'diamond_anim',
            frames: frameNames,
            frameRate: 10,
            repeat: -1 // 无限循环
        });

        // 如果精灵存在，播放动画
        if (this.sprite && this.sprite.active) {
            this.sprite.play('diamond_anim');
        }
    }

    /**
     * 收集钻石
     */
    collect() {
        if (!this.sprite || !this.sprite.active) return;

        // 触发金币收集事件
        this.scene.events.emit('coinCollected', this.value);

        // 显示收集特效
        this.showCollectEffect();

        // 销毁钻石
        this.destroy();
    }

    /**
     * 显示收集特效
     */
    showCollectEffect() {
        // 粒子效果已移除
        return;
    }

    /**
     * 销毁钻石
     */
    destroy() {
        if (this.sprite) {
            this.sprite.destroy();
            this.sprite = null;
        }
    }
}
