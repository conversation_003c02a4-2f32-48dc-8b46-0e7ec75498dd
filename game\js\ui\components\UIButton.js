/**
 * UI按钮组件
 * 统一的按钮组件，支持主题和交互效果
 */
class UIButton extends UIComponent {
    constructor(scene, config = {}) {
        super(scene, config);
        
        // 按钮特有属性
        this.text = config.text || '按钮';
        this.onClick = config.onClick || null;
        this.onHover = config.onHover || null;
        this.onOut = config.onOut || null;
        
        // 样式属性
        this.backgroundColor = config.backgroundColor || 0x3498db;
        this.hoverColor = config.hoverColor || 0x2980b9;
        this.textColor = config.textColor || '#ffffff';
        this.font = config.font || 'bold 24px Arial';
        
        // 状态
        this.isHovered = false;
        this.isPressed = false;
        this.enabled = config.enabled !== false;
        
        // Phaser对象
        this.background = null;
        this.textObject = null;
        
        this.create();
    }

    /**
     * 创建按钮
     */
    create() {
        // 创建按钮背景
        this.background = this.scene.add.rectangle(
            this.x,
            this.y,
            this.width,
            this.height,
            this.backgroundColor
        );
        
        // 创建按钮文本
        this.textObject = this.scene.add.text(
            this.x,
            this.y,
            this.text,
            {
                font: this.font,
                fill: this.textColor
            }
        ).setOrigin(0.5);
        
        // 添加到游戏对象列表
        this.addGameObject(this.background);
        this.addGameObject(this.textObject);
        
        // 设置交互
        if (this.interactive && this.enabled) {
            this.setupInteraction();
        }
        
        console.log(`按钮创建完成: ${this.text}`);
    }

    /**
     * 设置交互事件
     */
    setupInteraction() {
        this.background.setInteractive();
        
        // 鼠标悬停
        this.background.on('pointerover', () => {
            if (!this.enabled) return;
            
            this.isHovered = true;
            this.background.fillColor = this.hoverColor;
            this.scene.game.canvas.style.cursor = 'pointer';
            
            if (this.onHover) {
                this.onHover(this);
            }
            
            this.emit('button:hover', this);
        });
        
        // 鼠标离开
        this.background.on('pointerout', () => {
            if (!this.enabled) return;
            
            this.isHovered = false;
            this.isPressed = false;
            this.background.fillColor = this.backgroundColor;
            this.scene.game.canvas.style.cursor = 'default';
            
            if (this.onOut) {
                this.onOut(this);
            }
            
            this.emit('button:out', this);
        });
        
        // 鼠标按下
        this.background.on('pointerdown', () => {
            if (!this.enabled) return;
            
            this.isPressed = true;
            this.emit('button:down', this);
        });
        
        // 鼠标释放（点击）
        this.background.on('pointerup', () => {
            if (!this.enabled) return;
            
            if (this.isPressed && this.isHovered) {
                if (this.onClick) {
                    this.onClick(this);
                }
                
                this.emit('button:click', this);
            }
            
            this.isPressed = false;
            this.emit('button:up', this);
        });
    }

    /**
     * 设置按钮文本
     * @param {string} text - 新文本
     */
    setText(text) {
        this.text = text;
        if (this.textObject) {
            this.textObject.setText(text);
        }
        return this;
    }

    /**
     * 设置按钮启用状态
     * @param {boolean} enabled - 是否启用
     */
    setEnabled(enabled) {
        this.enabled = enabled;
        
        if (this.background) {
            if (enabled) {
                this.background.setInteractive();
                this.background.setAlpha(1);
            } else {
                this.background.disableInteractive();
                this.background.setAlpha(0.5);
                this.scene.game.canvas.style.cursor = 'default';
            }
        }
        
        return this;
    }

    /**
     * 应用样式
     * @param {Object} style - 样式配置
     */
    applyStyle(style) {
        if (style.backgroundColor !== undefined) {
            this.backgroundColor = style.backgroundColor;
            if (this.background && !this.isHovered) {
                this.background.fillColor = this.backgroundColor;
            }
        }
        
        if (style.hoverColor !== undefined) {
            this.hoverColor = style.hoverColor;
        }
        
        if (style.textColor !== undefined) {
            this.textColor = style.textColor;
            if (this.textObject) {
                this.textObject.setFill(this.textColor);
            }
        }
        
        if (style.font !== undefined) {
            this.font = style.font;
            if (this.textObject) {
                this.textObject.setFont(this.font);
            }
        }
        
        if (style.strokeColor !== undefined && this.textObject) {
            this.textObject.setStroke(style.strokeColor, style.strokeThickness || 0);
        }
    }

    /**
     * 设置位置（重写父类方法）
     */
    setPosition(x, y) {
        super.setPosition(x, y);
        
        // 确保文本居中
        if (this.textObject) {
            this.textObject.setPosition(x, y);
        }
        
        return this;
    }

    /**
     * 模拟点击
     */
    click() {
        if (this.enabled && this.onClick) {
            this.onClick(this);
            this.emit('button:click', this);
        }
    }

    /**
     * 销毁按钮
     */
    destroy() {
        // 恢复鼠标样式
        if (this.scene && this.scene.game && this.scene.game.canvas) {
            this.scene.game.canvas.style.cursor = 'default';
        }
        
        super.destroy();
    }
}
