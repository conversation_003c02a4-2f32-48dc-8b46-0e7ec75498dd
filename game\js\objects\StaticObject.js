class StaticObject {
    constructor(scene, x, y, texture, scale = 1) {
        this.scene = scene;
        this.sprite = scene.add.image(x, y, texture);

        // 设置缩放
        this.sprite.setScale(scale);

        // 设置深度，确保正确显示
        this.sprite.setDepth(1);

        // 启用物理系统
        scene.physics.add.existing(this.sprite, true); // true表示是静态物体
    }

    destroy() {
        if (this.sprite) {
            this.sprite.destroy();
        }
    }

    // 设置缩放
    setScale(scale) {
        if (this.sprite) {
            this.sprite.setScale(scale);
        }
    }
} 