# 滑动卡片选择系统技术文档

## 概述

本文档描述了游戏中升级选择界面的滑动卡片系统实现，该系统模仿《王权》游戏的滑动选择机制，提供直观的左右滑动选择体验。

## 核心设计理念

### 王权逻辑
- **纯位置判断**：基于卡片最终位置而非滑动速度
- **边拖边看**：用户可以慢慢拖拽，实时查看选项内容
- **松手触发**：只有松手时才根据位置做出选择
- **直观反馈**：左滑选左，右滑选右，距离不够回中间

### 与传统按钮的区别
- 传统：点击按钮立即触发
- 滑动：拖拽过程可预览，松手才确认

## 技术实现

### 1. UI结构

```javascript
// 创建大卡片容器
const cardWidth = this.cameras.main.width * 0.7;
const cardHeight = this.cameras.main.height * 0.5;
const centerX = this.cameras.main.width / 2;
const centerY = this.cameras.main.height / 2;

const card = this.add.rectangle(
    centerX, centerY, cardWidth, cardHeight, 0x2c3e50
).setInteractive().setScrollFactor(0).setDepth(1001);

// 卡片标题
const cardTitle = this.add.text(
    centerX, centerY - 50, '选择升级',
    { font: 'bold 32px Arial', fill: '#ffffff' }
).setOrigin(0.5).setScrollFactor(0).setDepth(1003);

// 左侧选项
const leftOption = this.add.text(
    centerX - cardWidth / 3, centerY + 80,
    options[0].name + '\n' + options[0].description,
    { font: '18px Arial', fill: '#ffffff', align: 'center' }
).setOrigin(0.5).setScrollFactor(0).setDepth(1003);

// 右侧选项
const rightOption = this.add.text(
    centerX + cardWidth / 3, centerY + 80,
    options[1].name + '\n' + options[1].description,
    { font: '18px Arial', fill: '#ffffff', align: 'center' }
).setOrigin(0.5).setScrollFactor(0).setDepth(1003);
```

### 2. 滑动逻辑核心

#### 状态变量
```javascript
let startX = 0;      // 起始X坐标
let startY = 0;      // 起始Y坐标  
let isDragging = false; // 拖拽状态
// 注意：不使用速度变量！
```

#### 事件处理

**按下事件 (pointerdown)**
```javascript
card.on('pointerdown', (pointer) => {
    startX = pointer.x;
    startY = pointer.y;
    isDragging = true;
    console.log('开始拖拽');
});
```

**移动事件 (pointermove)**
```javascript
card.on('pointermove', (pointer) => {
    if (!isDragging) return;

    const deltaX = pointer.x - startX;
    const deltaY = pointer.y - startY;

    // 卡片跟随移动
    card.x = centerX + deltaX;
    cardTitle.x = centerX + deltaX;
    leftOption.x = centerX - cardWidth / 3 + deltaX;
    rightOption.x = centerX + cardWidth / 3 + deltaX;

    // 旋转效果
    const xMulti = deltaX * 0.03;
    const yMulti = deltaY / 80;
    const rotation = xMulti * yMulti;

    card.rotation = rotation;
    cardTitle.rotation = rotation;
    leftOption.rotation = rotation;
    rightOption.rotation = rotation;
});
```

**松开事件 (pointerup) - 核心判断逻辑**
```javascript
card.on('pointerup', (pointer) => {
    if (!isDragging) return;
    isDragging = false;

    const deltaX = pointer.x - startX;

    // 王权逻辑：纯位置判断
    const threshold = this.cameras.main.width * 0.25; // 25%屏幕宽度
    const keep = Math.abs(deltaX) < threshold;

    if (keep) {
        // 距离不够，回到原位
        card.x = centerX;
        cardTitle.x = centerX;
        leftOption.x = centerX - cardWidth / 3;
        rightOption.x = centerX + cardWidth / 3;
        card.rotation = 0;
        cardTitle.rotation = 0;
        leftOption.rotation = 0;
        rightOption.rotation = 0;
    } else {
        // 距离足够，选择对应选项
        const selectedOption = deltaX < 0 ? options[0] : options[1];
        selectedOption.apply(this.player);
        
        // 清理UI和恢复游戏...
    }
});
```

### 3. 关键参数

| 参数 | 值 | 说明 |
|------|----|----|
| 卡片宽度 | 屏幕宽度 × 0.7 | 卡片占屏幕70%宽度 |
| 卡片高度 | 屏幕高度 × 0.5 | 卡片占屏幕50%高度 |
| 判断阈值 | 屏幕宽度 × 0.25 | 滑动25%屏幕宽度才触发选择 |
| 旋转系数X | 0.03 | 水平拖拽的旋转敏感度 |
| 旋转系数Y | 1/80 | 垂直拖拽的旋转敏感度 |

## 实现要点

### ✅ 必须做的
1. **删除所有速度判断**：不使用 `velocity` 或 `lastVelocityX`
2. **纯位置判断**：只基于 `deltaX` 和阈值比较
3. **实时视觉反馈**：拖拽过程中卡片跟随移动
4. **旋转效果**：增强拖拽的真实感
5. **回弹动画**：距离不够时平滑回到中心

### ❌ 绝对禁止
1. **速度计算**：不要计算或使用拖拽速度
2. **复杂判断**：不要使用 `&&` 或 `||` 组合多个条件
3. **时间因素**：不要考虑拖拽持续时间
4. **加速度**：不要使用惯性或加速度效果

### 🎯  判断逻辑
```javascript
// 正确的王权逻辑
const threshold = this.cameras.main.width * 0.25;
const keep = Math.abs(deltaX) < threshold;

if (keep) {
    // 回到中间
} else {
    // 选择选项：deltaX < 0 选左，deltaX > 0 选右
    const selectedOption = deltaX < 0 ? options[0] : options[1];
}
```

## 集成指南

### 替换现有按钮系统

**原始按钮代码模式：**
```javascript
// 创建多个独立按钮
options.forEach((option, index) => {
    const button = this.add.rectangle(x, y, width, height, color)
        .setInteractive();
    
    button.on('pointerdown', () => {
        option.apply(this.player);
        // 清理UI...
    });
});
```

**替换为滑动卡片：**
1. 删除 `forEach` 循环和独立按钮
2. 创建单个大卡片容器
3. 添加左右选项文本
4. 实现滑动事件处理

### 适配不同选项数量

**两选项（标准）：**
- 左选项：`options[0]`
- 右选项：`options[1]`
- 判断：`deltaX < 0 ? options[0] : options[1]`

**三选项（扩展）：**
- 左选项：`options[0]`
- 中选项：`options[1]` 
- 右选项：`options[2]`
- 判断：需要两个阈值区分三个区域

## 调试技巧

### 控制台输出
```javascript
console.log('拖拽中 - deltaX:', deltaX);
console.log('松开 - deltaX:', deltaX, 'threshold:', threshold, 'keep:', keep);
console.log('选择选项:', deltaX < 0 ? '左侧' : '右侧', selectedOption.name);
```

### 常见问题

**问题1：卡片不跟随移动**
- 检查 `isDragging` 状态
- 确认 `pointermove` 事件正确绑定

**问题2：选择逻辑不触发**
- 检查 `pointerup` 事件中的 `isDragging` 判断
- 确认阈值计算正确

**问题3：回弹效果异常**
- 检查所有UI元素的位置重置
- 确认旋转角度重置为0

## 性能优化

1. **事件解绑**：UI销毁时自动解绑事件
2. **深度管理**：合理设置UI元素深度避免重叠
3. **内存清理**：选择后及时销毁所有UI元素

## 总结

滑动卡片选择系统的核心是**纯位置判断**的王权逻辑，通过简单的阈值比较实现直观的左右选择体验。关键是删除所有速度相关的复杂判断，专注于位置和视觉反馈的实现。
