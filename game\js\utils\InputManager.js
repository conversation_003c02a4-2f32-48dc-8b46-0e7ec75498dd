/**
 * 输入管理器 - 简化版
 * 处理键盘和触屏输入
 */
class InputManager {
    /**
     * 创建输入管理器
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {Player} player - 玩家实例
     */
    constructor(scene, player) {
        this.scene = scene;
        this.player = player;
        
        // 创建键盘输入
        this.cursors = this.scene.input.keyboard.createCursorKeys();
        this.wasd = {
            up: this.scene.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.W),
            down: this.scene.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.S),
            left: this.scene.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.A),
            right: this.scene.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.D)
        };
        
        // 移动输入值
        this.moveInput = { x: 0, y: 0 };
        
        console.log('输入管理器已初始化');
    }
    
    /**
     * 更新输入状态
     */
    update() {
        if (!this.player) return;
        
        // 重置移动输入
        this.moveInput.x = 0;
        this.moveInput.y = 0;
        
        // 处理键盘输入
        if (this.cursors.left.isDown || this.wasd.left.isDown) {
            this.moveInput.x = -1;
        } else if (this.cursors.right.isDown || this.wasd.right.isDown) {
            this.moveInput.x = 1;
        }
        
        if (this.cursors.up.isDown || this.wasd.up.isDown) {
            this.moveInput.y = -1;
        } else if (this.cursors.down.isDown || this.wasd.down.isDown) {
            this.moveInput.y = 1;
        }
        
        // 移动玩家
        this.player.move(this.moveInput);
    }
}
