/**
 * 主游戏场景
 */
class GameScene extends Phaser.Scene {
    constructor() {
        super({ key: 'GameScene' });

        // 使用DepthManager管理渲染层级
        this.DEPTH_LAYERS = DepthManager.LAYERS;

        // UI管理器
        this.uiManager = null;

        // 帧计数器，用于调试
        this.frameCounter = 0;

        // 游戏时间相关
        this.gameTime = 0;       // 游戏进行时间（毫秒）
        this.startTime = 0;      // 游戏开始时间戳
        this.isPaused = false;   // 游戏是否暂停
        this.lastMinute = -1;    // 上一次触发分钟事件的分钟数
        this.gameStarted = false; // 游戏是否已开始

        // 物品相关
        this.items = [];         // 存储所有物品

        // 收集计数器
        this.coinCount = 0;      // 钱币计数
        this.expCount = 0;       // 经验值计数
    }

    preload() {
        // 所有资源已在StartScene中预加载
        console.log('GameScene: 使用StartScene预加载的资源');

        // 预加载敌人图片
        // 注意：实际的敌人图片会在各自的类中加载

    }

    create() {
        // 重置所有状态变量
        this.gameTime = 0;
        this.startTime = 0;
        this.lastMinute = -1;
        this.gameStarted = false;
        this.isPaused = false;
        this.frameCounter = 0;
        this.coinCount = 0;
        this.expCount = 0;
        this.items = [];
        this.isBossDefeated = false;

        // 开始新游戏会话
        if (window.gameData) {
            try {
                window.gameData.startNewGame();
            } catch (error) {
                console.error('启动新游戏会话失败:', error);
            }
        }

        // 清理定时器
        if (this.spawnTimer) {
            this.spawnTimer.remove();
            this.spawnTimer = null;
        }

        // 清空敌人数组
        if (this.activeEnemies) {
            this.activeEnemies.forEach(enemy => {
                if (enemy && enemy.destroy) {
                    enemy.destroy();
                }
            });
        }
        this.activeEnemies = [];

        // 重置怪物生成配置
        this.currentMinuteConfig = null;

        const width = this.cameras.main.width;
        const height = this.cameras.main.height;

        // 创建UI管理器
        this.uiManager = new UIManager(this);
        this.uiManager.getTheme().setTheme('game');

        // 创建升级模态框
        this.upgradeModal = new UpgradeModal(this);

        // 升级状态管理 - 防止重复触发升级选项
        this.isShowingUpgrade = false;

        // 移除世界边界，允许无限移动
        this.physics.world.setBounds(-Infinity, -Infinity, Infinity, Infinity);

        // 初始化深度管理器
        DepthManager.initScene(this);

        // 创建九宫格地图系统
        this.createNineGridMap();

        // 创建敌人组
        this.enemiesGroup = this.physics.add.group();

        // 创建物品组
        this.itemsGroup = this.physics.add.group();

        // 创建玩家角色
        this.player = new Player(
            this,
            width / 2,  // 水平居中
            height / 2  // 垂直居中
        );

        // 确保玩家不受世界边界限制
        this.player.setWorldCollision(false);

        // 设置物品收集事件监听
        this.events.on('coinCollected', this.onCoinCollected, this);
        this.events.on('experienceCollected', this.onExperienceCollected, this);

        // 创建经验条UI
        this.createExpBar();

        // 初始化资源显示（在UI创建之后）
        this.updateResourceDisplay();

        // 设置镜头跟随玩家
        this.cameras.main.startFollow(this.player.sprite, true, 0.1, 0.1);

        // 创建输入管理器
        this.inputManager = new InputManager(this, this.player);

        // 创建虚拟摇杆
        this.createVirtualJoystick();

        // 设置玩家与敌人之间的碰撞
        this.physics.add.overlap(
            this.player.sprite,
            this.enemiesGroup,
            (playerSprite, enemySprite) => {
                // 计算从玩家到敌人的角度
                const angle = Phaser.Math.Angle.Between(
                    playerSprite.x, playerSprite.y,
                    enemySprite.x, enemySprite.y
                );
                // 应用推力
                const force = 200;
                enemySprite.body.velocity.x = Math.cos(angle) * force;
                enemySprite.body.velocity.y = Math.sin(angle) * force;
            }
        );

        // 设置敌人之间的碰撞，但排除E000a和E000b（飞行单位）
        this.physics.add.collider(this.enemiesGroup, this.enemiesGroup, null, this.checkEnemyCollision, this);

        // 初始化怪物生成系统
        this.initEnemySpawner();

        // 创建宠物动画
        this.createPetAnimations();

        // 创建宠物管理器
        this.petManager = new PetManager(this, this.player);
        this.petManager.createDefaultPets();

        // 创建蝙蝠群管理器
        this.batSwarmManager = new BatSwarmManager(this);

        // 应用工作台buff
        this.healthBuffApplied = false;
        this.speedBuffApplied = false;
        this.attackBuffApplied = false;
        this.rangeBuffApplied = false;
        this.playerBaseMaxHealth = this.player.maxHealth;
        this.playerBaseMoveSpeed = this.player.moveSpeed;
        this.playerBaseAttackPower = this.player.attackPower;
        this.applyWorkstationBuffs();

        // 创建开始游戏按钮
        this.uiManager.createComponent('button', 'startGameButton', {
            x: width / 2,
            y: height / 2,
            width: 200,
            height: 60,
            text: '开始游戏',
            backgroundColor: 0x00aa00,
            hoverColor: 0x008800,
            font: 'bold 24px Arial',
            scrollFactor: 0,
            depth: this.DEPTH_LAYERS.UI + 10,
            onClick: () => {
                this.startGame();
                this.uiManager.destroyComponent('startGameButton');
            }
        });

        // 创建时间显示文本
        this.uiManager.createComponent('text', 'timeText', {
            x: width / 2,
            y: 32,
            text: '时间: 00:00',
            font: '18px Arial',
            color: '#ffffff',
            strokeColor: '#000000',
            strokeThickness: 3,
            originX: 0.5,
            originY: 0.5,
            scrollFactor: 0,
            depth: this.DEPTH_LAYERS.UI
        });

        // 创建返回按钮
        this.uiManager.createComponent('button', 'backButton', {
            x: width / 2,
            y: height * 0.9,
            width: 200,
            height: 60,
            text: '返回主菜单',
            font: 'bold 20px Arial',
            backgroundColor: 0x3498db,
            hoverColor: 0x2980b9,
            scrollFactor: 0,
            depth: this.DEPTH_LAYERS.UI,
            onClick: () => {
                this.scene.start('MainMenuScene');
            }
        });


        // 创建暂停/继续按钮
        this.uiManager.createComponent('button', 'pauseButton', {
            x: width - 68,
            y: 40,
            width: 60,
            height: 40,
            text: '暂停',
            font: '16px Arial',
            backgroundColor: 0x3498db,
            hoverColor: 0x2980b9,
            scrollFactor: 0,
            depth: this.DEPTH_LAYERS.UI,
            onClick: () => {
                this.togglePause();
            }
        });

        // ===== 临时功能：时间快进按钮（上线前删除） =====
        this.uiManager.createComponent('button', 'skipButton', {
            x: width - 180,
            y: 40,
            width: 120,
            height: 40,
            text: '时间+1分钟',
            font: '16px Arial',
            backgroundColor: 0xff5500,
            hoverColor: 0xcc4400,
            scrollFactor: 0,
            depth: this.DEPTH_LAYERS.UI,
            onClick: () => {
                this.skipToNextMinute();
            }
        });

        // 创建调试信息显示
        this.uiManager.createComponent('text', 'debugText', {
            x: width - 180,
            y: 70,
            text: '当前分钟: 0, 诅咒等级: 0%',
            font: '12px Arial',
            color: '#ffffff',
            originX: 0.5,
            originY: 0.5,
            scrollFactor: 0,
            depth: this.DEPTH_LAYERS.UI
        });
        // ===== 临时功能结束 =====
    }

    /**
     * 开始游戏
     */
    startGame() {
        // 初始化计时系统
        this.gameTime = 0;
        this.startTime = this.time.now;
        this.isPaused = false;
        this.gameStarted = true;

        console.log('游戏开始！');
    }

    /**
     * 更新游戏状态
     * @param {number} time - 当前时间
     */
    update(time) {
        // 更新帧计数器
        this.frameCounter++;

        // 计算deltaTime
        if (!this.lastUpdateTime) {
            this.lastUpdateTime = time;
        }
        const deltaTime = (time - this.lastUpdateTime) / 1000; // 转换为秒
        this.lastUpdateTime = time;

        // 如果游戏暂停或未开始，不更新游戏逻辑
        if (this.isPaused || !this.gameStarted) return;

        // 更新游戏时间
        this.updateGameTime(time);

        // 更新输入管理器
        if (this.inputManager) {
            this.inputManager.update();
        }

        // 更新虚拟摇杆
        this.updateVirtualJoystick();

        // 更新玩家
        if (this.player) {
            this.player.update(time);
        }

        // 更新九宫格地图
        if (this.player && this.player.sprite) {
            this.updateNineGridMap(this.player.sprite.x, this.player.sprite.y);
        }

        // 更新敌人
        this.updateEnemies();

        // 更新蝙蝠群
        if (this.batSwarmManager) {
            this.batSwarmManager.update(deltaTime);
        }

        // 更新物品
        this.updateItems();

        // 每秒检查一次工作台buff状态
        if (!this.lastBuffCheckTime || time - this.lastBuffCheckTime > 1000) {
            this.applyWorkstationBuffs();
            this.lastBuffCheckTime = time;
        }
    }

    /**
     * 更新所有物品
     */
    updateItems() {
        // 如果玩家不存在，不更新物品
        if (!this.player || !this.player.sprite) return;

        // 清理已销毁的物品
        this.items = this.items.filter(item => item && item.sprite && item.sprite.active);

        // 使用玩家的物品收集方法处理所有物品
        this.player.updateItemCollection(this.items);

        // 更新物品深度
        for (const item of this.items) {
            if (item && item.sprite) {
                // 根据物品类型设置深度
                let itemType = 'Item'; // 默认类型

                // 根据物品实例类型确定深度类型
                if (item instanceof ExperienceGem) {
                    itemType = 'ExperienceGem';
                } else if (item instanceof Coin) {
                    itemType = 'Coin';
                } else if (item instanceof Diamond) {
                    itemType = 'Diamond';
                } else if (item instanceof HealthPotion) {
                    itemType = 'HealthPotion';
                }

                // 设置自定义类型
                item.sprite.customType = itemType;

                // 更新深度，启用Y轴排序
                DepthManager.updateDepth(item.sprite, itemType, true);
            }
        }
    }

    /**
     * 钱币收集事件处理
     * @param {number} value - 钱币价值
     */
    onCoinCollected(value) {
        // 增加钱币计数
        this.coinCount += value;

        // 更新游戏数据中的当前局钱币
        if (window.gameData) {
            try {
                window.gameData.updateCurrentGameCoins(this.coinCount);
            } catch (error) {
                console.error('更新当前局钱币失败:', error);
            }
        }

        // 更新UI显示
        this.updateResourceDisplay();
    }

    /**
     * 经验宝石收集事件处理
     * @param {number} value - 经验值
     */
    onExperienceCollected(value) {
        // 增加经验值计数
        this.expCount += value;

        // 增加玩家经验并检查升级
        if (this.player) {
            const levelUp = this.player.addExperience(value);

            // 如果升级且当前没有显示升级选项，显示提示并显示升级选项
            if (levelUp && !this.isShowingUpgrade) {
                this.isShowingUpgrade = true;
                this.showLevelUpMessage();
                this.upgradeModal.show();
            }
        }

        // 更新UI显示
        this.updateResourceDisplay();
    }

    /**
     * 显示升级提示
     */
    showLevelUpMessage() {
        // 创建升级提示文本
        const levelUpText = this.add.text(
            this.cameras.main.width / 2,
            this.cameras.main.height / 2 - 50,
            `升级! 当前等级: ${this.player.level}`,
            {
                font: 'bold 32px Arial',
                fill: '#ffff00',
                stroke: '#000000',
                strokeThickness: 6
            }
        ).setOrigin(0.5).setScrollFactor(0);

        // 2秒后移除文本
        this.time.delayedCall(2000, () => {
            levelUpText.destroy();
        });
    }

    /**
     * 升级选项关闭后的回调
     */
    onUpgradeModalClosed() {
        // 重置升级状态锁定
        this.isShowingUpgrade = false;
    }

    /**
     * 创建经验条UI
     */
    createExpBar() {
        const width = this.cameras.main.width;

        // 底板参数
        const barWidth = width * 0.9;
        const barHeight = 10;
        const barX = width / 2;
        const barY = 95;

        // 创建经验条进度条组件
        this.uiManager.createComponent('progressbar', 'expBar', {
            x: barX,
            y: barY,
            width: barWidth,
            height: barHeight,
            value: 0,
            maxValue: 100,
            backgroundColor: 0x333333,
            fillColor: 0x00ff00,
            showText: false,
            scrollFactor: 0,
            depth: this.DEPTH_LAYERS.UI
        });

        // 创建经验文本
        this.uiManager.createComponent('text', 'expBarText', {
            x: barX,
            y: barY + barHeight - 5,
            text: "0/5",
            font: '18px Arial',
            color: '#ffffff',
            strokeColor: '#000000',
            strokeThickness: 2,
            originX: 0.5,
            originY: 0,
            scrollFactor: 0,
            depth: this.DEPTH_LAYERS.UI + 0.2
        });

        // 更新经验条
        this.updateExpBar();
    }

    /**
     * 更新经验条
     */
    updateExpBar() {
        if (!this.player) return;

        const level = this.player.level;
        const exp = this.player.experience;

        // 计算当前等级和下一等级所需经验
        // 使用Player类的静态方法
        const currentLevelExp = level > 0 ? Player.getRequiredExperience(level) : 0;
        const nextLevelExp = Player.getRequiredExperience(level + 1);

        // 计算进度
        const expInCurrentLevel = exp - currentLevelExp;
        const expNeededForNextLevel = nextLevelExp - currentLevelExp;
        const progress = expNeededForNextLevel > 0 ? expInCurrentLevel / expNeededForNextLevel : 0;

        // 更新经验条进度
        const expBar = this.uiManager.getComponent('expBar');
        if (expBar) {
            expBar.setValue(progress * 100, false);
        }

        // 更新经验文本
        const expBarText = this.uiManager.getComponent('expBarText');
        if (expBarText) {
            expBarText.setText(exp + '/' + nextLevelExp + ' (等级 ' + level + ')');
        }
    }

    /**
     * 更新资源显示
     */
    updateResourceDisplay() {
        // 如果钱币文本组件不存在，创建它
        if (!this.uiManager.getComponent('coinText')) {
            this.uiManager.createComponent('text', 'coinText', {
                x: 36,
                y: 22,
                text: '钱币: ' + this.coinCount,
                font: '18px Arial',
                color: '#ffffff',
                strokeColor: '#000000',
                strokeThickness: 3,
                scrollFactor: 0,
                depth: this.DEPTH_LAYERS.UI
            });
        }

        // 更新钱币文本内容
        const coinText = this.uiManager.getComponent('coinText');
        if (coinText) {
            coinText.setText('钱币: ' + this.coinCount);
        }

        // 更新经验条
        this.updateExpBar();
    }

    /**
     * 创建九宫格地图系统
     */
    createNineGridMap() {
        // 使用floor_plain纹理
        this.tileTexture = 'background'; // 使用已加载的背景纹理

        // 缩放比例
        this.tileScale = 3;

        // 深度设置，确保地图在玩家下方
        this.tileDepth = -10;

        // 获取地图图片尺寸
        const texture = this.textures.get(this.tileTexture);
        if (texture) {
            const sourceImage = texture.getSourceImage();
            if (sourceImage) {
                // 计算地图块尺寸（考虑缩放）
                this.tileWidth = sourceImage.width * this.tileScale;
                this.tileHeight = sourceImage.height * this.tileScale;
            }
        }

        // 如果无法获取纹理尺寸，使用默认值
        if (!this.tileWidth || !this.tileHeight) {
            this.tileWidth = 256 * this.tileScale;
            this.tileHeight = 256 * this.tileScale;
        }

        // 存储已创建的地图块
        this.mapTiles = {};

        // 当前玩家所在的网格坐标
        this.currentGridX = null;
        this.currentGridY = null;
    }

    /**
     * 更新九宫格地图
     * @param {number} playerX - 玩家X坐标
     * @param {number} playerY - 玩家Y坐标
     */
    updateNineGridMap(playerX, playerY) {
        // 安全检查
        if (playerX === undefined || playerY === undefined ||
            isNaN(playerX) || isNaN(playerY)) {
            return;
        }

        // 计算玩家所在的网格坐标
        const gridX = Math.floor(playerX / this.tileWidth);
        const gridY = Math.floor(playerY / this.tileHeight);

        // 如果玩家仍在同一网格，不更新地图
        if (gridX === this.currentGridX && gridY === this.currentGridY) {
            return;
        }

        // 更新当前网格坐标
        this.currentGridX = gridX;
        this.currentGridY = gridY;

        // 记录需要保留的地图块
        const neededTiles = {};

        // 创建可见范围内的地图块（3x3网格）
        for (let y = gridY - 1; y <= gridY + 1; y++) {
            for (let x = gridX - 1; x <= gridX + 1; x++) {
                const key = `${x},${y}`;
                neededTiles[key] = true;

                // 如果地图块不存在，创建它
                if (!this.mapTiles[key]) {
                    this.createMapTile(x, y);
                }
            }
        }

        // 移除不再可见的地图块
        for (const key in this.mapTiles) {
            if (!neededTiles[key]) {
                this.removeMapTile(key);
            }
        }
    }

    /**
     * 创建单个地图块
     * @param {number} gridX - 网格X坐标
     * @param {number} gridY - 网格Y坐标
     */
    createMapTile(gridX, gridY) {
        // 计算世界坐标
        const worldX = gridX * this.tileWidth;
        const worldY = gridY * this.tileHeight;
        const key = `${gridX},${gridY}`;

        // 创建地图块精灵
        const tile = this.add.sprite(worldX, worldY, this.tileTexture);

        // 设置精灵属性
        tile.setOrigin(0, 0); // 设置原点为左上角
        tile.setScale(this.tileScale);

        // 使用DepthManager设置深度
        tile.customType = 'Tilemap'; // 设置自定义类型
        DepthManager.updateDepth(tile, 'Tilemap', false);

        // 存储地图块
        this.mapTiles[key] = tile;
    }

    /**
     * 移除地图块
     * @param {string} key - 地图块键值
     */
    removeMapTile(key) {
        if (this.mapTiles[key]) {
            this.mapTiles[key].destroy();
            delete this.mapTiles[key];
        }
    }

    /**
     * 更新游戏时间
     * @param {number} currentTime - 当前时间戳
     */
    updateGameTime(currentTime) {
        // 计算游戏进行时间（毫秒）
        this.gameTime = currentTime - this.startTime;

        // 更新时间显示
        const timeText = this.uiManager.getComponent('timeText');
        if (timeText) {
            const seconds = Math.floor(this.gameTime / 1000) % 60;
            const minutes = Math.floor(this.gameTime / 60000);

            timeText.setText('时间: ' + minutes.toString().padStart(2, '0') + ':' + seconds.toString().padStart(2, '0'));

            // 检查是否过了一分钟
            if (minutes > this.lastMinute) {
                this.lastMinute = minutes;
                this.onMinutePassed(minutes);
            }
        }
    }

    /**
     * 每分钟触发一次的事件
     * @param {number} minute - 当前分钟数
     */
    onMinutePassed(minute) {
        console.log(`游戏时间: ${minute}分钟`);

        // 检查蝙蝠群突袭
        this.checkBatSwarmEvent(minute);

        // 更新刷新配置
        this.updateSpawnConfig();

        // 检查游戏结束条件
        this.checkGameEnd();
    }

    /**
     * 更新刷新配置
     */
    updateSpawnConfig() {
        // 获取当前分钟
        const currentMinute = Math.min(Math.floor(this.gameTime / 60000), 15);

        // 获取当前分钟的配置
        this.currentMinuteConfig = this.minuteEnemyConfig[currentMinute] || this.minuteEnemyConfig[0];

        // 设置当前的刷新间隔，不再考虑诅咒等级
        this.currentRefreshInterval = this.currentMinuteConfig.baseInterval;

        // 计算最大怪物数量（最小数量的140%）
        this.maxEnemyCount = Math.ceil(this.currentMinuteConfig.minCount * 1.4);

        // 计算每次补充数量（最小数量的20%）
        this.spawnBatchSize = Math.max(1, Math.floor(this.currentMinuteConfig.minCount * 0.2));

        console.log(`当前分钟: ${currentMinute}, 怪物类型: ${this.currentMinuteConfig.types.join(',')}, 刷新间隔: ${this.currentRefreshInterval.toFixed(2)}秒, 最小数量: ${this.currentMinuteConfig.minCount}, 最大数量: ${this.maxEnemyCount}, 每批数量: ${this.spawnBatchSize}`);

        // 如果是Boss分钟，清除所有现有怪物
        if (this.currentMinuteConfig.isBoss) {
            console.log(`第${currentMinute}分钟：Boss出现，清除所有其他怪物`);
            this.clearAllEnemies();
        }

        // 重置刷新计时器
        if (this.spawnTimer) {
            this.spawnTimer.remove();
        }

        // 启动刷新循环
        this.startSpawnLoop();
    }

    /**
     * 启动刷新循环
     */
    startSpawnLoop() {
        // 立即执行一次刷新
        this.spawnEnemies();

        // 设置定时刷新，使用考虑诅咒等级后的刷新间隔
        this.spawnTimer = this.time.addEvent({
            delay: this.currentRefreshInterval * 1000,
            callback: this.spawnEnemies,
            callbackScope: this,
            loop: true
        });
    }

    /**
     * 刷新怪物
     * 简化的怪物生成逻辑：
     * 1. 当当前怪物数量 < 最小怪物数量时，直接补充到最小值
     * 2. 当当前怪物数量 >= 最小怪物数量但 < 最大怪物数量时，每次只生成最小数量的10%
     * 3. 当当前怪物数量 >= 最大怪物数量时，不再生成
     */
    spawnEnemies() {
        // 如果游戏暂停，不生成怪物
        if (this.isPaused) return;

        // 获取当前怪物数量
        const currentEnemyCount = this.activeEnemies.length;

        // 获取最小怪物数量
        const minEnemyCount = this.currentMinuteConfig.minCount;

        // 情况1: 当前怪物数量 < 最小怪物数量时，直接补充到最小值
        if (currentEnemyCount < minEnemyCount) {
            const spawnCount = minEnemyCount - currentEnemyCount;
            console.log(`怪物数量(${currentEnemyCount})低于最小值(${minEnemyCount})，直接补充到最小值`);

            // 检查是否是第6分钟的围攻
            const currentMinute = Math.floor(this.gameTime / 60000);
            if (currentMinute === 6 && this.currentMinuteConfig.types.includes('E006')) {
                // 第6分钟使用围攻生成
                this.spawnSurroundingEnemies('E006', spawnCount);
            } else {
                // 普通生成逻辑
                let remainingToSpawn = spawnCount;

                while (remainingToSpawn > 0 && this.activeEnemies.length < this.maxEnemyCount) {
                    // 随机选择一种怪物类型
                    const enemyType = this.currentMinuteConfig.types[Math.floor(Math.random() * this.currentMinuteConfig.types.length)];

                    // 随机决定是生成组还是单个
                    if (remainingToSpawn >= this.spawnParams.groupSizeMin && Math.random() < this.spawnParams.groupSpawnChance) {
                        // 确定组大小，不超过剩余数量和最大组大小
                        const maxGroupSize = Math.min(
                            remainingToSpawn,
                            this.spawnParams.groupSizeMax,
                            this.maxEnemyCount - this.activeEnemies.length // 确保不超过最大数量
                        );

                        if (maxGroupSize < this.spawnParams.groupSizeMin) {
                            // 如果不足以形成最小组，就生成单个
                            this.spawnSingleEnemy(enemyType);
                            remainingToSpawn--;
                        } else {
                            // 生成一组怪物
                            const groupSize = this.spawnParams.groupSizeMin +
                                Math.floor(Math.random() * (maxGroupSize - this.spawnParams.groupSizeMin + 1));

                            this.spawnEnemyGroup(enemyType, groupSize);
                            remainingToSpawn -= groupSize;
                        }
                    } else {
                        // 生成单个怪物
                        this.spawnSingleEnemy(enemyType);
                        remainingToSpawn--;
                    }
                }
            }
        }
        // 情况2: 当前怪物数量 >= 最小怪物数量但 < 最大怪物数量时，每次只生成最小数量的10%
        else if (currentEnemyCount < this.maxEnemyCount) {
            console.log(`怪物数量(${currentEnemyCount})>=最小值(${minEnemyCount})但<最大值(${this.maxEnemyCount})，生成${this.spawnBatchSize}个怪物`);

            // 计算本次可以生成的最大数量
            const maxToSpawn = Math.min(this.spawnBatchSize, this.maxEnemyCount - currentEnemyCount);

            if (maxToSpawn <= 0) return;

            // 随机决定是生成组还是单个
            if (maxToSpawn >= this.spawnParams.groupSizeMin && Math.random() < this.spawnParams.groupSpawnChance) {
                // 确定组大小
                const groupSize = Math.min(
                    maxToSpawn,
                    this.spawnParams.groupSizeMin +
                    Math.floor(Math.random() * (this.spawnParams.groupSizeMax - this.spawnParams.groupSizeMin + 1))
                );

                // 随机选择一种怪物类型
                const enemyType = this.currentMinuteConfig.types[Math.floor(Math.random() * this.currentMinuteConfig.types.length)];

                // 生成一组怪物
                this.spawnEnemyGroup(enemyType, groupSize);
            } else {
                // 生成单个怪物，随机选择类型
                for (let i = 0; i < maxToSpawn; i++) {
                    const enemyType = this.currentMinuteConfig.types[Math.floor(Math.random() * this.currentMinuteConfig.types.length)];
                    this.spawnSingleEnemy(enemyType);
                }
            }
        }
        // 情况3: 当前怪物数量 >= 最大怪物数量时，不再刷新
        else {
            console.log(`怪物数量(${currentEnemyCount})已达最大值(${this.maxEnemyCount})，不生成新怪物`);
        }
    }

    /**
     * 检查蝙蝠群突袭事件
     * @param {number} minute - 当前分钟数
     */
    checkBatSwarmEvent(minute) {
        // 蝙蝠群突袭配置
        const batSwarmConfig = {
            2: 3,  // 2分钟：3组蝙蝠群
            4: 3,  // 4分钟：3组蝙蝠群
            7: 5,  // 7分钟：5组蝙蝠群
            9: 5,  // 9分钟：5组蝙蝠群
            12: 6, // 12分钟：6组幽灵群
            14: 6  // 14分钟：6组幽灵群
        };

        if (batSwarmConfig[minute]) {
            const groupCount = batSwarmConfig[minute];
            console.log(`第${minute}分钟：触发蝙蝠群突袭 - ${groupCount}组`);

            if (this.batSwarmManager) {
                this.batSwarmManager.spawnBatSwarm(groupCount);
            } else {
                console.warn('蝙蝠群管理器未初始化');
            }
        }
    }

    /**
     * 检查游戏结束条件
     */
    checkGameEnd() {
        // 获取当前分钟
        const currentMinute = Math.floor(this.gameTime / 60000);

        // 如果达到15分钟且Boss已被击败，游戏胜利
        if (currentMinute >= 15 && this.isBossDefeated) {
            console.log('游戏胜利！Boss已被击败！');
            // 这里可以添加胜利画面或其他效果
        }
    }

    /**
     * 初始化怪物生成系统
     */
    initEnemySpawner() {
        // 设置怪物生成参数
        this.spawnParams = {
            // 生成区域与屏幕的额外距离（像素）
            spawnMargin: 50,
            // 方向偏好概率（有多大概率从同一方向生成）
            directionalSpawnChance: 0.7,
            // 每组怪物的数量范围
            groupSizeMin: 2,
            groupSizeMax: 5,
            // 组生成概率
            groupSpawnChance: 0.3,
            // 锚点周围的随机偏移范围（像素）
            anchorOffsetRange: 60,
            // 主轴随机偏移范围（像素）
            mainAxisOffsetRange: 100,
            // 交叉轴随机偏移范围（像素）
            crossAxisOffsetRange: 200,
            // 怪物最大存在距离（超过此距离将被销毁）
            maxEnemyDistance: 0 // 将在updateEnemies中计算为生成距离的1.5倍
        };

        // 每分钟怪物配置（添加最小怪物数量(Smin)和基础刷新间隔(Tsi)）
        this.minuteEnemyConfig = {
            0: { types: ['E000a', 'E000b'], minCount: 20, baseInterval: 0.5 },
            1: { types: ['E001'], minCount: 25, baseInterval: 0.47 },
            2: { types: ['E002'], minCount: 30, baseInterval: 0.44 },
            3: { types: ['E003'], minCount: 35, baseInterval: 0.41 },
            4: { types: ['E004'], minCount: 40, baseInterval: 0.38 },
            5: { types: ['E002', 'E003', 'E004'], minCount: 50, baseInterval: 0.35, isSpecial: true }, // 特殊分钟
            6: { types: ['E006'], minCount: 100, baseInterval: 0.32 },
            7: { types: ['E007'], minCount: 70, baseInterval: 0.29 },
            8: { types: ['E008'], minCount: 80, baseInterval: 0.26 }, // 较慢的刷新间隔
            9: { types: ['E009'], minCount: 90, baseInterval: 0.23 },
            10: { types: ['E007', 'E008', 'E009'], minCount: 100, baseInterval: 0.20, isSpecial: true }, // 特殊分钟
            11: { types: ['E011'], minCount: 110, baseInterval: 0.17 },
            12: { types: ['E012'], minCount: 120, baseInterval: 0.14 },
            13: { types: ['E013'], minCount: 130, baseInterval: 0.11 },
            14: { types: ['E011', 'E012', 'E013'], minCount: 140, baseInterval: 0.08, isSpecial: true }, // 特殊分钟
            15: { types: ['E015'], minCount: 1, baseInterval: 30.0, isBoss: true } // Boss分钟
        };

        // 存储所有活跃的敌人
        this.activeEnemies = [];

        // 当前刷新计时器
        this.spawnTimer = null;

        // 当前分钟的配置
        this.currentMinuteConfig = null;

        // 是否已击败Boss
        this.isBossDefeated = false;

        // 初始化刷新系统
        this.updateSpawnConfig();
    }

    /**
     * 清除所有现有怪物
     */
    clearAllEnemies() {
        // 遍历所有活跃敌人并销毁
        for (let i = this.activeEnemies.length - 1; i >= 0; i--) {
            const enemy = this.activeEnemies[i];
            if (enemy && enemy.sprite) {
                enemy.destroy();
            }
        }

        // 清空活跃敌人数组
        this.activeEnemies = [];

        console.log('已清除所有怪物');
    }

    /**
     * 获取怪物生成位置（使用极坐标系统）
     * @returns {Object} 包含x和y坐标的对象
     */
    getSpawnPositionOnSide() {
        // 获取玩家位置
        const playerX = this.player.sprite.x;
        const playerY = this.player.sprite.y;

        // 计算屏幕对角线长度
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;
        const screenDiagonal = Math.sqrt(width * width + height * height);

        // 基础生成距离（屏幕对角线的一半加上边距）
        const minDistance = screenDiagonal / 2 + this.spawnParams.spawnMargin;
        const maxDistance = minDistance + 200; // 最大距离比最小距离多xxx（数值）像素

        // 决定是否生成在较近位置（20%概率）
        const spawnNearby = Math.random() < 0.2;

        let distance;
        if (spawnNearby) {
            // 较近位置：屏幕边缘附近
            distance = minDistance;
            console.log("在屏幕边缘附近生成怪物");
        } else {
            // 较远位置：使用完整的随机范围
            distance = minDistance + Math.random() * (maxDistance - minDistance);
        }

        // 完全随机角度（0-359度）
        const angle = Math.random() * Math.PI * 2;

        // 使用极坐标计算生成位置
        const spawnX = playerX + Math.cos(angle) * distance;
        const spawnY = playerY + Math.sin(angle) * distance;

        return { x: spawnX, y: spawnY };
    }

    /**
     * 在指定位置生成单个怪物
     * @param {number} x - 生成位置X坐标
     * @param {number} y - 生成位置Y坐标
     * @param {string} enemyType - 怪物类型
     */
    spawnSingleEnemyAt(x, y, enemyType = 'E000a') {
        // 创建怪物实例
        let enemy;

        // 根据类型创建不同的怪物
        switch (enemyType) {
            case 'E000a':
                enemy = new E000a(this, x, y);
                break;
            case 'E000b':
                if (typeof E000b !== 'undefined') {
                    enemy = new E000b(this, x, y);
                } else {
                    enemy = new E000a(this, x, y);
                    console.warn(`怪物类型 ${enemyType} 未实现，使用默认类型 E000a`);
                }
                break;
            case 'E001':
                if (typeof E001 !== 'undefined') {
                    enemy = new E001(this, x, y);
                } else {
                    enemy = new E000a(this, x, y);
                    console.warn(`怪物类型 ${enemyType} 未实现，使用默认类型 E000a`);
                }
                break;
            case 'E002':
                if (typeof E002 !== 'undefined') {
                    enemy = new E002(this, x, y);
                } else {
                    enemy = new E000a(this, x, y);
                    console.warn(`怪物类型 ${enemyType} 未实现，使用默认类型 E000a`);
                }
                break;
            case 'E003':
                if (typeof E003 !== 'undefined') {
                    enemy = new E003(this, x, y);
                } else {
                    enemy = new E000a(this, x, y);
                    console.warn(`怪物类型 ${enemyType} 未实现，使用默认类型 E000a`);
                }
                break;
            case 'E004':
                if (typeof E004 !== 'undefined') {
                    enemy = new E004(this, x, y);
                } else {
                    enemy = new E000a(this, x, y);
                    console.warn(`怪物类型 ${enemyType} 未实现，使用默认类型 E000a`);
                }
                break;
            case 'E005':
                enemy = new E000a(this, x, y);
                console.warn(`怪物类型 ${enemyType} 未实现，使用默认类型 E000a`);
                break;
            case 'E006':
                if (typeof E006 !== 'undefined') {
                    enemy = new E006(this, x, y);
                } else {
                    enemy = new E000a(this, x, y);
                    console.warn(`怪物类型 ${enemyType} 未实现，使用默认类型 E000a`);
                }
                break;
            case 'E007':
                if (typeof E007 !== 'undefined') {
                    enemy = new E007(this, x, y);
                } else {
                    enemy = new E000a(this, x, y);
                    console.warn(`怪物类型 ${enemyType} 未实现，使用默认类型 E000a`);
                }
                break;
            case 'E008':
                if (typeof E008 !== 'undefined') {
                    enemy = new E008(this, x, y);
                } else {
                    enemy = new E000a(this, x, y);
                    console.warn(`怪物类型 ${enemyType} 未实现，使用默认类型 E000a`);
                }
                break;
            case 'E009':
                if (typeof E009 !== 'undefined') {
                    enemy = new E009(this, x, y);
                } else {
                    enemy = new E000a(this, x, y);
                    console.warn(`怪物类型 ${enemyType} 未实现，使用默认类型 E000a`);
                }
                break;
            case 'E010':
                enemy = new E000a(this, x, y);
                console.warn(`怪物类型 ${enemyType} 未实现，使用默认类型 E000a`);
                break;
            case 'E011':
                if (typeof E011 !== 'undefined') {
                    enemy = new E011(this, x, y);
                } else {
                    enemy = new E000a(this, x, y);
                    console.warn(`怪物类型 ${enemyType} 未实现，使用默认类型 E000a`);
                }
                break;
            case 'E012':
                if (typeof E012 !== 'undefined') {
                    enemy = new E012(this, x, y);
                } else {
                    enemy = new E000a(this, x, y);
                    console.warn(`怪物类型 ${enemyType} 未实现，使用默认类型 E000a`);
                }
                break;
            case 'E013':
                if (typeof E013 !== 'undefined') {
                    enemy = new E013(this, x, y);
                } else {
                    enemy = new E000a(this, x, y);
                    console.warn(`怪物类型 ${enemyType} 未实现，使用默认类型 E000a`);
                }
                break;
            case 'E014':
                enemy = new E000a(this, x, y);
                console.warn(`怪物类型 ${enemyType} 未实现，使用默认类型 E000a`);
                break;
            case 'E015': // Boss
                if (typeof E015 !== 'undefined') {
                    enemy = new E015(this, x, y);
                } else {
                    enemy = new E000a(this, x, y);
                    console.warn(`Boss类型 ${enemyType} 未实现，使用默认类型 E000a`);
                }
                break;
            default:
                enemy = new E000a(this, x, y);
                console.warn(`未知怪物类型 ${enemyType}，使用默认类型 E000a`);
        }

        // 将怪物添加到活跃敌人列表
        this.activeEnemies.push(enemy);

        console.log(`在位置 (${x.toFixed(0)}, ${y.toFixed(0)}) 生成怪物类型: ${enemyType}`);
    }

    /**
     * 生成单个怪物
     * @param {string} enemyType - 怪物类型
     */
    spawnSingleEnemy(enemyType = 'E000a') {
        // 获取玩家位置
        if (!this.player || !this.player.sprite) return;

        // 获取生成位置
        const position = this.getSpawnPositionOnSide();

        // 在该位置生成怪物
        this.spawnSingleEnemyAt(position.x, position.y, enemyType);
    }

    /**
     * 生成一组怪物
     * @param {string} enemyType - 怪物类型
     * @param {number} groupSize - 组大小
     */
    spawnEnemyGroup(enemyType, groupSize) {
        // 获取一个基准生成位置
        const basePosition = this.getSpawnPositionOnSide();

        // 在基准位置附近生成一组怪物
        for (let i = 0; i < groupSize; i++) {
            // 在基准位置附近随机偏移
            const offsetX = (Math.random() - 0.5) * 500;
            const offsetY = (Math.random() - 0.5) * 200;

            // 生成怪物
            this.spawnSingleEnemyAt(
                basePosition.x + offsetX,
                basePosition.y + offsetY,
                enemyType
            );
        }

        console.log(`生成了一组${groupSize}个${enemyType}类型的怪物`);
    }

    /**
     * 围攻生成怪物（圆形阵型）
     * @param {string} enemyType - 怪物类型
     * @param {number} count - 怪物数量
     */
    spawnSurroundingEnemies(enemyType, count) {
        // 获取玩家位置
        const playerX = this.player.sprite.x;
        const playerY = this.player.sprite.y;

        // 围攻半径（密集包围）
        const radius = 300;

        // 计算每个怪物的角度间隔
        const angleStep = (Math.PI * 2) / count;

        // 在圆形阵型中生成怪物
        for (let i = 0; i < count; i++) {
            const angle = i * angleStep;
            const x = playerX + Math.cos(angle) * radius;
            const y = playerY + Math.sin(angle) * radius;

            // 生成怪物
            this.spawnSingleEnemyAt(x, y, enemyType);
        }

        console.log(`生成了围攻阵型：${count}个${enemyType}类型的怪物`);
    }

    /**
     * 更新所有敌人
     */
    updateEnemies() {
        // 获取玩家位置
        if (!this.player || !this.player.sprite) return;

        const playerPosition = {
            x: this.player.sprite.x,
            y: this.player.sprite.y
        };

        // 计算最大怪物存在距离（如果尚未计算）
        if (this.spawnParams.maxEnemyDistance <= 0) {
            const width = this.cameras.main.width;
            const height = this.cameras.main.height;
            const screenDiagonal = Math.sqrt(width * width + height * height);

            // 生成距离是屏幕对角线的一半加上边距
            const spawnDistance = screenDiagonal / 2 + this.spawnParams.spawnMargin;

            // 销毁距离设置为生成距离的1.1倍，确保在生成区域外
            this.spawnParams.maxEnemyDistance = spawnDistance * 1.1;
            console.log(`设置怪物最大存在距离: ${this.spawnParams.maxEnemyDistance.toFixed(0)}像素`);
        }

        // 更新每个敌人
        for (let i = this.activeEnemies.length - 1; i >= 0; i--) {
            const enemy = this.activeEnemies[i];

            // 如果敌人已被销毁，从列表中移除
            if (!enemy || !enemy.sprite || !enemy.sprite.active) {
                this.activeEnemies.splice(i, 1);
                continue;
            }

            // 计算敌人与玩家的距离
            const dx = enemy.sprite.x - playerPosition.x;
            const dy = enemy.sprite.y - playerPosition.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            // 如果敌人距离玩家太远，销毁它（Boss除外）
            if (distance > this.spawnParams.maxEnemyDistance && !enemy.isBoss) {
                console.log(`销毁距离过远的怪物(${distance.toFixed(0)}像素)`);
                enemy.destroy();
                this.activeEnemies.splice(i, 1);
                continue;
            }

            // 更新敌人行为
            enemy.update(playerPosition);
        }
    }

    /**
     * 时间快进到下一分钟
     */
    skipToNextMinute() {
        // 计算当前分钟数
        const currentMinute = Math.floor(this.gameTime / 60000);

        // 设置新的分钟数（+1）
        const newMinute = currentMinute + 1;

        // 直接设置游戏时间为新分钟的开始
        this.gameTime = newMinute * 60000;

        // 更新开始时间，保持游戏时间一致
        this.startTime = this.time.now - this.gameTime;

        // 更新时间显示
        const timeText = this.uiManager.getComponent('timeText');
        if (timeText) {
            timeText.setText(`时间: ${newMinute.toString().padStart(2, '0')}:00`);
        }

        // 更新调试信息
        const debugText = this.uiManager.getComponent('debugText');
        if (debugText) {
            debugText.setText(`当前分钟: ${newMinute}`);
        }

        // 输出日志
        console.log(`========== 时间快进到第${newMinute}分钟 ==========`);

        // 更新lastMinute并触发新分钟的怪物生成
        this.lastMinute = newMinute;

        // 直接更新刷新配置
        this.updateSpawnConfig();
    }

    /**
     * 切换游戏暂停/继续状态
     */
    togglePause() {
        this.isPaused = !this.isPaused;

        if (this.isPaused) {
            // 暂停游戏
            const pauseButton = this.uiManager.getComponent('pauseButton');
            if (pauseButton) {
                pauseButton.setText('继续');
            }

            // 暂停物理引擎
            this.physics.pause();

            // 暂停所有动画
            this.anims.pauseAll();

            // 如果玩家存在，停止其移动
            if (this.player && this.player.sprite) {
                this.player.sprite.setVelocity(0, 0);
            }

            // 显示暂停提示
            const width = this.cameras.main.width;
            const height = this.cameras.main.height;

            this.pauseOverlay = this.add.rectangle(
                width / 2,
                height / 2,
                width,
                height,
                0x000000,
                0.5
            ).setDepth(100).setScrollFactor(0);

            this.pauseText = this.add.text(
                width / 2,
                height / 2,
                '游戏已暂停',
                {
                    font: 'bold 32px Arial',
                    fill: '#ffffff',
                    stroke: '#000000',
                    strokeThickness: 4
                }
            ).setOrigin(0.5).setDepth(101).setScrollFactor(0);
        } else {
            // 继续游戏
            const pauseButton = this.uiManager.getComponent('pauseButton');
            if (pauseButton) {
                pauseButton.setText('暂停');
            }

            // 恢复物理引擎
            this.physics.resume();

            // 恢复所有动画
            this.anims.resumeAll();

            // 移除暂停提示
            if (this.pauseOverlay) this.pauseOverlay.destroy();
            if (this.pauseText) this.pauseText.destroy();

            // 更新开始时间，确保暂停时间不计入游戏时间
            this.startTime = this.time.now - this.gameTime;
        }
    }

    /**
     * 检查敌人之间的碰撞
     * @param {Phaser.GameObjects.Sprite} enemy1 - 第一个敌人精灵
     * @param {Phaser.GameObjects.Sprite} enemy2 - 第二个敌人精灵
     * @returns {boolean} 是否应该发生碰撞
     */
    checkEnemyCollision(enemy1, enemy2) {
        // 获取敌人实例
        const instance1 = enemy1.enemyInstance;
        const instance2 = enemy2.enemyInstance;

        // 如果任何一个敌人不存在实例，允许碰撞（保险措施）
        if (!instance1 || !instance2) return true;

        // 检查敌人的构造函数名称
        const type1 = instance1.constructor.name;
        const type2 = instance2.constructor.name;

        // 如果任何一个敌人是E000a或E000b类型，不发生碰撞
        if (type1 === 'E000a' || type1 === 'E000b' || type2 === 'E000a' || type2 === 'E000b') {
            return false;
        }

        // 其他情况下允许碰撞
        return true;
    }

    /**
     * 玩家死亡处理
     */
    onPlayerDeath() {
        console.log('GameScene: 处理玩家死亡事件');
        this.endGameAndSave();
    }

    /**
     * 结束游戏并保存数据
     */
    endGameAndSave() {
        if (!window.gameData) {
            console.log('游戏数据系统不可用，跳过保存');
            return;
        }

        try {
            // 计算游戏统计数据
            const survivalTime = Math.floor(this.gameTime / 1000); // 转换为秒
            const currentLevel = this.player ? this.player.level : 1;

            // 保存游戏数据
            const success = window.gameData.endGame({
                coins: this.coinCount,
                kills: 0, // 暂时设为0，后续可以添加击杀统计
                survivalTime: survivalTime,
                level: currentLevel
            });

            if (success) {
                console.log(`游戏数据已保存 - 金币: ${this.coinCount}, 生存时间: ${survivalTime}秒, 等级: ${currentLevel}`);
            } else {
                console.error('保存游戏数据失败');
            }
        } catch (error) {
            console.error('结束游戏并保存数据时出错:', error);
        }
    }

    /**
     * 应用工作台buff
     */
    applyWorkstationBuffs() {
        if (!window.gameData) return;

        // 应用生命工作台buff
        this.applyHealthBuff();
        // 应用速度工作台buff
        this.applySpeedBuff();
        // 应用攻击工作台buff
        this.applyAttackBuff();
        // 应用范围工作台buff
        this.applyRangeBuff();
    }

    applyHealthBuff() {
        const config = HealthWorkstation.getConfig();
        const shouldHaveBuff = window.gameData.isWorkstationOwned(config.id) &&
            window.gameData.isWorkstationBuffActive(config.id);

        if (shouldHaveBuff && !this.healthBuffApplied) {
            const level = window.gameData.getWorkstationLevel(config.id);
            const buffValue = window.WorkstationConfig.qualities[level].buffValue;
            this.player.maxHealth = this.playerBaseMaxHealth * (1 + buffValue);
            this.player.currentHealth = this.player.maxHealth;
            this.player.updateHealthBar();
            this.healthBuffApplied = true;
        } else if (!shouldHaveBuff && this.healthBuffApplied) {
            this.player.maxHealth = this.playerBaseMaxHealth;
            if (this.player.currentHealth > this.player.maxHealth) {
                this.player.currentHealth = this.player.maxHealth;
            }
            this.player.updateHealthBar();
            this.healthBuffApplied = false;
        }
    }

    applySpeedBuff() {
        const config = SpeedWorkstation.getConfig();
        const shouldHaveBuff = window.gameData.isWorkstationOwned(config.id) &&
            window.gameData.isWorkstationBuffActive(config.id);

        if (shouldHaveBuff && !this.speedBuffApplied) {
            const level = window.gameData.getWorkstationLevel(config.id);
            const buffValue = window.WorkstationConfig.qualities[level].buffValue;
            this.player.moveSpeed = this.playerBaseMoveSpeed * (1 + buffValue);
            this.speedBuffApplied = true;
        } else if (!shouldHaveBuff && this.speedBuffApplied) {
            this.player.moveSpeed = this.playerBaseMoveSpeed;
            this.speedBuffApplied = false;
        }
    }

    applyAttackBuff() {
        const config = AttackWorkstation.getConfig();
        const shouldHaveBuff = window.gameData.isWorkstationOwned(config.id) &&
            window.gameData.isWorkstationBuffActive(config.id);

        if (shouldHaveBuff && !this.attackBuffApplied) {
            const level = window.gameData.getWorkstationLevel(config.id);
            const buffValue = window.WorkstationConfig.qualities[level].buffValue;
            this.player.attackPower = this.playerBaseAttackPower * (1 + buffValue);
            this.attackBuffApplied = true;
        } else if (!shouldHaveBuff && this.attackBuffApplied) {
            this.player.attackPower = this.playerBaseAttackPower;
            this.attackBuffApplied = false;
        }
    }

    applyRangeBuff() {
        const config = RangeWorkstation.getConfig();
        const shouldHaveBuff = window.gameData.isWorkstationOwned(config.id) &&
            window.gameData.isWorkstationBuffActive(config.id);

        if (shouldHaveBuff && !this.rangeBuffApplied) {
            const level = window.gameData.getWorkstationLevel(config.id);
            const buffValue = window.WorkstationConfig.qualities[level].buffValue;

            // 应用到所有武器的攻击范围
            if (this.player.weaponManager && this.player.weaponManager.activeWeapons) {
                this.player.weaponManager.activeWeapons.forEach(weapon => {
                    if (weapon.range) {
                        weapon.range *= (1 + buffValue);
                    }
                    if (weapon.width) {
                        weapon.width *= (1 + buffValue);
                    }

                    // 更新需要碰撞体更新的武器类型
                    if (weapon.preloadAssets && typeof weapon.preloadAssets === 'function') {
                        weapon.preloadAssets();
                    }
                });
            }
            this.rangeBuffApplied = true;
        } else if (!shouldHaveBuff && this.rangeBuffApplied) {
            // 恢复武器原始范围 - 简单除法恢复
            if (this.player.weaponManager && this.player.weaponManager.activeWeapons) {
                this.player.weaponManager.activeWeapons.forEach(weapon => {
                    if (weapon.range) {
                        weapon.range /= (1 + 0.25); // 使用最大可能的buff值恢复
                    }
                    if (weapon.width) {
                        weapon.width /= (1 + 0.25);
                    }
                });
            }
            this.rangeBuffApplied = false;
        }
    }

    /**
     * 场景销毁时清理UI
     */
    destroy() {
        // 清理虚拟摇杆
        this.destroyVirtualJoystick();

        if (this.uiManager) {
            this.uiManager.destroyAll();
            this.uiManager = null;
        }
        super.destroy();
    }

    /**
     * 创建虚拟摇杆
     */
    createVirtualJoystick() {

        // 摇杆参数
        this.joystick = {
            isActive: false,
            baseRadius: 60,
            knobRadius: 25,
            maxDistance: 50,
            baseX: 0,
            baseY: 0,
            knobX: 0,
            knobY: 0,
            moveX: 0,
            moveY: 0
        };

        // 创建摇杆底盘（初始隐藏）
        this.joystickBase = this.add.circle(0, 0, this.joystick.baseRadius, 0x333333, 0.3)
            .setDepth(this.DEPTH_LAYERS.UI + 5)
            .setScrollFactor(0)
            .setVisible(false);

        // 创建摇杆头（初始隐藏）
        this.joystickKnob = this.add.circle(0, 0, this.joystick.knobRadius, 0x666666, 0.8)
            .setDepth(this.DEPTH_LAYERS.UI + 6)
            .setScrollFactor(0)
            .setVisible(false);

        // 绑定触摸事件到场景输入
        this.input.on('pointerdown', this.onJoystickPointerDown, this);
        this.input.on('pointermove', this.onJoystickPointerMove, this);
        this.input.on('pointerup', this.onJoystickPointerUp, this);

        console.log('虚拟摇杆已创建');
    }

    /**
     * 触摸开始事件
     */
    onJoystickPointerDown(pointer) {
        // 激活摇杆
        this.joystick.isActive = true;

        // 设置摇杆底盘位置为触摸位置
        this.joystick.baseX = pointer.x;
        this.joystick.baseY = pointer.y;
        this.joystick.knobX = pointer.x;
        this.joystick.knobY = pointer.y;

        // 显示摇杆
        this.joystickBase.setPosition(this.joystick.baseX, this.joystick.baseY).setVisible(true);
        this.joystickKnob.setPosition(this.joystick.knobX, this.joystick.knobY).setVisible(true);
    }

    /**
     * 触摸移动事件
     */
    onJoystickPointerMove(pointer) {
        if (!this.joystick.isActive) return;

        // 计算从底盘中心到触摸点的距离和角度
        const deltaX = pointer.x - this.joystick.baseX;
        const deltaY = pointer.y - this.joystick.baseY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        if (distance <= this.joystick.maxDistance) {
            // 在范围内，摇杆头跟随触摸点
            this.joystick.knobX = pointer.x;
            this.joystick.knobY = pointer.y;
        } else {
            // 超出范围，限制在最大距离内
            const angle = Math.atan2(deltaY, deltaX);
            this.joystick.knobX = this.joystick.baseX + Math.cos(angle) * this.joystick.maxDistance;
            this.joystick.knobY = this.joystick.baseY + Math.sin(angle) * this.joystick.maxDistance;
        }

        // 更新摇杆头位置
        this.joystickKnob.setPosition(this.joystick.knobX, this.joystick.knobY);

        // 计算移动向量（标准化到-1到1之间）
        const finalDeltaX = this.joystick.knobX - this.joystick.baseX;
        const finalDeltaY = this.joystick.knobY - this.joystick.baseY;

        this.joystick.moveX = finalDeltaX / this.joystick.maxDistance;
        this.joystick.moveY = finalDeltaY / this.joystick.maxDistance;
    }

    /**
     * 触摸结束事件
     */
    onJoystickPointerUp() {
        // 停用摇杆
        this.joystick.isActive = false;

        // 隐藏摇杆
        this.joystickBase.setVisible(false);
        this.joystickKnob.setVisible(false);

        // 重置移动向量
        this.joystick.moveX = 0;
        this.joystick.moveY = 0;
    }

    /**
     * 更新虚拟摇杆
     */
    updateVirtualJoystick() {
        if (!this.joystick || !this.player) return;

        // 如果摇杆激活，使用摇杆输入控制玩家移动
        if (this.joystick.isActive) {
            const moveInput = {
                x: this.joystick.moveX,
                y: this.joystick.moveY
            };
            this.player.move(moveInput);
        }
    }

    /**
     * 销毁虚拟摇杆
     */
    destroyVirtualJoystick() {
        // 移除事件监听器
        if (this.input) {
            this.input.off('pointerdown', this.onJoystickPointerDown, this);
            this.input.off('pointermove', this.onJoystickPointerMove, this);
            this.input.off('pointerup', this.onJoystickPointerUp, this);
        }

        // 销毁摇杆UI元素
        if (this.joystickBase) {
            this.joystickBase.destroy();
            this.joystickBase = null;
        }
        if (this.joystickKnob) {
            this.joystickKnob.destroy();
            this.joystickKnob = null;
        }

        // 清理摇杆数据
        this.joystick = null;

        console.log('虚拟摇杆已销毁');
    }

    /**
     * 创建宠物动画
     */
    createPetAnimations() {
        // 宠物001 - 猫咪宠物（精灵表）
        if (!this.anims.exists('pet001_anim')) {
            this.anims.create({
                key: 'pet001_anim',
                frames: this.anims.generateFrameNumbers('pet001_sprite', {
                    start: 0,
                    end: 12 // 13帧动画
                }),
                frameRate: 10,
                repeat: -1
            });
            console.log('创建宠物动画: pet001_anim, 使用精灵表');
        }

        // 宠物002 - 蘑菇宠物（多帧图片）
        if (!this.anims.exists('pet002_anim')) {
            this.anims.create({
                key: 'pet002_anim',
                frames: Array.from({ length: 8 }, (_, i) => ({
                    key: `pet_pet002_${i + 1}`
                })),
                frameRate: 10,
                repeat: -1
            });
            console.log('创建宠物动画: pet002_anim, 帧数: 8');
        }

        // 宠物003 - 哈比人宠物（多帧图片）
        if (!this.anims.exists('pet_pet003_anim')) {
            this.anims.create({
                key: 'pet_pet003_anim',
                frames: Array.from({ length: 10 }, (_, i) => ({
                    key: `pet_pet003_${i + 1}`
                })),
                frameRate: 10,
                repeat: -1
            });
            console.log('创建宠物动画: pet_pet003_anim, 帧数: 10');
        }

        // 宠物004 - Godot宠物（多帧图片）
        if (!this.anims.exists('pet_pet004_anim')) {
            this.anims.create({
                key: 'pet_pet004_anim',
                frames: Array.from({ length: 4 }, (_, i) => ({
                    key: `pet_pet004_${i + 1}`
                })),
                frameRate: 10,
                repeat: -1
            });
            console.log('创建宠物动画: pet_pet004_anim, 帧数: 4');
        }

        // 宠物005 - 蘑菇奔跑宠物（多帧图片）
        if (!this.anims.exists('pet005_anim')) {
            this.anims.create({
                key: 'pet005_anim',
                frames: Array.from({ length: 8 }, (_, i) => ({
                    key: `pet_pet005_${i + 1}`
                })),
                frameRate: 10,
                repeat: -1
            });
            console.log('创建宠物动画: pet005_anim, 帧数: 8');
        }
    }
}




