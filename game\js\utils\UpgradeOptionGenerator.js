/**
 * 升级选项生成器
 */
class UpgradeOptionGenerator {
    constructor() {
        // 基础权重配置
        this.baseWeights = {
            newWeapon: 100,      // 新武器基础权重
            weaponUpgrade: 80,   // 武器升级基础权重
            newPassive: 60,      // 新被动道具基础权重
            passiveUpgrade: 40   // 被动道具升级基础权重
        };
    }

    /**
     * 生成升级选项
     * @param {Object} player - 玩家对象
     * @returns {Array} 升级选项数组（2个选项）
     */
    generateUpgradeOptions(player) {
        // 收集所有可用选项
        const allOptions = this.collectAllOptions(player);

        // 按类别分类
        const categorizedOptions = this.categorizeOptions(allOptions, player);

        // 应用权重系统
        const weightedOptions = this.applyWeightSystem(categorizedOptions);

        // 选择2个选项
        const selectedOptions = this.selectOptions(weightedOptions, 2);

        return selectedOptions;
    }

    /**
     * 收集所有可用选项
     * @param {Object} player - 玩家对象
     * @returns {Array} 所有可用选项
     */
    collectAllOptions(player) {
        const options = [];

        // 收集武器选项
        if (player.weaponManager) {
            const weaponOptions = WeaponUpgradeUtils.generateWeaponUpgradeOptions(player.weaponManager);
            options.push(...weaponOptions);
        }

        // 收集被动道具选项（使用现有的道具系统）
        const passiveOptions = this.getPassiveOptions();
        options.push(...passiveOptions);

        return options;
    }

    /**
     * 获取被动道具选项
     * @returns {Array} 被动道具选项
     */
    getPassiveOptions() {
        // 使用现有的InGameItems系统，但添加类型标识
        if (typeof window.getRandomItems === 'function') {
            const items = window.getRandomItems(10); // 获取更多选项用于筛选
            return items.map(item => ({
                ...item,
                optionType: 'passive',
                category: 'newPassive' // 暂时都标记为新道具
            }));
        }
        return [];
    }

    /**
     * 按类别分类选项
     * @param {Array} options - 所有选项
     * @param {Object} player - 玩家对象
     * @returns {Object} 分类后的选项
     */
    categorizeOptions(options, player) {
        const categorized = {
            newWeapon: [],      // A类：未获得武器
            weaponUpgrade: [],  // B类：武器升级
            newPassive: [],     // C类：未获得被动道具
            passiveUpgrade: []  // D类：被动道具升级
        };

        for (const option of options) {
            if (option.type === 'new_weapon') {
                categorized.newWeapon.push(option);
            } else if (option.type === 'weapon_upgrade') {
                categorized.weaponUpgrade.push(option);
            } else if (option.optionType === 'passive') {
                categorized.newPassive.push(option);
            }
        }

        return categorized;
    }

    /**
     * 应用权重系统
     * @param {Object} categorizedOptions - 分类后的选项
     * @returns {Array} 带权重的选项
     */
    applyWeightSystem(categorizedOptions) {
        const weightedOptions = [];

        // 按优先级处理各类别
        const categories = ['newWeapon', 'weaponUpgrade', 'newPassive', 'passiveUpgrade'];

        for (const category of categories) {
            const options = categorizedOptions[category];

            for (const option of options) {
                const weight = this.calculateWeight(option, category);
                weightedOptions.push({
                    ...option,
                    weight: weight,
                    category: category
                });
            }
        }

        return weightedOptions;
    }

    /**
     * 计算选项权重
     * @param {Object} option - 选项对象
     * @param {string} category - 选项类别
     * @returns {number} 计算后的权重
     */
    calculateWeight(option, category) {
        return this.baseWeights[category] || 50;
    }

    /**
     * 选择指定数量的选项
     * @param {Array} weightedOptions - 带权重的选项
     * @param {number} count - 需要选择的数量
     * @returns {Array} 选中的选项
     */
    selectOptions(weightedOptions, count) {
        if (weightedOptions.length === 0) {
            return [];
        }

        const selected = [];
        let remainingOptions = [...weightedOptions];

        for (let i = 0; i < count && remainingOptions.length > 0; i++) {
            // 计算总权重
            const totalWeight = remainingOptions.reduce((sum, option) => sum + option.weight, 0);

            if (totalWeight === 0) break;

            // 生成随机数
            const random = Math.random() * totalWeight;

            // 选择选项
            let cumulativeWeight = 0;
            let selectedIndex = -1;

            for (let j = 0; j < remainingOptions.length; j++) {
                cumulativeWeight += remainingOptions[j].weight;
                if (random <= cumulativeWeight) {
                    selectedIndex = j;
                    break;
                }
            }

            // 添加到选中列表并从剩余选项中移除
            if (selectedIndex >= 0) {
                selected.push(remainingOptions[selectedIndex]);
                remainingOptions.splice(selectedIndex, 1);
            }
        }

        return selected;
    }

    /**
     * 记录玩家选择
     * @param {Object} selectedOption - 玩家选择的选项
     */
    recordSelection(selectedOption) {
        // 简单记录，暂不处理
    }
}

// 创建全局实例
window.upgradeOptionGenerator = new UpgradeOptionGenerator();
