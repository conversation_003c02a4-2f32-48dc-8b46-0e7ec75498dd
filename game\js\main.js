/**
 * 基本配置和初始化
 */

// 游戏配置
const GameConfig = {
    // 游戏核心设置
    core: {
        width: 720,                 // 游戏宽度
        height: 1280,               // 游戏高度
        backgroundColor: '#2f4f2f', // 游戏背景色
        pixelArt: true,             // 是否启用像素艺术模式
        antialias: false            // 是否启用抗锯齿
    }
};

// 全局调试配置
const GameDebug = {
    weapons: false,  // 武器系统调试
    enemies: false,  // 敌人系统调试
    items: false,    // 物品系统调试
    physics: false,  // 物理系统调试
    spawner: false   // 怪物生成系统调试
};

// 导出配置对象
window.GameConfig = GameConfig;
window.GameDebug = GameDebug;

// Phaser游戏配置
const config = {
    type: Phaser.AUTO,
    width: GameConfig.core.width,
    height: GameConfig.core.height,
    parent: 'game-container',
    backgroundColor: GameConfig.core.backgroundColor,
    dom: {
        createContainer: true
    },
    physics: {
        default: 'arcade',
        arcade: {
            gravity: { y: 0 },
            debug: false  // 将false改为true，开启物理引擎调试显示
        }
    },
    scale: {
        mode: Phaser.Scale.FIT,
        orientation: Phaser.Scale.PORTRAIT, // 强制竖屏
        autoCenter: Phaser.Scale.CENTER_BOTH
    },
    render: {
        pixelArt: GameConfig.core.pixelArt,
        antialias: GameConfig.core.antialias,
        willReadFrequently: true
    },
    loader: {
        crossOrigin: 'anonymous'
    },
    scene: [StartScene, MainMenuScene, GameScene, HomeScene, PetShopScene]  // 添加所有场景到场景列表
};

// 创建游戏实例
const game = new Phaser.Game(config);
