/**
 * 游戏数据管理器
 * 管理全局游戏数据和状态
 */
class GameData {
    constructor() {
        this.saveManager = new SaveManager();
        this.currentGameData = null;
        this.sessionData = {
            currentGameCoins: 0,      // 当前局游戏获得的金币
            currentGameKills: 0,      // 当前局击杀数
            currentGameTime: 0,       // 当前局游戏时间
            currentGameLevel: 1       // 当前局达到的等级
        };

        // 初始化数据
        this.loadData();

        console.log('GameData初始化完成');
    }

    /**
     * 加载游戏数据
     */
    loadData() {
        this.currentGameData = this.saveManager.loadGameData();
        console.log('游戏数据已加载:', this.currentGameData);
    }

    /**
     * 保存游戏数据
     * @returns {boolean} 保存是否成功
     */
    saveData() {
        if (!this.currentGameData) {
            console.error('没有可保存的游戏数据');
            return false;
        }

        return this.saveManager.saveGameData(this.currentGameData);
    }

    /**
     * 获取累计金币
     * @returns {number} 累计金币数量
     */
    getTotalCoins() {
        return this.currentGameData?.player?.totalCoins || 0;
    }

    /**
     * 添加金币到累计总数
     * @param {number} amount - 要添加的金币数量
     * @returns {boolean} 操作是否成功
     */
    addTotalCoins(amount) {
        if (!this.currentGameData) {
            console.error('游戏数据未初始化');
            return false;
        }

        const coinAmount = Math.max(0, Number(amount) || 0);
        this.currentGameData.player.totalCoins += coinAmount;

        console.log(`添加 ${coinAmount} 金币到累计总数，当前总计: ${this.currentGameData.player.totalCoins}`);
        return this.saveData();
    }

    /**
     * 开始新游戏
     */
    startNewGame() {
        // 重置当前局数据
        this.sessionData = {
            currentGameCoins: 0,
            currentGameKills: 0,
            currentGameTime: 0,
            currentGameLevel: 1
        };

        console.log('开始新游戏，会话数据已重置');
    }

    /**
     * 结束当前游戏并保存统计数据
     * @param {Object} gameStats - 游戏统计数据
     * @param {number} gameStats.coins - 本局获得的金币
     * @param {number} gameStats.kills - 本局击杀数
     * @param {number} gameStats.survivalTime - 本局生存时间（秒）
     * @param {number} gameStats.level - 本局达到的等级
     * @returns {boolean} 保存是否成功
     */
    endGame(gameStats = {}) {
        if (!this.currentGameData) {
            console.error('游戏数据未初始化');
            return false;
        }

        const {
            coins = this.sessionData.currentGameCoins,
            kills = this.sessionData.currentGameKills,
            survivalTime = this.sessionData.currentGameTime,
            level = this.sessionData.currentGameLevel
        } = gameStats;

        // 更新累计统计
        this.currentGameData.player.totalCoins += Math.max(0, Number(coins) || 0);
        this.currentGameData.player.totalGames += 1;
        this.currentGameData.player.totalKills += Math.max(0, Number(kills) || 0);
        this.currentGameData.player.totalSurvivalTime += Math.max(0, Number(survivalTime) || 0);

        // 更新最佳记录
        if (survivalTime > this.currentGameData.player.bestSurvivalTime) {
            this.currentGameData.player.bestSurvivalTime = survivalTime;
            console.log(`新的最佳生存时间记录: ${survivalTime}秒`);
        }

        if (level > this.currentGameData.player.maxLevel) {
            this.currentGameData.player.maxLevel = level;
            console.log(`新的最高等级记录: ${level}`);
        }

        console.log(`游戏结束统计:
            本局金币: ${coins}
            本局击杀: ${kills}
            本局生存时间: ${survivalTime}秒
            本局等级: ${level}
            累计金币: ${this.currentGameData.player.totalCoins}
            总游戏次数: ${this.currentGameData.player.totalGames}`);

        return this.saveData();
    }

    /**
     * 更新当前局金币数
     * @param {number} amount - 金币数量
     */
    updateCurrentGameCoins(amount) {
        this.sessionData.currentGameCoins = Math.max(0, Number(amount) || 0);
    }

    /**
     * 更新当前局击杀数
     * @param {number} kills - 击杀数
     */
    updateCurrentGameKills(kills) {
        this.sessionData.currentGameKills = Math.max(0, Number(kills) || 0);
    }

    /**
     * 更新当前局游戏时间
     * @param {number} time - 游戏时间（秒）
     */
    updateCurrentGameTime(time) {
        this.sessionData.currentGameTime = Math.max(0, Number(time) || 0);
    }

    /**
     * 更新当前局等级
     * @param {number} level - 等级
     */
    updateCurrentGameLevel(level) {
        this.sessionData.currentGameLevel = Math.max(1, Number(level) || 1);
    }

    /**
     * 获取当前局统计数据
     * @returns {Object} 当前局统计数据
     */
    getCurrentGameStats() {
        return { ...this.sessionData };
    }

    /**
     * 获取玩家统计数据
     * @returns {Object} 玩家统计数据
     */
    getPlayerStats() {
        if (!this.currentGameData) {
            return {
                totalCoins: 0,
                totalGames: 0,
                totalKills: 0,
                totalSurvivalTime: 0,
                bestSurvivalTime: 0,
                maxLevel: 1,
                unlockedWeapons: [],
                unlockedItems: [],
                achievements: []
            };
        }

        return { ...this.currentGameData.player };
    }

    /**
     * 获取游戏设置
     * @returns {Object} 游戏设置
     */
    getSettings() {
        return this.currentGameData?.settings || {
            soundEnabled: true,
            musicEnabled: true,
            volume: 1.0
        };
    }

    /**
     * 更新游戏设置
     * @param {Object} newSettings - 新的设置
     * @returns {boolean} 更新是否成功
     */
    updateSettings(newSettings) {
        if (!this.currentGameData) {
            console.error('游戏数据未初始化');
            return false;
        }

        this.currentGameData.settings = { ...this.currentGameData.settings, ...newSettings };
        return this.saveData();
    }

    /**
     * 解锁武器
     * @param {string} weaponId - 武器ID
     * @returns {boolean} 操作是否成功
     */
    unlockWeapon(weaponId) {
        if (!this.currentGameData) {
            console.error('游戏数据未初始化');
            return false;
        }

        if (!this.currentGameData.player.unlockedWeapons.includes(weaponId)) {
            this.currentGameData.player.unlockedWeapons.push(weaponId);
            console.log(`武器已解锁: ${weaponId}`);
            return this.saveData();
        }

        return true;
    }

    /**
     * 解锁道具
     * @param {string} itemId - 道具ID
     * @returns {boolean} 操作是否成功
     */
    unlockItem(itemId) {
        if (!this.currentGameData) {
            console.error('游戏数据未初始化');
            return false;
        }

        if (!this.currentGameData.player.unlockedItems.includes(itemId)) {
            this.currentGameData.player.unlockedItems.push(itemId);
            console.log(`道具已解锁: ${itemId}`);
            return this.saveData();
        }

        return true;
    }

    /**
     * 获取已解锁的武器列表
     * @returns {Array} 已解锁的武器ID列表
     */
    getUnlockedWeapons() {
        return this.currentGameData?.player?.unlockedWeapons || [];
    }

    /**
     * 获取已解锁的道具列表
     * @returns {Array} 已解锁的道具ID列表
     */
    getUnlockedItems() {
        return this.currentGameData?.player?.unlockedItems || [];
    }

    /**
     * 检查武器是否已解锁
     * @param {string} weaponId - 武器ID
     * @returns {boolean} 是否已解锁
     */
    isWeaponUnlocked(weaponId) {
        return this.getUnlockedWeapons().includes(weaponId);
    }

    /**
     * 检查道具是否已解锁
     * @param {string} itemId - 道具ID
     * @returns {boolean} 是否已解锁
     */
    isItemUnlocked(itemId) {
        return this.getUnlockedItems().includes(itemId);
    }

    /**
     * 获取已拥有的宠物列表
     * @returns {Array} 已拥有的宠物ID列表
     */
    getOwnedPets() {
        return this.currentGameData?.player?.ownedPets || ['pet001'];
    }

    /**
     * 添加拥有的宠物
     * @param {string} petId - 宠物ID
     * @returns {boolean} 操作是否成功
     */
    addOwnedPet(petId) {
        if (!this.currentGameData) {
            console.error('游戏数据未初始化');
            return false;
        }

        if (!this.currentGameData.player.ownedPets.includes(petId)) {
            this.currentGameData.player.ownedPets.push(petId);
            console.log(`宠物已拥有: ${petId}`);
            return this.saveData();
        }

        return true;
    }

    /**
     * 检查宠物是否已拥有
     * @param {string} petId - 宠物ID
     * @returns {boolean} 是否已拥有
     */
    isOwnedPet(petId) {
        return this.getOwnedPets().includes(petId);
    }

    /**
     * 获取已拥有的工作台列表
     * @returns {Array} 已拥有的工作台ID列表
     */
    getOwnedWorkstations() {
        return this.currentGameData?.player?.ownedWorkstations || [];
    }

    /**
     * 检查工作台是否已拥有
     * @param {string} workstationId - 工作台ID
     * @returns {boolean} 是否已拥有
     */
    isWorkstationOwned(workstationId) {
        return this.getOwnedWorkstations().includes(workstationId);
    }

    /**
     * 购买工作台
     * @param {string} workstationId - 工作台ID
     * @param {number} cost - 费用
     * @returns {boolean} 操作是否成功
     */
    buyWorkstation(workstationId, cost) {
        if (!this.currentGameData) {
            console.error('游戏数据未初始化');
            return false;
        }

        // 检查金币是否足够
        if (this.getTotalCoins() < cost) {
            console.log(`金币不足，需要 ${cost}，当前 ${this.getTotalCoins()}`);
            return false;
        }

        // 检查是否已拥有
        if (this.isWorkstationOwned(workstationId)) {
            console.log('工作台已拥有');
            return false;
        }

        // 扣除金币并添加工作台
        this.currentGameData.player.totalCoins -= cost;
        this.currentGameData.player.ownedWorkstations.push(workstationId);

        console.log(`成功购买工作台: ${workstationId}, 花费: ${cost}`);
        return this.saveData();
    }

    /**
     * 购买工作台buff
     * @param {string} workstationId - 工作台ID
     * @param {number} duration - 持续时间(毫秒)
     * @param {number} cost - 费用
     * @returns {boolean} 操作是否成功
     */
    buyWorkstationBuff(workstationId, duration, cost) {
        if (!this.currentGameData) return false;

        // 检查金币是否足够
        if (this.getTotalCoins() < cost) {
            console.log('金币不足，无法购买buff');
            return false;
        }

        // 扣除金币
        this.currentGameData.player.totalCoins -= cost;

        // 初始化buff数据结构
        if (!this.currentGameData.workstationBuffs) {
            this.currentGameData.workstationBuffs = {};
        }

        // 设置buff状态
        this.currentGameData.workstationBuffs[workstationId] = {
            endTime: Date.now() + duration,
            isActive: true
        };

        console.log(`购买${workstationId}buff成功，持续${duration / 1000}秒`);
        return this.saveData();
    }

    /**
     * 获取工作台等级
     * @param {string} workstationId - 工作台ID
     * @returns {number} 工作台等级
     */
    getWorkstationLevel(workstationId) {
        if (!this.currentGameData) return 1;

        if (!this.currentGameData.player.workstationLevels) {
            this.currentGameData.player.workstationLevels = {};
        }

        return this.currentGameData.player.workstationLevels[workstationId] || 1;
    }

    /**
     * 升级工作台
     * @param {string} workstationId - 工作台ID
     * @returns {boolean} 操作是否成功
     */
    upgradeWorkstation(workstationId) {
        if (!this.currentGameData) return false;

        const currentLevel = this.getWorkstationLevel(workstationId);
        if (currentLevel >= 5) {
            console.log('工作台已达最高级');
            return false;
        }

        const nextLevel = currentLevel + 1;
        const upgradeCost = window.WorkstationConfig.qualities[nextLevel].upgradeCost;

        if (this.getTotalCoins() < upgradeCost) {
            console.log(`金币不足，需要 ${upgradeCost}，当前 ${this.getTotalCoins()}`);
            return false;
        }

        this.currentGameData.player.totalCoins -= upgradeCost;

        if (!this.currentGameData.player.workstationLevels) {
            this.currentGameData.player.workstationLevels = {};
        }

        this.currentGameData.player.workstationLevels[workstationId] = nextLevel;

        console.log(`工作台升级成功: ${workstationId} -> 等级 ${nextLevel}, 花费: ${upgradeCost}`);
        return this.saveData();
    }

    /**
     * 检查工作台buff是否激活
     * @param {string} workstationId - 工作台ID
     * @returns {boolean} 是否激活
     */
    isWorkstationBuffActive(workstationId) {
        if (!this.currentGameData || !this.currentGameData.workstationBuffs) return false;

        const buff = this.currentGameData.workstationBuffs[workstationId];
        if (!buff || !buff.isActive) return false;

        // 检查是否过期
        if (Date.now() >= buff.endTime) {
            buff.isActive = false;
            this.saveData();
            return false;
        }

        return true;
    }

    /**
     * 获取工作台buff剩余时间
     * @param {string} workstationId - 工作台ID
     * @returns {number} 剩余时间(秒)
     */
    getWorkstationBuffTimeLeft(workstationId) {
        if (!this.isWorkstationBuffActive(workstationId)) return 0;

        const buff = this.currentGameData.workstationBuffs[workstationId];
        return Math.ceil((buff.endTime - Date.now()) / 1000);
    }

    /**
     * 重置所有数据（用于调试）
     */
    resetAllData() {
        this.saveManager.clearAllData();
        this.loadData();
        console.log('所有游戏数据已重置');
    }

    /**
     * 获取格式化的生存时间
     * @param {number} seconds - 秒数
     * @returns {string} 格式化的时间字符串
     */
    static formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
}

// 全局游戏数据实例将在游戏启动时初始化
window.gameData = null;

/**
 * 安全地初始化游戏数据系统
 * @returns {boolean} 初始化是否成功
 */
window.initializeGameData = function () {
    try {
        if (window.gameData) {
            console.log('游戏数据已经初始化');
            return true;
        }

        console.log('开始初始化游戏数据系统...');
        window.gameData = new GameData();
        console.log('游戏数据系统初始化成功');
        return true;

    } catch (error) {
        console.error('初始化游戏数据系统失败:', error);
        window.gameData = null;
        return false;
    }
};
