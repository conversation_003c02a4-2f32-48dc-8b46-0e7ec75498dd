/**
 * 宠物管理器
 * 管理所有宠物的创建、队列和更新
 */
class PetManager {
    /**
     * 创建宠物管理器
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {Player} player - 玩家实例
     */
    constructor(scene, player) {
        this.scene = scene;
        this.player = player;

        // 宠物系统数据
        this.activePets = [];     // 当前活跃的宠物队列
        this.unlockedPets = [];   // 已解锁的宠物类型列表

        // 设置玩家的宠物管理器引用
        if (this.player) {
            this.player.setPetManager(this);
        }

        // 初始化已解锁宠物列表（默认解锁所有宠物）
        this.initUnlockedPets();
    }

    /**
     * 初始化已解锁宠物列表
     * 从存档中加载已拥有的宠物
     */
    initUnlockedPets() {
        if (window.gameData) {
            this.unlockedPets = window.gameData.getOwnedPets();
        } else {
            // 降级方案
            this.unlockedPets = ['pet001'];
        }
    }

    /**
     * 创建默认的宠物队列
     * 使用已解锁的宠物类型创建宠物
     */
    createDefaultPets() {
        // 创建5个宠物，使用不同的宠物类型
        const petTypes = ['pet001', 'pet002', 'pet003', 'pet004', 'pet005'];

        for (let i = 0; i < petTypes.length; i++) {
            // 检查宠物是否已解锁
            if (this.unlockedPets.includes(petTypes[i])) {
                this.createPet(petTypes[i]);
            }
        }
    }

    /**
     * 根据宠物类型创建宠物
     * @param {string} petType - 宠物类型
     * @param {Object} config - 额外配置
     * @returns {Pet} 创建的宠物实例
     */
    createPet(petType, config = {}) {
        // 根据宠物类型获取间距
        let spacing;
        switch (petType) {
            case 'pet001': spacing = Pet001.SPACING; break;
            case 'pet002': spacing = Pet002.SPACING; break;
            case 'pet003': spacing = Pet003.SPACING; break;
            case 'pet004': spacing = Pet004.SPACING; break;
            default: spacing = 35; break;
        }

        // 计算跟随延迟步数 - 累加前面所有宠物的间距
        let followDelay = spacing; // 自己的间距
        for (let i = 0; i < this.activePets.length; i++) {
            const prevPet = this.activePets[i];
            const prevSpacing = prevPet.constructor.SPACING || 35;
            followDelay += prevSpacing;
        }

        // 所有宠物直接跟随玩家
        const target = this.player;

        // 合并配置
        const petConfig = {
            ...config,
            followDelay
        };

        let pet;

        // 根据宠物类型创建对应的宠物实例
        switch (petType) {
            case 'pet001':
                pet = new Pet001(this.scene, target, petConfig);
                break;
            case 'pet002':
                pet = new Pet002(this.scene, target, petConfig);
                break;
            case 'pet003':
                pet = new Pet003(this.scene, target, petConfig);
                break;
            case 'pet004':
                pet = new Pet004(this.scene, target, petConfig);
                break;
            default:
                // 如果类型无效，创建默认宠物
                pet = new Pet(this.scene, target, petConfig);
        }

        // 添加到活跃队列
        this.activePets.push(pet);

        console.log(`创建宠物: ${petType}, 跟随延迟: ${followDelay}`);
        return pet;
    }

    /**
     * 添加宠物到活跃队列
     * @param {Object} config - 宠物配置
     * @returns {Pet} 创建的宠物实例
     */
    addPet(config) {
        // 计算跟随延迟步数 - 每个宠物的延迟递增
        const followDelay = 35 + (this.activePets.length * 35);

        // 所有宠物直接跟随玩家
        const target = this.player;

        // 合并配置
        const petConfig = {
            ...config,
            followDelay
        };

        // 创建宠物
        const pet = new Pet(this.scene, target, petConfig);

        // 添加到活跃队列
        this.activePets.push(pet);

        console.log(`添加宠物, 跟随延迟: ${followDelay}`);
        return pet;
    }

    /**
     * 从活跃队列中移除宠物
     * @param {Pet} pet - 要移除的宠物
     */
    removePet(pet) {
        const index = this.activePets.indexOf(pet);
        if (index !== -1) {
            // 销毁宠物并从数组中移除
            pet.destroy();
            this.activePets.splice(index, 1);

            // 重新分配所有宠物的跟随延迟
            this.updateFollowDelays();
        }
    }

    /**
     * 更新所有宠物的跟随延迟
     */
    updateFollowDelays() {
        for (let i = 0; i < this.activePets.length; i++) {
            const pet = this.activePets[i];
            const currentSpacing = pet.constructor.SPACING || 35;

            // 累加前面所有宠物的间距
            let followDelay = currentSpacing; // 自己的间距
            for (let j = 0; j < i; j++) {
                const prevPet = this.activePets[j];
                const prevSpacing = prevPet.constructor.SPACING || 35;
                followDelay += prevSpacing;
            }

            // 更新跟随延迟
            pet.followDelay = followDelay;
        }
    }

    /**
     * 清除所有活跃宠物
     */
    clearAllPets() {
        for (const pet of this.activePets) {
            pet.destroy();
        }
        this.activePets = [];
    }

    /**
     * 更新所有活跃宠物
     */
    update() {
        for (const pet of this.activePets) {
            pet.update();
        }
    }
}