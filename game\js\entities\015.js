/**
 * 第15分钟生成的Boss敌人 - 终极审判者
 */
class E015 extends Enemy {
    /** 缩放比例 */
    static SCALE = 1;

    /** 碰撞箱设置 */
    static COLLISION_BOX = {
        type: 'circle',
        radius: 120,
        offsetX: 10,
        offsetY: 20
    };

    /**
     * 创建Boss敌人
     */
    constructor(scene, x, y) {
        // 属性: 生命值, 伤害值, 移动速度
        super(scene, x, y, 'e015', 5000, 90, 120);

        // Boss特有属性
        this.isDashing = false;           // 是否正在冲刺
        this.dashCooldown = 0;           // 冲刺冷却时间
        this.dashInterval = 3000;        // 冲刺间隔（毫秒）
        this.dashSpeed = 200;            // 冲刺速度
        this.dashDuration = 1000;        // 冲刺持续时间
        this.entrancePerformed = false;  // 是否已执行登场表演
        this.battleStartTime = 0;        // 战斗开始时间
        this.maxBattleTime = 30000;      // 最大战斗时间（30秒）

        // 标记为Boss，免疫距离检测
        this.isBoss = true;

        // 加载图片和创建动画
        this.loadAssets();

        // 清空场上所有其他怪物
        if (this.scene.enemiesGroup) {
            this.scene.enemiesGroup.children.each(enemySprite => {
                if (enemySprite && enemySprite !== this.sprite) {
                    const enemy = enemySprite.enemyInstance;
                    if (enemy && enemy !== this) {
                        enemy.destroy();
                    }
                }
            });
        }
    }

    /**
     * 加载图片和创建动画
     */
    loadAssets() {
        // 动画帧图片
        const frames = [
            { key: 'e015_1', path: 'assets/images/015/015_01.png' },
            { key: 'e015_2', path: 'assets/images/015/015_02.png' },
            { key: 'e015_3', path: 'assets/images/015/015_03.png' },
            { key: 'e015_4', path: 'assets/images/015/015_04.png' }
        ];

        // 加载图片
        frames.forEach(frame => {
            this.scene.load.image(frame.key, frame.path);
        });

        // 加载完成后创建动画并播放
        this.scene.load.once('complete', () => {
            // 创建动画
            if (!this.scene.anims.exists('e015_anim')) {
                this.scene.anims.create({
                    key: 'e015_anim',
                    frames: frames.map(frame => ({ key: frame.key })),
                    frameRate: 6,
                    repeat: -1
                });
            }

            if (this.sprite) {
                this.sprite.setTexture('e015_1');
                this.sprite.setScale(E015.SCALE);
                this.sprite.play('e015_anim');

                // 创建威胁性发光和缩放动画
                this.glowTween = this.scene.tweens.add({
                    targets: this.sprite,
                    alpha: 0.7,
                    duration: 600,
                    yoyo: true,
                    repeat: -1
                });

                this.scaleTween = this.scene.tweens.add({
                    targets: this.sprite,
                    scaleX: E015.SCALE * 1.1,
                    scaleY: E015.SCALE * 1.1,
                    duration: 1200,
                    yoyo: true,
                    repeat: -1
                });

                // 资源加载完成后执行登场表演
                this.performEntrance();
            }
        });

        this.scene.load.start();
    }

    /**
     * 登场表演
     */
    performEntrance() {
        if (this.entrancePerformed) return;
        this.entrancePerformed = true;

        console.log('[E015] 终极审判者降临！');

        // 震屏效果
        if (this.scene.cameras && this.scene.cameras.main) {
            this.scene.cameras.main.shake(3000, 0.02);
        }

        // 屏幕闪红光效果
        if (this.sprite) {
            this.sprite.setTint(0xff0000);
            this.scene.time.delayedCall(500, () => {
                if (this.sprite) this.sprite.clearTint();
            });
        }

        // 3秒后开始战斗
        this.scene.time.delayedCall(3000, () => {
            this.battleStartTime = this.scene.time.now;
            console.log('[E015] 审判开始！');
        });
    }

    /**
     * 更新行为
     */
    update(playerPosition) {
        if (!this.isAlive || !this.sprite || !playerPosition) return;

        // 如果还在登场阶段，不进行攻击
        if (this.battleStartTime === 0) {
            return;
        }

        // 检查是否超过最大战斗时间，强制提升伤害
        const battleTime = this.scene.time.now - this.battleStartTime;
        if (battleTime > this.maxBattleTime) {
            this.damage = 150; // 强制结束机制
        }

        // 更新深度值
        this.updateDepth();

        // 计算与玩家的距离
        const dx = playerPosition.x - this.sprite.x;
        const dy = playerPosition.y - this.sprite.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // 检查是否接触玩家并造成伤害
        if (distance < 80 && this.scene.player && this.scene.player.takeDamage) {
            this.scene.player.takeDamage(this.damage);

            // 攻击命中时的震屏效果
            if (this.scene.cameras && this.scene.cameras.main) {
                this.scene.cameras.main.shake(500, 0.03);
            }
        }

        // 冲刺攻击逻辑
        this.updateDashAttack(playerPosition);

        // 普通追击逻辑
        if (!this.isDashing) {
            this.normalChase(playerPosition, distance);
        }
    }

    /**
     * 冲刺攻击更新
     */
    updateDashAttack(playerPosition) {
        const currentTime = this.scene.time.now;

        // 冲刺冷却检查
        if (!this.isDashing && currentTime - this.dashCooldown > this.dashInterval) {
            this.startDashAttack(playerPosition);
        }

        // 冲刺持续时间检查
        if (this.isDashing && currentTime - this.dashStartTime > this.dashDuration) {
            this.endDashAttack();
        }
    }

    /**
     * 开始冲刺攻击
     */
    startDashAttack(playerPosition) {
        this.isDashing = true;
        this.dashStartTime = this.scene.time.now;

        console.log('[E015] 死亡冲刺！');

        // 冲刺预警效果
        if (this.sprite) {
            this.sprite.setTint(0xff0000);

            // 0.5秒后开始冲刺
            this.scene.time.delayedCall(500, () => {
                if (this.sprite && this.isDashing) {
                    this.sprite.clearTint();

                    // 计算冲刺方向
                    const dx = playerPosition.x - this.sprite.x;
                    const dy = playerPosition.y - this.sprite.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance > 0) {
                        const dirX = dx / distance;
                        const dirY = dy / distance;

                        // 设置冲刺速度
                        this.sprite.body.setVelocity(
                            dirX * this.dashSpeed * 60,
                            dirY * this.dashSpeed * 60
                        );
                    }
                }
            });
        }
    }

    /**
     * 结束冲刺攻击
     */
    endDashAttack() {
        this.isDashing = false;
        this.dashCooldown = this.scene.time.now;

        // 恢复正常速度
        if (this.sprite && this.sprite.body) {
            this.sprite.body.setVelocity(0, 0);
        }
    }

    /**
     * 普通追击行为
     */
    normalChase(playerPosition, distance) {
        if (distance > 10) {
            // 计算朝向玩家的方向
            const dx = playerPosition.x - this.sprite.x;
            const dy = playerPosition.y - this.sprite.y;

            // 标准化方向向量
            const dirX = dx / distance;
            const dirY = dy / distance;

            // 高速追击
            const targetVelocityX = dirX * this.speed / 60;
            const targetVelocityY = dirY * this.speed / 60;

            // 直接设置速度，不使用惯性（Boss更直接威胁）
            this.sprite.body.setVelocity(
                targetVelocityX * 60,
                targetVelocityY * 60
            );

            // 根据移动方向翻转精灵
            if (dx < 0) {
                this.sprite.setFlipX(true);
            } else {
                this.sprite.setFlipX(false);
            }
        }
    }

    /**
     * 销毁对象
     */
    destroy() {
        if (this.glowTween) {
            this.glowTween.stop();
            this.glowTween = null;
        }

        if (this.scaleTween) {
            this.scaleTween.stop();
            this.scaleTween = null;
        }

        super.destroy();
    }
}
