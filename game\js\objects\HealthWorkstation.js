/**
 * 生命工作台类
 * 继承通用工作台基类
 */
class HealthWorkstation extends BaseWorkstation {
    constructor(scene, x, y) {
        super(scene, x, y);
    }

    /**
     * 获取工作台静态配置
     * @returns {Object} 工作台配置信息
     */
    static getConfig() {
        return {
            id: 'health_workstation',
            name: '生命工作台',
            cost: 1,
            buffValue: 0.2, // +20%血量
            buffType: 'maxHealth',
            description: '提升生命值上限 +20%',
            spriteKey: 'health_workstation',
            spritePath: 'assets/tools/Grill_03-Sheet.png',
            petId: 'pet004',
            frameConfig: {
                frameWidth: 64,
                frameHeight: 64
            },
            scale: 3.0
        };
    }

    /**
     * 获取宠物显示配置
     */
    getPetDisplayConfig() {
        return {
            offsetX: 80,
            offsetY: 90,
            scale: 3,
            flipX: true, // 添加翻转配置
            originX: 0.5,
            originY: 0.8,
            resourceType: 'images',
            basePath: 'assets/pets/character04/big_f',
            frames: 4,
            animKey: 'pet_pet004_anim',
            frameRate: 8
        };
    }

    /**
     * 获取碰撞配置
     */
    getCollisionConfig() {
        return {
            type: 'rectangle',
            width: 140,
            height: 50,
            offsetX: 30,
            offsetY: 50
        };
    }
}
