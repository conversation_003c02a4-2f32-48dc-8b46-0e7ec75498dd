/**
 * 通用加成工具
 * 提供应用各种加成效果的函数
 */
class BuffUtils {
    /**
     * 应用加成到指定对象的属性
     * @param {Object} target - 目标对象（如玩家、武器等）
     * @param {string} property - 属性名称
     * @param {number} value - 加成值
     * @param {boolean} isMultiplier - 是否是乘法加成（true为乘法，false为加法）
     * @returns {boolean} - 是否成功应用
     */
    static applyBuff(target, property, value, isMultiplier = true) {
        if (!target || typeof target[property] !== 'number') return false;
        
        if (isMultiplier) {
            // 乘法加成（百分比增加）
            target[property] *= (1 + value);
        } else {
            // 加法加成（直接增加）
            target[property] += value;
        }
        
        return true;
    }
    
    /**
     * 应用道具效果
     * @param {Object} item - 道具对象
     * @param {Object} target - 目标对象
     * @returns {boolean} - 是否成功应用
     */
    static applyItem(item, target) {
        if (!item || !target) return false;
        
        // 如果道具有自定义应用函数，使用它
        if (typeof item.apply === 'function') {
            return item.apply(target);
        }
        
        // 否则使用通用应用逻辑
        if (item.target && item.target !== 'player') {
            // 应用到其他目标（如武器）
            if (item.target === 'weapon' && target.weaponManager && target.weaponManager.activeWeapons) {
                let applied = false;
                target.weaponManager.activeWeapons.forEach(weapon => {
                    if (this.applyBuff(weapon, item.property, item.value, item.isMultiplier)) {
                        applied = true;
                    }
                });
                return applied;
            }
            return false;
        }
        
        // 应用到玩家
        return this.applyBuff(target, item.property, item.value, item.isMultiplier);
    }
}
