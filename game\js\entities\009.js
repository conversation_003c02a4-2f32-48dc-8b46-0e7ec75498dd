/**
 * 第9分钟生成的敌人
 */

class E009 extends Enemy {
    /** 缩放比例 */
    static SCALE = 3;

    /** 碰撞箱设置 */
    static COLLISION_BOX = {
        type: 'circle',  // 圆形碰撞箱
        radius: 16,      // 半径
        offsetX: 0,      // X偏移
        offsetY: 8       // Y偏移
    };

    /**
     * 创建敌人
     */
    constructor(scene, x, y) {
        // 属性: 生命值, 伤害值, 移动速度(随机)
        const baseSpeed = 21;
        const randomSpeed = Phaser.Math.Between(baseSpeed - 5, baseSpeed + 5);
        // 将E009标记为精英敌人（10%概率）
        const isElite = Math.random() < 0.1;
        super(scene, x, y, 'e009', 500, 20, randomSpeed, isElite);

        // 如果是精英敌人，增加生命值和体型
        if (this.isElite) {
            this.health *= 3; // 精英敌人生命值是普通敌人的3倍
            this.sprite.setScale(E009.SCALE * 1.1); // 精英敌人体型是普通敌人的1.1倍

            // 添加闪烁发光效果
            this.createGlowEffect();
        }

        // 加载图片和创建动画
        this.loadAssets();
    }

    /**
     * 加载图片和创建动画
     */
    loadAssets() {
        // 动画帧图片
        const frames = [
            { key: 'e009_1', path: 'assets/images/009/009_01.png' },
            { key: 'e009_2', path: 'assets/images/009/009_02.png' },
            { key: 'e009_3', path: 'assets/images/009/009_03.png' },
            { key: 'e009_4', path: 'assets/images/009/009_04.png' }
        ];

        // 加载图片
        frames.forEach(frame => {
            this.scene.load.image(frame.key, frame.path);
        });

        // 加载完成后创建动画并播放
        this.scene.load.once('complete', () => {
            // 创建动画
            this.scene.anims.create({
                key: 'e009_anim',
                frames: frames.map(frame => ({ key: frame.key })),
                frameRate: 6,
                repeat: -1
            });

            // 设置精灵并播放动画
            if (this.sprite) {
                this.sprite.setTexture('e009_1');
                // 只为非精英敌人设置缩放，精英敌人已在构造函数中设置了更大的缩放
                if (!this.isElite) {
                    this.sprite.setScale(E009.SCALE);
                }
                this.sprite.play('e009_anim');

                // 如果是精英敌人，重新应用发光效果（防止动画覆盖）
                if (this.isElite) {
                    this.sprite.setTint(0xff00ff);
                }
            }
        });

        // 开始加载
        this.scene.load.start();
    }

    /**
     * 创建闪烁发光效果
     */
    createGlowEffect() {
        // 停止之前的发光效果（如果有）
        if (this.glowTween) {
            this.glowTween.stop();
            this.glowTween.remove();
        }
        if (this.colorCycleTween) {
            this.colorCycleTween.stop();
            this.colorCycleTween.remove();
        }
        if (this.colorCycleEvent) {
            this.colorCycleEvent.remove();
        }
        
        // 创建彩虹色循环变换效果
        const rainbowColors = [
            0x8470FF, // 红
            0x8470FF, // 红
            0xDDA0DD, // 蓝
            0xDDA0DD, // 蓝
            0xff00ff  // 紫
        ];
        
        // 创建颜色循环变换
        this.colorIndex = 0;
        
        // 添加主光晕效果（通过缩放）
        this.glowTween = this.scene.tweens.add({
            targets: this.sprite,
            scale: { from: this.sprite.scale * 1.15, to: this.sprite.scale * 0.95 },
            duration: 600,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
        
        // 添加透明度闪烁效果
        this.alphaTween = this.scene.tweens.add({
            targets: this.sprite,
            alpha: { from: 1, to: 0.7 },
            duration: 300,
            yoyo: true,
            repeat: -1,
            ease: 'Linear'
        });
        
        // 设置颜色循环计时器
        this.colorCycleEvent = this.scene.time.addEvent({
            delay: 100, // 更快的颜色变换
            callback: () => {
                // 应用当前颜色，并添加轻微的发光效果
                const currentColor = rainbowColors[this.colorIndex];
                this.sprite.setTint(currentColor);
                
                // 移动到下一个颜色
                this.colorIndex = (this.colorIndex + 1) % rainbowColors.length;
            },
            callbackScope: this,
            loop: true
        });
    }

    /**
     * 更新行为
     */
    update(playerPosition) {
        super.update(playerPosition);
    }

    /**
     * 销毁敌人时清理资源
     */
    destroy() {
        // 停止并清理所有动画效果
        if (this.glowTween) {
            this.glowTween.stop();
            this.glowTween.remove();
            this.glowTween = null;
        }
        if (this.alphaTween) {
            this.alphaTween.stop();
            this.alphaTween.remove();
            this.alphaTween = null;
        }
        if (this.colorCycleEvent) {
            this.colorCycleEvent.remove();
            this.colorCycleEvent = null;
        }
        // 调用父类销毁方法
        super.destroy();
    }
}