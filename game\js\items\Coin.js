/**
 * 金币类
 * 金币可以被玩家收集，增加玩家的金币数量
 */
class Coin {
    /** 缩放比例 */
    static SCALE = 1.5;

    /** 碰撞箱设置 */
    static COLLISION_BOX = {
        type: 'circle',  // 圆形碰撞箱
        radius: 4,      // 半径
        offsetX: 4,      // X偏移
        offsetY: 4       // Y偏移
    };

    /**
     * 创建一个新的金币
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} value - 金币价值
     */
    constructor(scene, x, y, value = 1) {
        this.scene = scene;
        this.value = value;
        this.type = 'coin';

        // 创建精灵
        this.sprite = scene.physics.add.sprite(x, y, 'coin_1');
        this.sprite.setScale(Coin.SCALE);
        this.sprite.setScrollFactor(1); // 确保金币随相机移动

        // 设置物理属性
        this.sprite.body.setCircle(
            Coin.COLLISION_BOX.radius,
            Coin.COLLISION_BOX.offsetX,
            Coin.COLLISION_BOX.offsetY
        );

        // 添加引用以便在碰撞时获取
        this.sprite.coinInstance = this;
        this.sprite.customType = 'Coin'; // 设置自定义类型

        // 使用DepthManager设置深度
        DepthManager.updateDepth(this.sprite, 'Coin', true); // 启用Y轴排序

        // 加载图片和创建动画
        this.loadAssets();
    }

    /**
     * 加载图片和创建动画
     */
    loadAssets() {
        // 检查是否已加载银币精灵表
        if (!this.scene.textures.exists('silver_coin')) {
            // 加载银币精灵表
            this.scene.load.spritesheet('silver_coin', 'assets/images/coin/MonedaP.png', {
                frameWidth: 16,  // 每帧宽度
                frameHeight: 16  // 每帧高度
            });

            // 加载完成后创建动画
            this.scene.load.once('complete', () => {
                // 创建银币动画
                if (!this.scene.anims.exists('silver_coin_spin')) {
                    this.scene.anims.create({
                        key: 'silver_coin_spin',
                        frames: this.scene.anims.generateFrameNumbers('silver_coin', { start: 0, end: 4 }),
                        frameRate: 10,
                        repeat: -1
                    });
                }

                // 设置精灵并播放动画
                if (this.sprite) {
                    this.sprite.setTexture('silver_coin');
                    this.sprite.setScale(Coin.SCALE);
                    this.sprite.play('silver_coin_spin');
                }
            });

            // 开始加载
            this.scene.load.start();
        } else {
            // 精灵表已加载，直接播放动画
            if (this.sprite) {
                this.sprite.setTexture('silver_coin');
                this.sprite.setScale(Coin.SCALE);
                this.sprite.play('silver_coin_spin');
            }
        }
    }

    /**
     * 收集金币
     */
    collect() {
        if (!this.sprite || !this.sprite.active) return;

        // 触发金币收集事件
        this.scene.events.emit('coinCollected', this.value);

        // 销毁金币对象
        this.destroy();
    }

    /**
     * 销毁金币
     */
    destroy() {
        if (this.sprite) {
            this.sprite.destroy();
            this.sprite = null;
        }
    }
}