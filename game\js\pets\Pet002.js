/**
 * 宠物002
 */
class Pet002 extends Pet {
    /** 缩放比例 */
    static SCALE = 0.5;

    /** 宠物间距 */
    static SPACING = 35;

    /**
     * 创建草
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {Player|Pet} target - 跟随目标（玩家或其他宠物）
     * @param {Object} config - 配置参数
     */
    constructor(scene, target, config = {}) {
        // 调用父类构造函数，传入基本配置
        super(scene, target, {
            ...config,
            id: 'pet002',
            name: '草',
            scale: Pet002.SCALE
            // 不设置速度，使用玩家速度
        });
    }

    /**
     * 加载宠物资源和创建精灵
     * @param {number} x - 初始X坐标
     * @param {number} y - 初始Y坐标
     */
    loadAssets(x, y) {
        // 检查第一帧纹理是否已加载
        if (!this.scene.textures.exists('pet_pet002_1')) {
            // 如果纹理未加载，尝试加载所有帧
            console.log('草纹理未加载，尝试加载');

            // 加载8帧图片
            for (let i = 1; i <= 8; i++) {
                const key = `pet_pet002_${i}`;
                const path = `assets/pets/character02/slm_0${i}.png`;
                this.scene.load.image(key, path);
            }

            // 加载完成后创建动画和精灵
            this.scene.load.once('complete', () => {
                // 创建动画
                if (!this.scene.anims.exists('pet002_anim')) {
                    this.scene.anims.create({
                        key: 'pet002_anim',
                        frames: Array.from({ length: 8 }, (_, i) => ({
                            key: `pet_pet002_${i + 1}`
                        })),
                        frameRate: 10,
                        repeat: -1
                    });
                }

                this.createPetSprite(x, y);
            });

            // 开始加载
            this.scene.load.start();
        } else {
            // 纹理已加载，直接创建精灵
            this.createPetSprite(x, y);
        }
    }

    /**
     * 创建宠物精灵
     * @param {number} x - 初始X坐标
     * @param {number} y - 初始Y坐标
     */
    createPetSprite(x, y) {
        // 创建精灵，使用第一帧（不使用物理碰撞）
        this.sprite = this.scene.add.sprite(x, y, 'pet_pet002_1');
        this.sprite.setOrigin(0.5, 0.65); // 纹理位置数值
        console.log('创建草');

        // 播放动画
        if (this.scene.anims.exists('pet002_anim')) {
            this.sprite.play('pet002_anim');
            console.log('播放草动画');
        }

        // 设置深度和缩放
        if (this.sprite) {
            this.sprite.customType = 'Pet';

            if (typeof DepthManager !== 'undefined') {
                DepthManager.updateDepth(this.sprite, 'Pet', true);
            }

            this.sprite.setScale(this.scale);
        }

        // 初始化宠物武器
        this.weapon = new C1(this.scene, this);
    }

}
