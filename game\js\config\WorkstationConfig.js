/**
 * 工作台配置文件
 * 定义所有工作台的类型、属性和升级配置
 */
const WorkstationConfig = {
    // 工作台类型配置
    types: {
        health_station: {
            name: '生命工作台',
            icon: '❤️',
            buffType: 'maxHealth',
            description: '提升生命值上限',
            texture: 'axe' // 暂时使用axe纹理，后续可替换
        },
        regen_station: {
            name: '回复工作台', 
            icon: '💚',
            buffType: 'healthRegen',
            description: '提升生命回复速度',
            texture: 'axe'
        },
        attack_station: {
            name: '攻击工作台',
            icon: '⚔️',
            buffType: 'attackPower',
            description: '提升攻击力',
            texture: 'axe'
        },
        range_station: {
            name: '范围工作台',
            icon: '🎯',
            buffType: 'attackRange',
            description: '提升攻击范围',
            texture: 'axe'
        }
    },

    // 品质等级配置
    qualities: {
        1: { 
            name: '普通', 
            color: 0x95a5a6, 
            buffValue: 0.10, 
            upgradeCost: 0,
            glowAlpha: 0.3
        },
        2: { 
            name: '优秀', 
            color: 0x2ecc71, 
            buffValue: 0.15, 
            upgradeCost: 200,
            glowAlpha: 0.4
        },
        3: { 
            name: '稀有', 
            color: 0x3498db, 
            buffValue: 0.20, 
            upgradeCost: 500,
            glowAlpha: 0.5
        },
        4: { 
            name: '史诗', 
            color: 0x9b59b6, 
            buffValue: 0.25, 
            upgradeCost: 1000,
            glowAlpha: 0.6
        },
        5: { 
            name: '传说', 
            color: 0xe67e22, 
            buffValue: 0.30, 
            upgradeCost: 2000,
            glowAlpha: 0.7
        }
    },

    // 宠物与工作台绑定关系
    petBindings: {
        'pet001': 'health_station',    // 猫咪 -> 生命工作台
        'pet002': 'regen_station',     // 草 -> 回复工作台
        'pet003': 'attack_station',    // 人 -> 攻击工作台
        'pet004': 'range_station'      // 呲毛 -> 范围工作台
    },

    // 宠物图标配置
    petIcons: {
        'pet001': '🐱',
        'pet002': '🌿',
        'pet003': '👨‍🦰',
        'pet004': '👹'
    },

    // 工作台在家园中的位置配置
    positions: [
        { x: 400, y: 300 },   // 第一个工作台位置
        { x: 600, y: 300 },   // 第二个工作台位置
        { x: 400, y: 450 },   // 第三个工作台位置
        { x: 600, y: 450 }    // 第四个工作台位置
    ]
};

// 导出配置
window.WorkstationConfig = WorkstationConfig;
