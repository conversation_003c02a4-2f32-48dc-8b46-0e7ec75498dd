/**
 * 玩家角色类
 * 使用player_boy文件夹下的图片创建动画
 */
class Player {
    // 玩家尺寸配置
    static SIZE = {
        width: 420,
        height: 420
    };

    // 玩家碰撞箱配置 - 使用圆形碰撞箱
    static COLLISION_BOX = {
        type: 'circle',  // 圆形碰撞箱
        radius: 7,      // 半径
        offsetX: 43.5,      // X偏移
        offsetY: 42       // Y偏移
    };

    /**
     * 创建一个新的玩家角色
     * @param {Phaser.Scene} scene - 游戏场景
     * @param {number} x - 初始X坐标
     * @param {number} y - 初始Y坐标
     */
    constructor(scene, x, y) {
        this.scene = scene;

        // 创建玩家精灵
        this.createSprite(x, y);

        // 玩家移动速度
        this.moveSpeed = 130;

        // 物品拾取相关参数
        this.attractRadius = 50;     // 物品吸引范围（原itemCollectRadius）
        this.collectRadius = 20;      // 物品自动收集距离（原autoCollectDistance）
        this.attractSpeed = 200;      // 物品被吸引时的基础速度
        this.attractAcceleration = 1; // 物品接近玩家时的加速系数

        // 等级系统
        this.level = 0; // 初始等级为0
        this.experience = 0;

        // 战斗属性
        this.attackPower = 0; // 攻击力，初始为0
        this.maxHealth = 100; // 生命上限
        this.currentHealth = 100; // 当前生命值
        this.healthRegen = 0; // 生命恢复
        this.defense = 0; // 防御

        // 伤害冷却系统
        this.lastDamageTime = 0;       // 上次受到伤害的时间
        this.damageInterval = 1000;    // 伤害间隔（毫秒）

        // 死亡状态标志
        this.isDead = false;           // 是否已死亡

        // 宠物系统
        this.petManager = null; // 由场景创建后设置

        // 位置历史数组 - 用于宠物跟随
        this.positionHistory = [];
        this.maxHistoryLength = 200; // 保存最近60帧的位置
        this.lastRecordedPosition = null; // 上一次记录的位置

        // 保存实例引用，便于在输入管理器中访问
        this.sprite.playerInstance = this;

        // 创建武器管理器
        this.weaponManager = new WeaponManager(scene, this);

        // 初始化历史位置数组
        this.initializePositionHistory();

        // 创建血条 - 灰色背景条（底层）
        this.healthBarBg = this.scene.add.rectangle(x, y - 50, 40, 6, 0x666666).setDepth(450);
        // 创建血条 - 红色血量条（上层）
        this.healthBar = this.scene.add.rectangle(x, y - 50, 40, 6, 0xff0000).setDepth(450);
        // 创建血量数字文本
        this.healthText = this.scene.add.text(x, y - 65, `${this.currentHealth}/${this.maxHealth}`, {
            fontSize: '12px',
            fill: '#ffffff',
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5).setDepth(450);

        console.log(`玩家角色创建在位置(${x}, ${y})`);
    }

    /**
     * 创建玩家精灵
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     */
    createSprite(x, y) {
        // 创建精灵，使用第一帧作为初始纹理
        this.sprite = this.scene.physics.add.sprite(x, y, 'player_boy_1');

        // 设置精灵尺寸
        this.sprite.setDisplaySize(Player.SIZE.width, Player.SIZE.height);

        // 设置物理属性
        this.sprite.setCollideWorldBounds(false); // 默认不受世界边界限制
        this.sprite.setBounce(0);
        this.sprite.body.setDrag(0);
        this.sprite.body.setFriction(0);

        // 设置自定义类型，用于深度管理
        this.sprite.customType = 'Player';

        // 设置深度，确保玩家显示在其他对象上方
        if (typeof DepthManager !== 'undefined') {
            DepthManager.updateDepth(this.sprite, 'Player', true);
        } else {
            this.sprite.setDepth(300); // 使用与DepthManager.LAYERS.CHARACTERS相同的值
        }

        // 应用碰撞箱设置
        this.applyCollisionBox();

        // 创建动画（如果不存在）
        if (!this.scene.anims.exists('player_anim')) {
            this.scene.anims.create({
                key: 'player_anim',
                frames: [
                    { key: 'player_boy_1' },
                    { key: 'player_boy_2' },
                    { key: 'player_boy_3' },
                    { key: 'player_boy_4' }
                ],
                frameRate: 8,
                repeat: -1
            });
        }

        // 播放动画
        this.sprite.play('player_anim');
    }

    /**
     * 应用碰撞箱设置
     */
    applyCollisionBox() {
        if (!this.sprite || !this.sprite.body) return;

        const collisionBox = Player.COLLISION_BOX;

        if (collisionBox.type === 'circle') {
            // 设置圆形碰撞箱
            this.sprite.body.setCircle(collisionBox.radius);
            this.sprite.body.setOffset(collisionBox.offsetX, collisionBox.offsetY);
            console.log(`应用玩家碰撞箱: 类型=圆形, 半径=${collisionBox.radius}`);
        } else if (collisionBox.type === 'rectangle') {
            // 设置矩形碰撞箱
            this.sprite.body.setSize(collisionBox.width, collisionBox.height);
            this.sprite.body.setOffset(collisionBox.offsetX, collisionBox.offsetY);
            console.log(`应用玩家碰撞箱: 类型=矩形, 尺寸=${collisionBox.width}x${collisionBox.height}`);
        }
    }

    /**
     * 获取玩家X坐标
     */
    get x() {
        return this.sprite ? this.sprite.x : 0;
    }

    /**
     * 获取玩家Y坐标
     */
    get y() {
        return this.sprite ? this.sprite.y : 0;
    }

    /**
     * 处理玩家移动
     * @param {Object} moveInput - 包含x和y方向移动值的对象
     */
    move(moveInput) {
        if (!this.sprite) return;

        // 标准化移动向量，确保对角线移动不会更快
        if (moveInput.x !== 0 || moveInput.y !== 0) {
            const length = Math.sqrt(moveInput.x * moveInput.x + moveInput.y * moveInput.y);
            moveInput.x = moveInput.x / length;
            moveInput.y = moveInput.y / length;

            // 根据移动方向设置角色朝向
            if (moveInput.x < 0) {
                // 向左移动，翻转精灵
                this.sprite.setFlipX(true);
            } else if (moveInput.x > 0) {
                // 向右移动，恢复正常方向
                this.sprite.setFlipX(false);
            }

            // 应用移动速度
            this.sprite.setVelocity(
                moveInput.x * this.moveSpeed,
                moveInput.y * this.moveSpeed
            );
        } else {
            // 如果没有输入，停止移动
            this.sprite.setVelocity(0, 0);
        }
    }

    /**
     * 更新玩家状态
     * @param {number} time - 当前时间
     */
    update(time) {
        if (!this.sprite) return;

        // 记录当前位置到历史数组
        this.recordPosition();

        // 更新武器
        if (this.weaponManager) {
            this.weaponManager.update(this.scene.time.now);
        }

        // 更新宠物管理器
        if (this.petManager) {
            this.petManager.update();
        }

        // 更新血条位置跟随玩家
        this.healthBarBg.x = this.sprite.x;
        this.healthBarBg.y = this.sprite.y - 50;
        this.healthBar.x = this.sprite.x;
        this.healthBar.y = this.sprite.y - 50;

        // 更新血量文本位置
        if (this.healthText) {
            this.healthText.x = this.sprite.x;
            this.healthText.y = this.sprite.y - 65;
        }
    }

    /**
     * 初始化历史位置数组
     * 精确模拟玩家从左向右移动，确保虚拟历史与实际历史一致
     */
    initializePositionHistory() {
        // 清空现有历史
        this.positionHistory = [];

        // 获取当前位置
        const currentPos = {
            x: this.sprite.x,
            y: this.sprite.y,
            flipX: false  // 假设初始朝向是向右
        };

        // 计算最大需要的历史位置数量（基于最大宠物延迟）
        // 假设最多有5个宠物，最大延迟值为20 + 4 * 20 = 100
        const maxDelay = 200;

        // 计算每帧移动的距离（基于玩家移动速度）
        // moveSpeed是每秒移动的像素数，假设游戏运行在60FPS
        const pixelsPerFrame = this.moveSpeed / 60;

        // 模拟玩家从左向右移动
        let simulatedX = currentPos.x - (pixelsPerFrame * maxDelay);
        let simulatedY = currentPos.y;

        // 记录初始位置
        let lastRecordedX = simulatedX;
        let lastRecordedY = simulatedY;

        // 模拟足够多的帧，确保能填充所需的历史位置
        for (let frame = 0; frame < maxDelay * 2; frame++) {
            // 模拟玩家向右移动
            simulatedX += pixelsPerFrame;

            // 检查位置是否发生足够的变化（与recordPosition逻辑一致）
            if (Math.abs(simulatedX - lastRecordedX) >= 1 ||
                Math.abs(simulatedY - lastRecordedY) >= 1) {

                // 记录新位置
                this.positionHistory.unshift({
                    x: simulatedX,
                    y: simulatedY,
                    flipX: false
                });

                // 更新最后记录的位置
                lastRecordedX = simulatedX;
                lastRecordedY = simulatedY;

                // 如果已经记录了足够多的位置，停止模拟
                if (this.positionHistory.length >= maxDelay) {
                    break;
                }
            }
        }

        // 确保历史数组长度不超过maxHistoryLength
        if (this.positionHistory.length > this.maxHistoryLength) {
            this.positionHistory = this.positionHistory.slice(0, this.maxHistoryLength);
        }

        // 设置最后记录的位置
        this.lastRecordedPosition = {
            x: lastRecordedX,
            y: lastRecordedY,
            flipX: false
        };
    }

    /**
     * 记录当前位置到历史数组
     */
    recordPosition() {
        if (!this.sprite) return;

        // 获取当前位置
        const currentPosition = {
            x: this.sprite.x,
            y: this.sprite.y,
            flipX: this.sprite.flipX // 记录朝向
        };

        // 检查位置是否发生变化
        if (!this.lastRecordedPosition ||
            currentPosition.x !== this.lastRecordedPosition.x ||
            currentPosition.y !== this.lastRecordedPosition.y) {

            // 添加当前位置到历史数组开头
            this.positionHistory.unshift(currentPosition);

            // 更新上一次记录的位置
            this.lastRecordedPosition = { ...currentPosition };

            // 限制历史数组长度
            if (this.positionHistory.length > this.maxHistoryLength) {
                this.positionHistory.pop();
            }
        }
    }

    /**
     * 获取指定步数之前的历史位置
     * @param {number} steps - 历史步数
     * @returns {Object|null} 历史位置对象，包含x、y坐标和朝向
     */
    getHistoryPosition(steps) {
        // 确保步数在有效范围内
        if (steps < 0 || steps >= this.positionHistory.length) {
            // 如果步数无效，返回当前位置
            return this.positionHistory[0] || {
                x: this.sprite.x,
                y: this.sprite.y,
                flipX: this.sprite.flipX
            };
        }

        // 返回指定步数的历史位置
        return this.positionHistory[steps];
    }

    /**
     * 设置宠物管理器
     * @param {PetManager} manager - 宠物管理器
     */
    setPetManager(manager) {
        this.petManager = manager;
    }

    /**
     * 处理物品收集
     * @param {Array} items - 场景中的物品数组
     */
    updateItemCollection(items) {
        if (!this.sprite) return;

        const playerPos = { x: this.sprite.x, y: this.sprite.y };

        for (let i = items.length - 1; i >= 0; i--) {
            const item = items[i];

            // 如果物品不存在或已被销毁，跳过
            if (!item || !item.sprite || !item.sprite.active) continue;

            // 计算与玩家的距离
            const distance = Phaser.Math.Distance.Between(
                item.sprite.x, item.sprite.y,
                playerPos.x, playerPos.y
            );

            // 如果玩家靠近物品，则吸引物品
            if (distance < this.attractRadius) {
                // 计算方向向量
                const dirX = playerPos.x - item.sprite.x;
                const dirY = playerPos.y - item.sprite.y;

                // 标准化方向向量
                const length = Math.sqrt(dirX * dirX + dirY * dirY);
                const normDirX = dirX / length;
                const normDirY = dirY / length;

                // 设置速度（距离越近速度越快）
                const speed = this.attractSpeed * (1 + this.attractAcceleration *
                    (this.attractRadius - Math.min(distance, this.attractRadius)) / this.attractRadius);

                item.sprite.setVelocity(
                    normDirX * speed,
                    normDirY * speed
                );

                // 如果非常接近玩家，自动收集
                if (distance < this.collectRadius) {
                    item.collect();
                }
            }
        }
    }

    /**
     * 计算升级所需经验
     * @param {number} level - 等级
     * @returns {number} 所需经验值
     */
    static getRequiredExperience(level) {
        return 5 * Math.pow(level, 2);
    }

    /**
     * 增加经验并检查升级
     * @param {number} value - 增加的经验值
     * @returns {boolean} 是否升级
     */
    addExperience(value) {
        this.experience += value;
        return this.checkLevelUp();
    }

    /**
     * 检查是否升级
     * @returns {boolean} 是否升级
     */
    checkLevelUp() {
        let levelChanged = false;
        let newLevel = 0;

        // 计算当前经验值对应的等级
        while (this.experience >= Player.getRequiredExperience(newLevel + 1)) {
            newLevel++;
        }

        // 如果等级提升
        if (newLevel > this.level) {
            levelChanged = true;
            this.level = newLevel;
        }

        return levelChanged;
    }

    /**
     * 受到伤害
     * @param {number} amount - 伤害量
     * @returns {boolean} 是否成功造成伤害
     */
    takeDamage(amount) {
        // 如果已经死亡，不再处理伤害
        if (this.isDead) {
            return false;
        }

        // 获取当前时间
        const currentTime = this.scene.time.now;

        // 检查是否在伤害冷却期内
        if (currentTime - this.lastDamageTime < this.damageInterval) {
            return false; // 在冷却期内，不造成伤害
        }

        // 更新上次受到伤害的时间
        this.lastDamageTime = currentTime;

        // 计算实际伤害（考虑防御）
        const actualDamage = Math.max(1, amount - this.defense);

        // 扣除生命值
        this.currentHealth -= actualDamage;

        // 确保生命值不低于0
        if (this.currentHealth < 0) {
            this.currentHealth = 0;
        }

        // 更新血条显示
        this.updateHealthBar();

        console.log(`玩家受到${actualDamage}点伤害，当前血量：${this.currentHealth}/${this.maxHealth}`);

        // 检查是否死亡
        if (this.currentHealth <= 0) {
            this.onPlayerDeath();
        }

        return true; // 成功造成伤害
    }

    /**
     * 恢复血量
     * @param {number} amount - 恢复量
     */
    healHealth(amount) {
        if (this.isDead) return;

        this.currentHealth += amount;
        if (this.currentHealth > this.maxHealth) {
            this.currentHealth = this.maxHealth;
        }

        this.updateHealthBar();
        console.log(`玩家恢复${amount}点血量，当前血量：${this.currentHealth}/${this.maxHealth}`);
    }

    /**
     * 更新血条显示
     */
    updateHealthBar() {
        if (!this.healthBar) return;

        // 计算血量百分比
        const healthPercent = this.currentHealth / this.maxHealth;

        // 更新血条宽度
        this.healthBar.displayWidth = 40 * healthPercent;

        // 更新血量数字文本
        if (this.healthText) {
            this.healthText.setText(`${Math.round(this.currentHealth)}/${Math.round(this.maxHealth)}`);
        }
    }

    /**
     * 玩家死亡处理
     */
    onPlayerDeath() {
        // 如果已经死亡，不重复处理
        if (this.isDead) {
            return;
        }

        // 设置死亡状态
        this.isDead = true;

        console.log('玩家死亡！');

        // 暂停游戏
        this.scene.physics.pause();

        // 显示游戏结束文字
        const gameOverText = this.scene.add.text(
            this.scene.cameras.main.centerX,
            this.scene.cameras.main.centerY,
            '游戏结束',
            { font: 'bold 48px Arial', fill: '#ff0000' }
        ).setOrigin(0.5).setScrollFactor(0).setDepth(1000);

        // 通知Scene处理玩家死亡（保存数据等）
        if (this.scene.onPlayerDeath) {
            this.scene.onPlayerDeath();
        }

        // 2秒后返回主菜单
        this.scene.time.delayedCall(2000, () => {
            this.scene.scene.start('MainMenuScene');
        });
    }

    /**
     * 设置是否启用世界边界碰撞
     * @param {boolean} enable - 是否启用碰撞
     */
    setWorldCollision(enable) {
        if (this.sprite) {
            this.sprite.setCollideWorldBounds(enable);
            console.log(`玩家世界边界碰撞: ${enable ? '启用' : '禁用'}`);
        }
    }
}